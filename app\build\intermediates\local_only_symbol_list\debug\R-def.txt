R_DEF: Internal format may change without notice
local
color black
color blue_200
color blue_500
color blue_700
color teal_200
color teal_700
color white
dimen fab_margin
drawable ic_launcher_background
drawable ic_launcher_foreground
id FirstFragment
id SecondFragment
id action_FirstFragment_to_SecondFragment
id action_SecondFragment_to_FirstFragment
id action_settings
id btn_add_category
id btn_all_tasks
id btn_back
id btn_calendar_today
id btn_delete_task
id btn_edit
id btn_mark_complete
id btn_next_month
id btn_personal_tasks
id btn_prev_month
id btn_search
id btn_work_tasks
id fab
id fab_add_task
id logo
id logo_calendar
id nav_calendar
id nav_calendar_active
id nav_graph
id nav_host_fragment_content_main
id nav_profile
id nav_profile_calendar
id nav_tasks
id nav_tasks_calendar
id recycler_calendar_tasks
id recycler_tasks
id toolbar
id tv_calendar_task_category
id tv_calendar_task_time
id tv_calendar_task_title
id tv_detail_category
id tv_detail_deadline
id tv_detail_description
id tv_detail_status
id tv_detail_title
id tv_month_year
id tv_task_category
id tv_task_deadline
id tv_task_description
id tv_task_title
id tv_today
id view_calendar_task_status
id view_detail_status
id view_status_indicator
layout activity_main
layout content_main
layout fragment_first
layout fragment_second
layout fragment_task_detail
layout item_calendar_task
layout item_task
menu menu_main
mipmap ic_launcher
mipmap ic_launcher_round
navigation nav_graph
string action_settings
string add_category
string add_task
string app_name
string category_all
string category_personal
string category_work
string complete_task
string delete_task
string edit
string first_fragment_label
string hello_first_fragment
string hello_second_fragment
string nav_calendar
string nav_profile
string nav_tasks
string next
string previous
string search
string second_fragment_label
string status_completed
string status_in_progress
string status_pending
string task_detail
string task_manager_title
style Theme.MyApplication
style Theme.MyApplication.AppBarOverlay
style Theme.MyApplication.NoActionBar
style Theme.MyApplication.PopupOverlay
xml backup_rules
xml data_extraction_rules

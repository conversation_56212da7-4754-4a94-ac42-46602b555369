#Wed Jun 25 15:25:22 ICT 2025
com.example.myapplication.app-main-6\:/mipmap-xxxhdpi/ic_launcher_round.webp=C\:\\Users\\srv_training\\AndroidStudioProjects\\MyApplication\\app\\build\\intermediates\\packaged_res\\debug\\mipmap-xxxhdpi-v4\\ic_launcher_round.webp
com.example.myapplication.app-main-6\:/navigation/nav_graph.xml=C\:\\Users\\srv_training\\AndroidStudioProjects\\MyApplication\\app\\build\\intermediates\\packaged_res\\debug\\navigation\\nav_graph.xml
com.example.myapplication.app-main-6\:/mipmap-anydpi-v26/ic_launcher.xml=C\:\\Users\\srv_training\\AndroidStudioProjects\\MyApplication\\app\\build\\intermediates\\packaged_res\\debug\\mipmap-anydpi-v26\\ic_launcher.xml
com.example.myapplication.app-packageDebugResources-3\:/layout/fragment_first.xml=C\:\\Users\\srv_training\\AndroidStudioProjects\\MyApplication\\app\\build\\intermediates\\packaged_res\\debug\\layout\\fragment_first.xml
com.example.myapplication.app-main-6\:/mipmap-mdpi/ic_launcher.webp=C\:\\Users\\srv_training\\AndroidStudioProjects\\MyApplication\\app\\build\\intermediates\\packaged_res\\debug\\mipmap-mdpi-v4\\ic_launcher.webp
com.example.myapplication.app-packageDebugResources-3\:/layout/item_task.xml=C\:\\Users\\srv_training\\AndroidStudioProjects\\MyApplication\\app\\build\\intermediates\\packaged_res\\debug\\layout\\item_task.xml
com.example.myapplication.app-main-6\:/mipmap-xxhdpi/ic_launcher.webp=C\:\\Users\\srv_training\\AndroidStudioProjects\\MyApplication\\app\\build\\intermediates\\packaged_res\\debug\\mipmap-xxhdpi-v4\\ic_launcher.webp
com.example.myapplication.app-main-6\:/menu/menu_main.xml=C\:\\Users\\srv_training\\AndroidStudioProjects\\MyApplication\\app\\build\\intermediates\\packaged_res\\debug\\menu\\menu_main.xml
com.example.myapplication.app-packageDebugResources-3\:/layout/activity_main.xml=C\:\\Users\\srv_training\\AndroidStudioProjects\\MyApplication\\app\\build\\intermediates\\packaged_res\\debug\\layout\\activity_main.xml
com.example.myapplication.app-main-6\:/xml/backup_rules.xml=C\:\\Users\\srv_training\\AndroidStudioProjects\\MyApplication\\app\\build\\intermediates\\packaged_res\\debug\\xml\\backup_rules.xml
com.example.myapplication.app-main-6\:/drawable-v24/ic_launcher_foreground.xml=C\:\\Users\\srv_training\\AndroidStudioProjects\\MyApplication\\app\\build\\intermediates\\packaged_res\\debug\\drawable-v24\\ic_launcher_foreground.xml
com.example.myapplication.app-main-6\:/drawable/ic_launcher_background.xml=C\:\\Users\\srv_training\\AndroidStudioProjects\\MyApplication\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_launcher_background.xml
com.example.myapplication.app-main-6\:/mipmap-xhdpi/ic_launcher_round.webp=C\:\\Users\\srv_training\\AndroidStudioProjects\\MyApplication\\app\\build\\intermediates\\packaged_res\\debug\\mipmap-xhdpi-v4\\ic_launcher_round.webp
com.example.myapplication.app-packageDebugResources-3\:/layout/fragment_task_detail.xml=C\:\\Users\\srv_training\\AndroidStudioProjects\\MyApplication\\app\\build\\intermediates\\packaged_res\\debug\\layout\\fragment_task_detail.xml
com.example.myapplication.app-packageDebugResources-3\:/layout/fragment_second.xml=C\:\\Users\\srv_training\\AndroidStudioProjects\\MyApplication\\app\\build\\intermediates\\packaged_res\\debug\\layout\\fragment_second.xml
com.example.myapplication.app-main-6\:/mipmap-xhdpi/ic_launcher.webp=C\:\\Users\\srv_training\\AndroidStudioProjects\\MyApplication\\app\\build\\intermediates\\packaged_res\\debug\\mipmap-xhdpi-v4\\ic_launcher.webp
com.example.myapplication.app-packageDebugResources-3\:/layout/content_main.xml=C\:\\Users\\srv_training\\AndroidStudioProjects\\MyApplication\\app\\build\\intermediates\\packaged_res\\debug\\layout\\content_main.xml
com.example.myapplication.app-main-6\:/mipmap-hdpi/ic_launcher_round.webp=C\:\\Users\\srv_training\\AndroidStudioProjects\\MyApplication\\app\\build\\intermediates\\packaged_res\\debug\\mipmap-hdpi-v4\\ic_launcher_round.webp
com.example.myapplication.app-main-6\:/mipmap-xxxhdpi/ic_launcher.webp=C\:\\Users\\srv_training\\AndroidStudioProjects\\MyApplication\\app\\build\\intermediates\\packaged_res\\debug\\mipmap-xxxhdpi-v4\\ic_launcher.webp
com.example.myapplication.app-packageDebugResources-3\:/layout/item_calendar_task.xml=C\:\\Users\\srv_training\\AndroidStudioProjects\\MyApplication\\app\\build\\intermediates\\packaged_res\\debug\\layout\\item_calendar_task.xml
com.example.myapplication.app-main-6\:/mipmap-anydpi-v26/ic_launcher_round.xml=C\:\\Users\\srv_training\\AndroidStudioProjects\\MyApplication\\app\\build\\intermediates\\packaged_res\\debug\\mipmap-anydpi-v26\\ic_launcher_round.xml
com.example.myapplication.app-main-6\:/xml/data_extraction_rules.xml=C\:\\Users\\srv_training\\AndroidStudioProjects\\MyApplication\\app\\build\\intermediates\\packaged_res\\debug\\xml\\data_extraction_rules.xml
com.example.myapplication.app-main-6\:/mipmap-mdpi/ic_launcher_round.webp=C\:\\Users\\srv_training\\AndroidStudioProjects\\MyApplication\\app\\build\\intermediates\\packaged_res\\debug\\mipmap-mdpi-v4\\ic_launcher_round.webp
com.example.myapplication.app-main-6\:/mipmap-xxhdpi/ic_launcher_round.webp=C\:\\Users\\srv_training\\AndroidStudioProjects\\MyApplication\\app\\build\\intermediates\\packaged_res\\debug\\mipmap-xxhdpi-v4\\ic_launcher_round.webp
com.example.myapplication.app-main-6\:/mipmap-hdpi/ic_launcher.webp=C\:\\Users\\srv_training\\AndroidStudioProjects\\MyApplication\\app\\build\\intermediates\\packaged_res\\debug\\mipmap-hdpi-v4\\ic_launcher.webp

package com.example.myapplication;

import android.content.Context;
import android.graphics.Color;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;

public class LocalDataManager {
    
    private Context context;
    
    public LocalDataManager(Context context) {
        this.context = context;
    }
    
    /**
     * Đọc tasks từ file JSON trong assets
     */
    public List<Task> loadTasksFromAssets() {
        List<Task> tasks = new ArrayList<>();
        
        try {
            // Đọc file JSON từ assets
            String jsonString = loadJSONFromAsset("tasks.json");
            
            // Parse JSON
            JSONArray jsonArray = new JSONArray(jsonString);
            
            for (int i = 0; i < jsonArray.length(); i++) {
                JSONObject taskJson = jsonArray.getJSONObject(i);
                Task task = parseTaskFromJson(taskJson);
                tasks.add(task);
            }
            
        } catch (JSONException e) {
            e.printStackTrace();
            // Fallback to hardcoded data if JSON parsing fails
            return getHardcodedTasks();
        }
        
        return tasks;
    }
    
    /**
     * Đọc file từ assets folder
     */
    private String loadJSONFromAsset(String filename) {
        String json = null;
        try {
            InputStream is = context.getAssets().open(filename);
            int size = is.available();
            byte[] buffer = new byte[size];
            is.read(buffer);
            is.close();
            json = new String(buffer, "UTF-8");
        } catch (IOException ex) {
            ex.printStackTrace();
            return null;
        }
        return json;
    }
    
    /**
     * Parse JSON object thành Task
     */
    private Task parseTaskFromJson(JSONObject taskJson) throws JSONException {
        String id = taskJson.getString("id");
        String title = taskJson.getString("title");
        String category = taskJson.getString("category");
        String description = taskJson.getString("description");
        String deadline = taskJson.getString("deadline");
        String status = taskJson.getString("status");
        
        // Convert status to color
        int statusColor = getStatusColor(status);
        
        Task task = new Task(title, category, description, deadline, status, statusColor);
        task.setId(id);
        return task;
    }
    
    /**
     * Lấy màu theo status
     */
    private int getStatusColor(String status) {
        switch (status) {
            case "Hoàn thành":
                return Color.parseColor("#4CAF50"); // Green
            case "Đang thực hiện":
                return Color.parseColor("#FF9800"); // Orange
            default:
                return Color.parseColor("#F44336"); // Red
        }
    }
    
    /**
     * Dữ liệu cứng làm fallback
     */
    private List<Task> getHardcodedTasks() {
        List<Task> tasks = new ArrayList<>();
        
        Task task1 = new Task(
            "Hoàn thành báo cáo tháng",
            "Công việc",
            "Viết báo cáo tổng kết công việc tháng 12 và gửi cho quản lý",
            "28/12/2024",
            "Đang thực hiện",
            Color.parseColor("#FF9800")
        );
        task1.setId("1");
        tasks.add(task1);
        
        Task task2 = new Task(
            "Mua quà Giáng sinh",
            "Cá nhân",
            "Đi mua quà Giáng sinh cho gia đình và bạn bè",
            "24/12/2024",
            "Chưa bắt đầu",
            Color.parseColor("#F44336")
        );
        task2.setId("2");
        tasks.add(task2);
        
        Task task3 = new Task(
            "Học tiếng Anh",
            "Cá nhân",
            "Ôn tập từ vựng và ngữ pháp tiếng Anh, làm bài tập",
            "30/12/2024",
            "Đang thực hiện",
            Color.parseColor("#2196F3")
        );
        task3.setId("3");
        tasks.add(task3);
        
        Task task4 = new Task(
            "Họp team dự án",
            "Công việc",
            "Họp với team để thảo luận tiến độ dự án và phân công công việc",
            "26/12/2024",
            "Hoàn thành",
            Color.parseColor("#4CAF50")
        );
        task4.setId("4");
        tasks.add(task4);
        
        Task task5 = new Task(
            "Tập thể dục",
            "Cá nhân",
            "Đi gym và tập cardio 45 phút",
            "25/12/2024",
            "Đang thực hiện",
            Color.parseColor("#2196F3")
        );
        task5.setId("5");
        tasks.add(task5);
        
        return tasks;
    }
    
    /**
     * Lưu task mới vào SharedPreferences (giả lập database)
     */
    public void saveNewTask(Task task) {
        // Có thể implement lưu vào SharedPreferences hoặc SQLite
        // Ở đây chỉ là ví dụ
    }
    
    /**
     * Cập nhật task
     */
    public void updateTask(Task task) {
        // Implement update logic
    }
    
    /**
     * Xóa task
     */
    public void deleteTask(String taskId) {
        // Implement delete logic
    }
}

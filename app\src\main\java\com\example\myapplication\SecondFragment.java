package com.example.myapplication;

import android.graphics.Color;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.fragment.app.Fragment;
import androidx.navigation.fragment.NavHostFragment;
import androidx.recyclerview.widget.LinearLayoutManager;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.List;
import java.util.Locale;

import com.example.myapplication.databinding.FragmentSecondBinding;

public class SecondFragment extends Fragment {

    private FragmentSecondBinding binding;
    private CalendarTaskAdapter calendarTaskAdapter;
    private List<CalendarTask> todayTasks;
    private Calendar currentCalendar;
    private SimpleDateFormat monthYearFormat;
    private SimpleDateFormat dateFormat;

    @Override
    public View onCreateView(
            LayoutInflater inflater, ViewGroup container,
            Bundle savedInstanceState
    ) {

        binding = FragmentSecondBinding.inflate(inflater, container, false);
        return binding.getRoot();

    }

    public void onViewCreated(@NonNull View view, Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);

        // Hide ActionBar for this fragment
        if (getActivity() != null && ((MainActivity) getActivity()).getSupportActionBar() != null) {
            ((MainActivity) getActivity()).getSupportActionBar().hide();
        }

        // Initialize calendar
        initializeCalendar();

        // Setup RecyclerView for today's tasks
        setupCalendarTasksRecyclerView();

        // Load today's tasks
        loadTodayTasks();

        // Setup click listeners
        setupClickListeners();

        // Update calendar display
        updateCalendarDisplay();
    }

    private void setupCalendarTasksRecyclerView() {
        todayTasks = new ArrayList<>();
        calendarTaskAdapter = new CalendarTaskAdapter(todayTasks);

        binding.recyclerCalendarTasks.setLayoutManager(new LinearLayoutManager(getContext()));
        binding.recyclerCalendarTasks.setAdapter(calendarTaskAdapter);
    }

    private void loadTodayTasks() {
        // Dữ liệu ảo cho các task hôm nay
        todayTasks.add(new CalendarTask(
            "09:00",
            "Tập thể dục",
            "Cá nhân",
            Color.parseColor("#2196F3")
        ));

        todayTasks.add(new CalendarTask(
            "10:30",
            "Họp team dự án",
            "Công việc",
            Color.parseColor("#4CAF50")
        ));

        todayTasks.add(new CalendarTask(
            "14:00",
            "Mua quà Giáng sinh",
            "Cá nhân",
            Color.parseColor("#F44336")
        ));

        todayTasks.add(new CalendarTask(
            "16:30",
            "Review code",
            "Công việc",
            Color.parseColor("#FF9800")
        ));

        calendarTaskAdapter.notifyDataSetChanged();
    }

    private void setupClickListeners() {
        // Calendar navigation buttons
        binding.btnPrevMonth.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                currentCalendar.add(Calendar.MONTH, -1);
                updateCalendarDisplay();
                loadTasksForSelectedDate();
            }
        });

        binding.btnNextMonth.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                currentCalendar.add(Calendar.MONTH, 1);
                updateCalendarDisplay();
                loadTasksForSelectedDate();
            }
        });

        binding.btnCalendarToday.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                currentCalendar = Calendar.getInstance(); // Reset to today
                updateCalendarDisplay();
                loadTasksForSelectedDate();
                Toast.makeText(getContext(), "Về hôm nay", Toast.LENGTH_SHORT).show();
            }
        });

        // Bottom navigation
        binding.navTasksCalendar.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                NavHostFragment.findNavController(SecondFragment.this)
                        .navigate(R.id.action_SecondFragment_to_FirstFragment);
            }
        });

        binding.navProfileCalendar.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                Toast.makeText(getContext(), "Chuyển đến trang Của tôi", Toast.LENGTH_SHORT).show();
            }
        });
    }

    private void initializeCalendar() {
        currentCalendar = Calendar.getInstance();
        monthYearFormat = new SimpleDateFormat("MMMM yyyy", new Locale("vi", "VN"));
        dateFormat = new SimpleDateFormat("dd/MM/yyyy", Locale.getDefault());
    }

    private void updateCalendarDisplay() {
        // Update month/year display
        String monthYear = monthYearFormat.format(currentCalendar.getTime());
        // Capitalize first letter
        monthYear = monthYear.substring(0, 1).toUpperCase() + monthYear.substring(1);
        binding.tvMonthYear.setText(monthYear);

        // Update selected date display
        String selectedDate = dateFormat.format(currentCalendar.getTime());
        // Update the task section title
        updateTaskSectionTitle(selectedDate);
    }

    private void updateTaskSectionTitle(String date) {
        // Update the task section title with selected date
        Calendar today = Calendar.getInstance();
        String todayStr = dateFormat.format(today.getTime());

        if (date.equals(todayStr)) {
            binding.tvSelectedDateTasks.setText("Nhiệm vụ hôm nay (" + date + ")");
        } else {
            binding.tvSelectedDateTasks.setText("Nhiệm vụ ngày " + date);
        }
    }

    private void loadTasksForSelectedDate() {
        // Clear current tasks
        todayTasks.clear();

        // Get current date
        String currentDate = dateFormat.format(currentCalendar.getTime());

        // Load tasks based on selected date
        // For demo, we'll show different tasks for different dates
        Calendar today = Calendar.getInstance();
        String todayStr = dateFormat.format(today.getTime());

        if (currentDate.equals(todayStr)) {
            // Today's tasks
            loadTodayTasks();
        } else {
            // Sample tasks for other dates
            todayTasks.add(new CalendarTask(
                "10:00",
                "Cuộc họp quan trọng",
                "Công việc",
                Color.parseColor("#FF9800")
            ));

            todayTasks.add(new CalendarTask(
                "15:30",
                "Đi mua sắm",
                "Cá nhân",
                Color.parseColor("#2196F3")
            ));
        }

        calendarTaskAdapter.notifyDataSetChanged();
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        binding = null;
    }

}
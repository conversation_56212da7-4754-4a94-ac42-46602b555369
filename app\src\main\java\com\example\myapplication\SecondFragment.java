package com.example.myapplication;

import android.graphics.Color;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.fragment.app.Fragment;
import androidx.navigation.fragment.NavHostFragment;
import androidx.recyclerview.widget.LinearLayoutManager;

import java.util.ArrayList;
import java.util.List;

import com.example.myapplication.databinding.FragmentSecondBinding;

public class SecondFragment extends Fragment {

    private FragmentSecondBinding binding;
    private CalendarTaskAdapter calendarTaskAdapter;
    private List<CalendarTask> todayTasks;

    @Override
    public View onCreateView(
            LayoutInflater inflater, ViewGroup container,
            Bundle savedInstanceState
    ) {

        binding = FragmentSecondBinding.inflate(inflater, container, false);
        return binding.getRoot();

    }

    public void onViewCreated(@NonNull View view, Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);

        // Hide ActionBar for this fragment
        if (getActivity() != null && ((MainActivity) getActivity()).getSupportActionBar() != null) {
            ((MainActivity) getActivity()).getSupportActionBar().hide();
        }

        // Setup RecyclerView for today's tasks
        setupCalendarTasksRecyclerView();

        // Load today's tasks
        loadTodayTasks();

        // Setup click listeners
        setupClickListeners();
    }

    private void setupCalendarTasksRecyclerView() {
        todayTasks = new ArrayList<>();
        calendarTaskAdapter = new CalendarTaskAdapter(todayTasks);

        binding.recyclerCalendarTasks.setLayoutManager(new LinearLayoutManager(getContext()));
        binding.recyclerCalendarTasks.setAdapter(calendarTaskAdapter);
    }

    private void loadTodayTasks() {
        // Dữ liệu ảo cho các task hôm nay
        todayTasks.add(new CalendarTask(
            "09:00",
            "Tập thể dục",
            "Cá nhân",
            Color.parseColor("#2196F3")
        ));

        todayTasks.add(new CalendarTask(
            "10:30",
            "Họp team dự án",
            "Công việc",
            Color.parseColor("#4CAF50")
        ));

        todayTasks.add(new CalendarTask(
            "14:00",
            "Mua quà Giáng sinh",
            "Cá nhân",
            Color.parseColor("#F44336")
        ));

        todayTasks.add(new CalendarTask(
            "16:30",
            "Review code",
            "Công việc",
            Color.parseColor("#FF9800")
        ));

        calendarTaskAdapter.notifyDataSetChanged();
    }

    private void setupClickListeners() {
        // Calendar navigation buttons
        binding.btnPrevMonth.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                Toast.makeText(getContext(), "Tháng trước", Toast.LENGTH_SHORT).show();
            }
        });

        binding.btnNextMonth.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                Toast.makeText(getContext(), "Tháng sau", Toast.LENGTH_SHORT).show();
            }
        });

        binding.btnCalendarToday.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                Toast.makeText(getContext(), "Hôm nay", Toast.LENGTH_SHORT).show();
            }
        });

        // Bottom navigation
        binding.navTasksCalendar.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                NavHostFragment.findNavController(SecondFragment.this)
                        .navigate(R.id.action_SecondFragment_to_FirstFragment);
            }
        });

        binding.navProfileCalendar.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                Toast.makeText(getContext(), "Chuyển đến trang Của tôi", Toast.LENGTH_SHORT).show();
            }
        });
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        binding = null;
    }

}
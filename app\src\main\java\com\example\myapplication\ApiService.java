package com.example.myapplication;

import android.os.AsyncTask;
import android.util.Log;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.ArrayList;
import java.util.List;

public class ApiService {
    
    private static final String TAG = "ApiService";
    private static final String BASE_URL = "https://your-api-url.com/api/"; // Thay bằng URL API thật
    
    public interface ApiCallback<T> {
        void onSuccess(T result);
        void onError(String error);
    }
    
    // Fetch tasks from API
    public static void getTasks(ApiCallback<List<Task>> callback) {
        new GetTasksTask(callback).execute(BASE_URL + "tasks");
    }
    
    // Add new task to API
    public static void addTask(Task task, ApiCallback<Task> callback) {
        new AddTaskTask(callback, task).execute(BASE_URL + "tasks");
    }
    
    // Update task via API
    public static void updateTask(Task task, ApiCallback<Task> callback) {
        new UpdateTaskTask(callback, task).execute(BASE_URL + "tasks/" + task.getId());
    }
    
    // Delete task via API
    public static void deleteTask(String taskId, ApiCallback<Boolean> callback) {
        new DeleteTaskTask(callback).execute(BASE_URL + "tasks/" + taskId);
    }
    
    // AsyncTask to get tasks
    private static class GetTasksTask extends AsyncTask<String, Void, String> {
        private ApiCallback<List<Task>> callback;
        private Exception exception;
        
        GetTasksTask(ApiCallback<List<Task>> callback) {
            this.callback = callback;
        }
        
        @Override
        protected String doInBackground(String... urls) {
            try {
                return makeHttpRequest(urls[0], "GET", null);
            } catch (Exception e) {
                this.exception = e;
                return null;
            }
        }
        
        @Override
        protected void onPostExecute(String result) {
            if (exception != null) {
                callback.onError("Network error: " + exception.getMessage());
                return;
            }
            
            try {
                List<Task> tasks = parseTasksFromJson(result);
                callback.onSuccess(tasks);
            } catch (JSONException e) {
                callback.onError("JSON parsing error: " + e.getMessage());
            }
        }
    }
    
    // AsyncTask to add task
    private static class AddTaskTask extends AsyncTask<String, Void, String> {
        private ApiCallback<Task> callback;
        private Task task;
        private Exception exception;
        
        AddTaskTask(ApiCallback<Task> callback, Task task) {
            this.callback = callback;
            this.task = task;
        }
        
        @Override
        protected String doInBackground(String... urls) {
            try {
                String jsonData = taskToJson(task);
                return makeHttpRequest(urls[0], "POST", jsonData);
            } catch (Exception e) {
                this.exception = e;
                return null;
            }
        }
        
        @Override
        protected void onPostExecute(String result) {
            if (exception != null) {
                callback.onError("Network error: " + exception.getMessage());
                return;
            }
            
            try {
                Task createdTask = parseTaskFromJson(result);
                callback.onSuccess(createdTask);
            } catch (JSONException e) {
                callback.onError("JSON parsing error: " + e.getMessage());
            }
        }
    }
    
    // AsyncTask to update task
    private static class UpdateTaskTask extends AsyncTask<String, Void, String> {
        private ApiCallback<Task> callback;
        private Task task;
        private Exception exception;
        
        UpdateTaskTask(ApiCallback<Task> callback, Task task) {
            this.callback = callback;
            this.task = task;
        }
        
        @Override
        protected String doInBackground(String... urls) {
            try {
                String jsonData = taskToJson(task);
                return makeHttpRequest(urls[0], "PUT", jsonData);
            } catch (Exception e) {
                this.exception = e;
                return null;
            }
        }
        
        @Override
        protected void onPostExecute(String result) {
            if (exception != null) {
                callback.onError("Network error: " + exception.getMessage());
                return;
            }
            
            try {
                Task updatedTask = parseTaskFromJson(result);
                callback.onSuccess(updatedTask);
            } catch (JSONException e) {
                callback.onError("JSON parsing error: " + e.getMessage());
            }
        }
    }
    
    // AsyncTask to delete task
    private static class DeleteTaskTask extends AsyncTask<String, Void, String> {
        private ApiCallback<Boolean> callback;
        private Exception exception;
        
        DeleteTaskTask(ApiCallback<Boolean> callback) {
            this.callback = callback;
        }
        
        @Override
        protected String doInBackground(String... urls) {
            try {
                makeHttpRequest(urls[0], "DELETE", null);
                return "success";
            } catch (Exception e) {
                this.exception = e;
                return null;
            }
        }
        
        @Override
        protected void onPostExecute(String result) {
            if (exception != null) {
                callback.onError("Network error: " + exception.getMessage());
            } else {
                callback.onSuccess(true);
            }
        }
    }
    
    // Helper method to make HTTP requests
    private static String makeHttpRequest(String urlString, String method, String jsonData) throws IOException {
        URL url = new URL(urlString);
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();
        
        try {
            connection.setRequestMethod(method);
            connection.setRequestProperty("Content-Type", "application/json");
            connection.setRequestProperty("Accept", "application/json");
            connection.setConnectTimeout(10000);
            connection.setReadTimeout(10000);
            
            if (jsonData != null && (method.equals("POST") || method.equals("PUT"))) {
                connection.setDoOutput(true);
                connection.getOutputStream().write(jsonData.getBytes("UTF-8"));
            }
            
            int responseCode = connection.getResponseCode();
            InputStream inputStream;
            
            if (responseCode >= 200 && responseCode < 300) {
                inputStream = connection.getInputStream();
            } else {
                inputStream = connection.getErrorStream();
            }
            
            BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream));
            StringBuilder response = new StringBuilder();
            String line;
            
            while ((line = reader.readLine()) != null) {
                response.append(line);
            }
            
            reader.close();
            
            if (responseCode >= 200 && responseCode < 300) {
                return response.toString();
            } else {
                throw new IOException("HTTP error code: " + responseCode + ", Response: " + response.toString());
            }
            
        } finally {
            connection.disconnect();
        }
    }
    
    // Parse JSON array to List<Task>
    private static List<Task> parseTasksFromJson(String jsonString) throws JSONException {
        List<Task> tasks = new ArrayList<>();
        JSONArray jsonArray = new JSONArray(jsonString);
        
        for (int i = 0; i < jsonArray.length(); i++) {
            JSONObject taskJson = jsonArray.getJSONObject(i);
            Task task = parseTaskFromJsonObject(taskJson);
            tasks.add(task);
        }
        
        return tasks;
    }
    
    // Parse single JSON object to Task
    private static Task parseTaskFromJson(String jsonString) throws JSONException {
        JSONObject taskJson = new JSONObject(jsonString);
        return parseTaskFromJsonObject(taskJson);
    }
    
    // Helper to parse JSONObject to Task
    private static Task parseTaskFromJsonObject(JSONObject taskJson) throws JSONException {
        String id = taskJson.optString("id", "");
        String title = taskJson.getString("title");
        String category = taskJson.getString("category");
        String description = taskJson.optString("description", "");
        String deadline = taskJson.optString("deadline", "");
        String status = taskJson.optString("status", "Chưa bắt đầu");
        
        // Convert status to color
        int statusColor = getStatusColor(status);
        
        Task task = new Task(title, category, description, deadline, status, statusColor);
        task.setId(id);
        return task;
    }
    
    // Convert Task to JSON string
    private static String taskToJson(Task task) throws JSONException {
        JSONObject jsonObject = new JSONObject();
        
        if (task.getId() != null && !task.getId().isEmpty()) {
            jsonObject.put("id", task.getId());
        }
        jsonObject.put("title", task.getTitle());
        jsonObject.put("category", task.getCategory());
        jsonObject.put("description", task.getDescription());
        jsonObject.put("deadline", task.getDeadline());
        jsonObject.put("status", task.getStatus());
        
        return jsonObject.toString();
    }
    
    // Helper to get status color
    private static int getStatusColor(String status) {
        switch (status) {
            case "Hoàn thành":
                return android.graphics.Color.parseColor("#4CAF50"); // Green
            case "Đang thực hiện":
                return android.graphics.Color.parseColor("#FF9800"); // Orange
            default:
                return android.graphics.Color.parseColor("#F44336"); // Red
        }
    }
}

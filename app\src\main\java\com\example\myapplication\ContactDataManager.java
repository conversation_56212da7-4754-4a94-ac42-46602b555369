package com.example.myapplication;

import android.content.ContentResolver;
import android.content.Context;
import android.database.Cursor;
import android.provider.ContactsContract;
import android.util.Log;
import java.util.ArrayList;
import java.util.List;

/**
 * <PERSON><PERSON> dụ sử dụng ContentResolver để đọc danh bạ
 * và tạo task từ thông tin contact
 */
public class ContactDataManager {
    
    private static final String TAG = "ContactDataManager";
    private Context context;
    
    public ContactDataManager(Context context) {
        this.context = context;
    }
    
    /**
     * Đọ<PERSON> danh bạ và tạo task "Gọi điện cho..."
     */
    public List<Task> createTasksFromContacts() {
        List<Task> tasks = new ArrayList<>();
        ContentResolver resolver = context.getContentResolver();
        
        // Query để lấy contacts có số điện thoại
        String[] projection = {
            ContactsContract.CommonDataKinds.Phone.DISPLAY_NAME,
            ContactsContract.CommonDataKinds.Phone.NUMBER,
            ContactsContract.CommonDataKinds.Phone.CONTACT_ID
        };
        
        Cursor cursor = resolver.query(
            ContactsContract.CommonDataKinds.Phone.CONTENT_URI,
            projection,
            null,
            null,
            ContactsContract.CommonDataKinds.Phone.DISPLAY_NAME + " ASC"
        );
        
        if (cursor != null && cursor.moveToFirst()) {
            int nameIndex = cursor.getColumnIndex(ContactsContract.CommonDataKinds.Phone.DISPLAY_NAME);
            int phoneIndex = cursor.getColumnIndex(ContactsContract.CommonDataKinds.Phone.NUMBER);
            int idIndex = cursor.getColumnIndex(ContactsContract.CommonDataKinds.Phone.CONTACT_ID);
            
            do {
                String name = cursor.getString(nameIndex);
                String phone = cursor.getString(phoneIndex);
                String contactId = cursor.getString(idIndex);
                
                // Tạo task từ contact
                Task contactTask = new Task(
                    "Gọi điện cho " + name,
                    "Cá nhân",
                    "Liên hệ với " + name + " qua số " + phone,
                    getCurrentDate(),
                    "Chưa bắt đầu",
                    android.graphics.Color.parseColor("#2196F3")
                );
                contactTask.setId("contact_" + contactId);
                
                tasks.add(contactTask);
                
                // Giới hạn 5 contact để không quá nhiều
                if (tasks.size() >= 5) {
                    break;
                }
                
            } while (cursor.moveToNext());
            
            cursor.close();
        }
        
        Log.d(TAG, "Created " + tasks.size() + " tasks from contacts");
        return tasks;
    }
    
    /**
     * Đọc tin nhắn gần đây và tạo task "Trả lời tin nhắn..."
     */
    public List<Task> createTasksFromSMS() {
        List<Task> tasks = new ArrayList<>();
        ContentResolver resolver = context.getContentResolver();
        
        try {
            // Query để lấy SMS inbox
            String[] projection = {"address", "body", "date"};
            
            Cursor cursor = resolver.query(
                android.net.Uri.parse("content://sms/inbox"),
                projection,
                null,
                null,
                "date DESC LIMIT 3"
            );
            
            if (cursor != null && cursor.moveToFirst()) {
                int addressIndex = cursor.getColumnIndex("address");
                int bodyIndex = cursor.getColumnIndex("body");
                int dateIndex = cursor.getColumnIndex("date");
                
                do {
                    String address = cursor.getString(addressIndex);
                    String body = cursor.getString(bodyIndex);
                    long date = cursor.getLong(dateIndex);
                    
                    // Tạo task từ SMS
                    Task smsTask = new Task(
                        "Trả lời tin nhắn từ " + address,
                        "Cá nhân",
                        "Nội dung: " + (body.length() > 50 ? body.substring(0, 50) + "..." : body),
                        getCurrentDate(),
                        "Chưa bắt đầu",
                        android.graphics.Color.parseColor("#FF9800")
                    );
                    smsTask.setId("sms_" + date);
                    
                    tasks.add(smsTask);
                    
                } while (cursor.moveToNext());
                
                cursor.close();
            }
            
        } catch (Exception e) {
            Log.e(TAG, "Error reading SMS: " + e.getMessage());
        }
        
        return tasks;
    }
    
    /**
     * Đọc ảnh từ gallery và tạo task "Chỉnh sửa ảnh..."
     */
    public List<Task> createTasksFromPhotos() {
        List<Task> tasks = new ArrayList<>();
        ContentResolver resolver = context.getContentResolver();
        
        String[] projection = {
            android.provider.MediaStore.Images.Media._ID,
            android.provider.MediaStore.Images.Media.DISPLAY_NAME,
            android.provider.MediaStore.Images.Media.DATE_ADDED
        };
        
        Cursor cursor = resolver.query(
            android.provider.MediaStore.Images.Media.EXTERNAL_CONTENT_URI,
            projection,
            null,
            null,
            android.provider.MediaStore.Images.Media.DATE_ADDED + " DESC LIMIT 3"
        );
        
        if (cursor != null && cursor.moveToFirst()) {
            int idIndex = cursor.getColumnIndex(android.provider.MediaStore.Images.Media._ID);
            int nameIndex = cursor.getColumnIndex(android.provider.MediaStore.Images.Media.DISPLAY_NAME);
            int dateIndex = cursor.getColumnIndex(android.provider.MediaStore.Images.Media.DATE_ADDED);
            
            do {
                String id = cursor.getString(idIndex);
                String name = cursor.getString(nameIndex);
                long dateAdded = cursor.getLong(dateIndex);
                
                // Tạo task từ ảnh
                Task photoTask = new Task(
                    "Chỉnh sửa ảnh " + name,
                    "Cá nhân",
                    "Chỉnh sửa và chia sẻ ảnh " + name,
                    getCurrentDate(),
                    "Chưa bắt đầu",
                    android.graphics.Color.parseColor("#4CAF50")
                );
                photoTask.setId("photo_" + id);
                
                tasks.add(photoTask);
                
            } while (cursor.moveToNext());
            
            cursor.close();
        }
        
        return tasks;
    }
    
    /**
     * Lấy ngày hiện tại
     */
    private String getCurrentDate() {
        java.text.SimpleDateFormat sdf = new java.text.SimpleDateFormat("dd/MM/yyyy", java.util.Locale.getDefault());
        return sdf.format(new java.util.Date());
    }
    
    /**
     * Kiểm tra permission trước khi đọc dữ liệu
     */
    public boolean hasContactPermission() {
        return android.content.pm.PackageManager.PERMISSION_GRANTED == 
            androidx.core.content.ContextCompat.checkSelfPermission(context, 
                android.Manifest.permission.READ_CONTACTS);
    }
    
    public boolean hasSMSPermission() {
        return android.content.pm.PackageManager.PERMISSION_GRANTED == 
            androidx.core.content.ContextCompat.checkSelfPermission(context, 
                android.Manifest.permission.READ_SMS);
    }
    
    public boolean hasStoragePermission() {
        return android.content.pm.PackageManager.PERMISSION_GRANTED == 
            androidx.core.content.ContextCompat.checkSelfPermission(context, 
                android.Manifest.permission.READ_EXTERNAL_STORAGE);
    }
}

package com.example.myapplication;

import android.content.Context;
import android.database.Cursor;
import android.provider.ContactsContract;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.CursorAdapter;
import android.widget.TextView;

/**
 * Ví dụ CursorAdapter để hiển thị danh bạ
 * Sử dụng khi bạn muốn hiển thị dữ liệu trực tiếp từ Cursor
 */
public class ContactCursorAdapter extends CursorAdapter {
    
    private LayoutInflater inflater;
    
    public ContactCursorAdapter(Context context, Cursor cursor) {
        super(context, cursor, 0);
        inflater = LayoutInflater.from(context);
    }
    
    /**
     * Tạo view mới cho mỗi item
     */
    @Override
    public View newView(Context context, Cursor cursor, ViewGroup parent) {
        // Inflate layout cho item contact
        return inflater.inflate(R.layout.item_contact, parent, false);
    }
    
    /**
     * Bind dữ liệu từ cursor vào view
     */
    @Override
    public void bindView(View view, Context context, Cursor cursor) {
        // Lấy views
        TextView nameText = view.findViewById(R.id.tv_contact_name);
        TextView phoneText = view.findViewById(R.id.tv_contact_phone);
        TextView actionText = view.findViewById(R.id.tv_contact_action);
        
        // Lấy dữ liệu từ cursor
        int nameIndex = cursor.getColumnIndex(ContactsContract.CommonDataKinds.Phone.DISPLAY_NAME);
        int phoneIndex = cursor.getColumnIndex(ContactsContract.CommonDataKinds.Phone.NUMBER);
        
        String name = cursor.getString(nameIndex);
        String phone = cursor.getString(phoneIndex);
        
        // Set dữ liệu vào views
        nameText.setText(name != null ? name : "Không có tên");
        phoneText.setText(phone != null ? phone : "Không có số");
        actionText.setText("Tạo task gọi điện");
        
        // Set click listener để tạo task
        view.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                createTaskFromContact(context, name, phone);
            }
        });
    }
    
    /**
     * Tạo task từ thông tin contact
     */
    private void createTaskFromContact(Context context, String name, String phone) {
        // Tạo task mới
        Task contactTask = new Task(
            "Gọi điện cho " + name,
            "Cá nhân",
            "Liên hệ với " + name + " qua số " + phone,
            getCurrentDate(),
            "Chưa bắt đầu",
            android.graphics.Color.parseColor("#2196F3")
        );
        
        // Thông báo đã tạo task
        android.widget.Toast.makeText(context, 
            "Đã tạo task: Gọi điện cho " + name, 
            android.widget.Toast.LENGTH_SHORT).show();
        
        // Ở đây bạn có thể thêm logic để save task vào database
        // hoặc gửi về Fragment để cập nhật danh sách
    }
    
    /**
     * Lấy ngày hiện tại
     */
    private String getCurrentDate() {
        java.text.SimpleDateFormat sdf = new java.text.SimpleDateFormat("dd/MM/yyyy", java.util.Locale.getDefault());
        return sdf.format(new java.util.Date());
    }
}

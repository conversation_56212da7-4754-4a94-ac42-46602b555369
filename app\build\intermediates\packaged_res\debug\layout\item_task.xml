<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="8dp"
    android:background="@android:color/white"
    android:elevation="4dp"
    android:clickable="true"
    android:foreground="?android:attr/selectableItemBackground"
    android:orientation="vertical"
    android:padding="16dp">



        <!-- Header v<PERSON><PERSON> ti<PERSON>u đề và chủ đề -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="8dp">

            <TextView
                android:id="@+id/tv_task_title"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="Tiêu đề task"
                android:textSize="18sp"
                android:textStyle="bold"
                android:textColor="#212121" />

            <TextView
                android:id="@+id/tv_task_category"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Cá nhân"
                android:textSize="12sp"
                android:textColor="@android:color/white"
                android:background="#2196F3"
                android:paddingHorizontal="8dp"
                android:paddingVertical="4dp"
                android:layout_marginStart="8dp" />

        </LinearLayout>

        <!-- Mô tả -->
        <TextView
            android:id="@+id/tv_task_description"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Mô tả chi tiết của task..."
            android:textSize="14sp"
            android:textColor="#757575"
            android:layout_marginBottom="8dp"
            android:maxLines="2"
            android:ellipsize="end" />

        <!-- Deadline -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical">

            <ImageView
                android:layout_width="16dp"
                android:layout_height="16dp"
                android:src="@android:drawable/ic_menu_recent_history"
                android:tint="#FF5722" />

            <TextView
                android:id="@+id/tv_task_deadline"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="25/12/2024"
                android:textSize="12sp"
                android:textColor="#FF5722"
                android:layout_marginStart="4dp" />

            <View
                android:layout_width="0dp"
                android:layout_height="1dp"
                android:layout_weight="1" />

            <!-- Status indicator -->
            <View
                android:id="@+id/view_status_indicator"
                android:layout_width="12dp"
                android:layout_height="12dp"
                android:background="#4CAF50" />

        </LinearLayout>

</LinearLayout>

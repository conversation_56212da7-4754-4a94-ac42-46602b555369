<?xml version="1.0" encoding="utf-8"?>
<resources>
    <color name="black">#FF000000</color>
    <color name="purple_200">#FFBB86FC</color>
    <color name="purple_500">#FF6200EE</color>
    <color name="purple_700">#FF3700B3</color>
    <color name="teal_200">#FF03DAC5</color>
    <color name="teal_700">#FF018786</color>
    <color name="white">#FFFFFFFF</color>
    <dimen name="fab_margin">16dp</dimen>
    <string name="action_settings">Settings</string>
    <string name="add_category">+ Thêm chủ đề</string>
    <string name="add_task">Thêm task mới</string>
    <string name="app_name">My Application</string>
    <string name="category_all">Tất cả</string>
    <string name="category_personal">Cá nhân</string>
    <string name="category_work">Công việc</string>
    <string name="complete_task"><PERSON><PERSON><PERSON> thành</string>
    <string name="delete_task">Xóa</string>
    <string name="edit">Chỉnh sửa</string>
    <string name="first_fragment_label">First Fragment</string>
    <string name="hello_first_fragment">Hello first fragment</string>
    <string name="hello_second_fragment">Hello second fragment. Arg: %1$s</string>
    <string name="nav_calendar">Lịch</string>
    <string name="nav_profile">Của tôi</string>
    <string name="nav_tasks">Nhiệm Vụ</string>
    <string name="next">Next</string>
    <string name="previous">Previous</string>
    <string name="search">Tìm kiếm</string>
    <string name="second_fragment_label">Second Fragment</string>
    <string name="status_completed">Hoàn thành</string>
    <string name="status_in_progress">Đang thực hiện</string>
    <string name="status_pending">Chờ xử lý</string>
    <string name="task_detail">Chi tiết Task</string>
    <string name="task_manager_title">Task Manager</string>
    <style name="Theme.MyApplication" parent="Theme.MaterialComponents.DayNight.DarkActionBar">
        
        <item name="colorPrimary">@color/purple_500</item>
        <item name="colorPrimaryVariant">@color/purple_700</item>
        <item name="colorOnPrimary">@color/white</item>
        
        <item name="colorSecondary">@color/teal_200</item>
        <item name="colorSecondaryVariant">@color/teal_700</item>
        <item name="colorOnSecondary">@color/black</item>
        
        <item name="android:statusBarColor">?attr/colorPrimaryVariant</item>
        
    </style>
    <style name="Theme.MyApplication.AppBarOverlay" parent="ThemeOverlay.AppCompat.Dark.ActionBar"/>
    <style name="Theme.MyApplication.NoActionBar">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
    </style>
    <style name="Theme.MyApplication.PopupOverlay" parent="ThemeOverlay.AppCompat.Light"/>
</resources>
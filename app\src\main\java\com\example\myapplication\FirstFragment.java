package com.example.myapplication;

import android.app.AlertDialog;
import android.app.DatePickerDialog;
import android.graphics.Color;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ArrayAdapter;
import android.widget.DatePicker;
import android.widget.EditText;
import android.widget.Spinner;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.fragment.app.Fragment;
import androidx.navigation.fragment.NavHostFragment;
import androidx.recyclerview.widget.LinearLayoutManager;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.List;
import java.util.Locale;

import com.example.myapplication.databinding.FragmentFirstBinding;

public class FirstFragment extends Fragment {

    private FragmentFirstBinding binding;
    private TaskAdapter taskAdapter;
    private List<Task> taskList;
    private List<Task> allTasks;

    @Override
    public View onCreateView(
            LayoutInflater inflater, ViewGroup container,
            Bundle savedInstanceState
    ) {

        binding = FragmentFirstBinding.inflate(inflater, container, false);
        return binding.getRoot();

    }

    public void onViewCreated(@NonNull View view, Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);

        // Setup RecyclerView
        setupRecyclerView();

        // Load dummy data
        loadDummyData();

        // Xử lý click nút tìm kiếm
        binding.btnSearch.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                Toast.makeText(getContext(), "Tìm kiếm task", Toast.LENGTH_SHORT).show();
            }
        });

        // Xử lý click các nút chọn chủ đề
        binding.btnAllTasks.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                filterTasks("Tất cả");
                Toast.makeText(getContext(), "Hiển thị tất cả task", Toast.LENGTH_SHORT).show();
            }
        });

        binding.btnPersonalTasks.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                filterTasks("Cá nhân");
                Toast.makeText(getContext(), "Hiển thị task cá nhân", Toast.LENGTH_SHORT).show();
            }
        });

        binding.btnWorkTasks.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                filterTasks("Công việc");
                Toast.makeText(getContext(), "Hiển thị task công việc", Toast.LENGTH_SHORT).show();
            }
        });

        binding.btnAddCategory.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                Toast.makeText(getContext(), "Thêm chủ đề mới", Toast.LENGTH_SHORT).show();
            }
        });

        // Xử lý click nút thêm task
        binding.fabAddTask.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                showAddTaskDialog();
            }
        });

        // Xử lý click navigation
        binding.navTasks.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                // Đã ở trang Tasks rồi, không cần làm gì
            }
        });

        binding.navCalendar.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                NavHostFragment.findNavController(FirstFragment.this)
                        .navigate(R.id.action_FirstFragment_to_SecondFragment);
            }
        });

        binding.navProfile.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                Toast.makeText(getContext(), "Chuyển đến trang Của tôi", Toast.LENGTH_SHORT).show();
            }
        });
    }

    private void setupRecyclerView() {
        taskList = new ArrayList<>();
        taskAdapter = new TaskAdapter(taskList);

        binding.recyclerTasks.setLayoutManager(new LinearLayoutManager(getContext()));
        binding.recyclerTasks.setAdapter(taskAdapter);

        // Set click listener for task items
        taskAdapter.setOnTaskClickListener(new TaskAdapter.OnTaskClickListener() {
            @Override
            public void onTaskClick(Task task) {
                Toast.makeText(getContext(), "Clicked: " + task.getTitle(), Toast.LENGTH_SHORT).show();
                // TODO: Navigate to task detail
            }
        });
    }

    private void loadDummyData() {
        allTasks = new ArrayList<>();

        // Thêm dữ liệu ảo
        allTasks.add(new Task(
            "Hoàn thành báo cáo tháng",
            "Công việc",
            "Viết báo cáo tổng kết công việc tháng 12 và gửi cho quản lý",
            "28/12/2024",
            "Đang thực hiện",
            Color.parseColor("#FF9800")
        ));

        allTasks.add(new Task(
            "Mua quà Giáng sinh",
            "Cá nhân",
            "Đi mua quà Giáng sinh cho gia đình và bạn bè",
            "24/12/2024",
            "Chưa bắt đầu",
            Color.parseColor("#F44336")
        ));

        allTasks.add(new Task(
            "Học tiếng Anh",
            "Cá nhân",
            "Ôn tập từ vựng và ngữ pháp tiếng Anh, làm bài tập",
            "30/12/2024",
            "Đang thực hiện",
            Color.parseColor("#2196F3")
        ));

        allTasks.add(new Task(
            "Họp team dự án",
            "Công việc",
            "Họp với team để thảo luận tiến độ dự án và phân công công việc",
            "26/12/2024",
            "Hoàn thành",
            Color.parseColor("#4CAF50")
        ));

        allTasks.add(new Task(
            "Tập thể dục",
            "Cá nhân",
            "Đi gym và tập cardio 45 phút",
            "25/12/2024",
            "Đang thực hiện",
            Color.parseColor("#2196F3")
        ));

        allTasks.add(new Task(
            "Review code",
            "Công việc",
            "Review code của các thành viên trong team và đưa feedback",
            "27/12/2024",
            "Chưa bắt đầu",
            Color.parseColor("#F44336")
        ));

        // Hiển thị tất cả task ban đầu
        taskList.clear();
        taskList.addAll(allTasks);
        taskAdapter.notifyDataSetChanged();
    }

    private void filterTasks(String category) {
        List<Task> filteredTasks = new ArrayList<>();

        if (category.equals("Tất cả")) {
            filteredTasks.addAll(allTasks);
        } else {
            for (Task task : allTasks) {
                if (task.getCategory().equals(category)) {
                    filteredTasks.add(task);
                }
            }
        }

        taskAdapter.updateTasks(filteredTasks);
    }

    private void showAddTaskDialog() {
        AlertDialog.Builder builder = new AlertDialog.Builder(getContext());
        View dialogView = getLayoutInflater().inflate(R.layout.dialog_add_task, null);
        builder.setView(dialogView);

        AlertDialog dialog = builder.create();

        // Get views from dialog
        EditText etTitle = dialogView.findViewById(R.id.et_task_title);
        EditText etDescription = dialogView.findViewById(R.id.et_task_description);
        Spinner spinnerCategory = dialogView.findViewById(R.id.spinner_category);
        android.widget.Button btnSelectDate = dialogView.findViewById(R.id.btn_select_date);
        android.widget.Button btnCancel = dialogView.findViewById(R.id.btn_cancel);
        android.widget.Button btnSave = dialogView.findViewById(R.id.btn_save);

        // Setup category spinner
        String[] categories = {"Cá nhân", "Công việc"};
        ArrayAdapter<String> adapter = new ArrayAdapter<>(getContext(),
            android.R.layout.simple_spinner_item, categories);
        adapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
        spinnerCategory.setAdapter(adapter);

        // Date selection
        final String[] selectedDate = {""};
        btnSelectDate.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Calendar calendar = Calendar.getInstance();
                DatePickerDialog datePickerDialog = new DatePickerDialog(
                    getContext(),
                    new DatePickerDialog.OnDateSetListener() {
                        @Override
                        public void onDateSet(DatePicker view, int year, int month, int dayOfMonth) {
                            Calendar selectedCalendar = Calendar.getInstance();
                            selectedCalendar.set(year, month, dayOfMonth);
                            SimpleDateFormat sdf = new SimpleDateFormat("dd/MM/yyyy", Locale.getDefault());
                            selectedDate[0] = sdf.format(selectedCalendar.getTime());
                            btnSelectDate.setText(selectedDate[0]);
                        }
                    },
                    calendar.get(Calendar.YEAR),
                    calendar.get(Calendar.MONTH),
                    calendar.get(Calendar.DAY_OF_MONTH)
                );
                datePickerDialog.show();
            }
        });

        // Cancel button
        btnCancel.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dialog.dismiss();
            }
        });

        // Save button
        btnSave.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                String title = etTitle.getText().toString().trim();
                String description = etDescription.getText().toString().trim();
                String category = spinnerCategory.getSelectedItem().toString();
                String deadline = selectedDate[0];

                if (title.isEmpty()) {
                    Toast.makeText(getContext(), "Vui lòng nhập tiêu đề", Toast.LENGTH_SHORT).show();
                    return;
                }

                if (deadline.isEmpty()) {
                    Toast.makeText(getContext(), "Vui lòng chọn ngày", Toast.LENGTH_SHORT).show();
                    return;
                }

                // Add new task
                addNewTask(title, category, description, deadline);
                dialog.dismiss();
                Toast.makeText(getContext(), "Đã thêm task mới!", Toast.LENGTH_SHORT).show();
            }
        });

        dialog.show();
    }

    private void addNewTask(String title, String category, String description, String deadline) {
        // Create new task
        Task newTask = new Task(
            title,
            category,
            description,
            deadline,
            "Chưa bắt đầu",
            Color.parseColor("#F44336") // Red for new tasks
        );

        // Add to both lists
        allTasks.add(newTask);
        taskList.add(newTask);

        // Notify adapter
        taskAdapter.notifyItemInserted(taskList.size() - 1);
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        binding = null;
    }

}
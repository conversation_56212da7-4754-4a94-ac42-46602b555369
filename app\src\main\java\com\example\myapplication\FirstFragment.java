package com.example.myapplication;

import android.graphics.Color;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.fragment.app.Fragment;
import androidx.navigation.fragment.NavHostFragment;
import androidx.recyclerview.widget.LinearLayoutManager;

import java.util.ArrayList;
import java.util.List;

import com.example.myapplication.databinding.FragmentFirstBinding;

public class FirstFragment extends Fragment {

    private FragmentFirstBinding binding;
    private TaskAdapter taskAdapter;
    private List<Task> taskList;
    private List<Task> allTasks;

    @Override
    public View onCreateView(
            LayoutInflater inflater, ViewGroup container,
            Bundle savedInstanceState
    ) {

        binding = FragmentFirstBinding.inflate(inflater, container, false);
        return binding.getRoot();

    }

    public void onViewCreated(@NonNull View view, Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);

        // Setup RecyclerView
        setupRecyclerView();

        // Load dummy data
        loadDummyData();

        // Xử lý click nút tìm kiếm
        binding.btnSearch.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                Toast.makeText(getContext(), "Tìm kiếm task", Toast.LENGTH_SHORT).show();
            }
        });

        // Xử lý click các nút chọn chủ đề
        binding.btnAllTasks.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                filterTasks("Tất cả");
                Toast.makeText(getContext(), "Hiển thị tất cả task", Toast.LENGTH_SHORT).show();
            }
        });

        binding.btnPersonalTasks.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                filterTasks("Cá nhân");
                Toast.makeText(getContext(), "Hiển thị task cá nhân", Toast.LENGTH_SHORT).show();
            }
        });

        binding.btnWorkTasks.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                filterTasks("Công việc");
                Toast.makeText(getContext(), "Hiển thị task công việc", Toast.LENGTH_SHORT).show();
            }
        });

        binding.btnAddCategory.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                Toast.makeText(getContext(), "Thêm chủ đề mới", Toast.LENGTH_SHORT).show();
            }
        });

        // Xử lý click nút thêm task
        binding.fabAddTask.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                Toast.makeText(getContext(), "Thêm task mới", Toast.LENGTH_SHORT).show();
            }
        });

        // Xử lý click navigation
        binding.navTasks.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                // Đã ở trang Tasks rồi, không cần làm gì
            }
        });

        binding.navCalendar.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                NavHostFragment.findNavController(FirstFragment.this)
                        .navigate(R.id.action_FirstFragment_to_SecondFragment);
            }
        });

        binding.navProfile.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                Toast.makeText(getContext(), "Chuyển đến trang Của tôi", Toast.LENGTH_SHORT).show();
            }
        });
    }

    private void setupRecyclerView() {
        taskList = new ArrayList<>();
        taskAdapter = new TaskAdapter(taskList);

        binding.recyclerTasks.setLayoutManager(new LinearLayoutManager(getContext()));
        binding.recyclerTasks.setAdapter(taskAdapter);

        // Set click listener for task items
        taskAdapter.setOnTaskClickListener(new TaskAdapter.OnTaskClickListener() {
            @Override
            public void onTaskClick(Task task) {
                Toast.makeText(getContext(), "Clicked: " + task.getTitle(), Toast.LENGTH_SHORT).show();
                // TODO: Navigate to task detail
            }
        });
    }

    private void loadDummyData() {
        allTasks = new ArrayList<>();

        // Thêm dữ liệu ảo
        allTasks.add(new Task(
            "Hoàn thành báo cáo tháng",
            "Công việc",
            "Viết báo cáo tổng kết công việc tháng 12 và gửi cho quản lý",
            "28/12/2024",
            "Đang thực hiện",
            Color.parseColor("#FF9800")
        ));

        allTasks.add(new Task(
            "Mua quà Giáng sinh",
            "Cá nhân",
            "Đi mua quà Giáng sinh cho gia đình và bạn bè",
            "24/12/2024",
            "Chưa bắt đầu",
            Color.parseColor("#F44336")
        ));

        allTasks.add(new Task(
            "Học tiếng Anh",
            "Cá nhân",
            "Ôn tập từ vựng và ngữ pháp tiếng Anh, làm bài tập",
            "30/12/2024",
            "Đang thực hiện",
            Color.parseColor("#2196F3")
        ));

        allTasks.add(new Task(
            "Họp team dự án",
            "Công việc",
            "Họp với team để thảo luận tiến độ dự án và phân công công việc",
            "26/12/2024",
            "Hoàn thành",
            Color.parseColor("#4CAF50")
        ));

        allTasks.add(new Task(
            "Tập thể dục",
            "Cá nhân",
            "Đi gym và tập cardio 45 phút",
            "25/12/2024",
            "Đang thực hiện",
            Color.parseColor("#2196F3")
        ));

        allTasks.add(new Task(
            "Review code",
            "Công việc",
            "Review code của các thành viên trong team và đưa feedback",
            "27/12/2024",
            "Chưa bắt đầu",
            Color.parseColor("#F44336")
        ));

        // Hiển thị tất cả task ban đầu
        taskList.clear();
        taskList.addAll(allTasks);
        taskAdapter.notifyDataSetChanged();
    }

    private void filterTasks(String category) {
        List<Task> filteredTasks = new ArrayList<>();

        if (category.equals("Tất cả")) {
            filteredTasks.addAll(allTasks);
        } else {
            for (Task task : allTasks) {
                if (task.getCategory().equals(category)) {
                    filteredTasks.add(task);
                }
            }
        }

        taskAdapter.updateTasks(filteredTasks);
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        binding = null;
    }

}
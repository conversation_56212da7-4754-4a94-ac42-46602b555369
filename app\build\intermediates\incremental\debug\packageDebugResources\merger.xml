<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\AndroidStudioProjects\MyApplication\app\src\main\res"/><source path="C:\Users\<USER>\AndroidStudioProjects\MyApplication\app\build\generated\res\rs\debug"/><source path="C:\Users\<USER>\AndroidStudioProjects\MyApplication\app\build\generated\res\resValues\debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\AndroidStudioProjects\MyApplication\app\src\main\res"><file name="ic_launcher_background" path="C:\Users\<USER>\AndroidStudioProjects\MyApplication\app\src\main\res\drawable\ic_launcher_background.xml" qualifiers="" type="drawable"/><file name="ic_launcher_foreground" path="C:\Users\<USER>\AndroidStudioProjects\MyApplication\app\src\main\res\drawable-v24\ic_launcher_foreground.xml" qualifiers="v24" type="drawable"/><file name="activity_main" path="C:\Users\<USER>\AndroidStudioProjects\MyApplication\app\src\main\res\layout\activity_main.xml" qualifiers="" type="layout"/><file name="content_main" path="C:\Users\<USER>\AndroidStudioProjects\MyApplication\app\src\main\res\layout\content_main.xml" qualifiers="" type="layout"/><file name="fragment_first" path="C:\Users\<USER>\AndroidStudioProjects\MyApplication\app\src\main\res\layout\fragment_first.xml" qualifiers="" type="layout"/><file name="fragment_second" path="C:\Users\<USER>\AndroidStudioProjects\MyApplication\app\src\main\res\layout\fragment_second.xml" qualifiers="" type="layout"/><file name="menu_main" path="C:\Users\<USER>\AndroidStudioProjects\MyApplication\app\src\main\res\menu\menu_main.xml" qualifiers="" type="menu"/><file name="ic_launcher" path="C:\Users\<USER>\AndroidStudioProjects\MyApplication\app\src\main\res\mipmap-anydpi-v26\ic_launcher.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\AndroidStudioProjects\MyApplication\app\src\main\res\mipmap-anydpi-v26\ic_launcher_round.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\AndroidStudioProjects\MyApplication\app\src\main\res\mipmap-hdpi\ic_launcher.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\AndroidStudioProjects\MyApplication\app\src\main\res\mipmap-hdpi\ic_launcher_round.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\AndroidStudioProjects\MyApplication\app\src\main\res\mipmap-mdpi\ic_launcher.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\AndroidStudioProjects\MyApplication\app\src\main\res\mipmap-mdpi\ic_launcher_round.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\AndroidStudioProjects\MyApplication\app\src\main\res\mipmap-xhdpi\ic_launcher.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\AndroidStudioProjects\MyApplication\app\src\main\res\mipmap-xhdpi\ic_launcher_round.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\AndroidStudioProjects\MyApplication\app\src\main\res\mipmap-xxhdpi\ic_launcher.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\AndroidStudioProjects\MyApplication\app\src\main\res\mipmap-xxhdpi\ic_launcher_round.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\AndroidStudioProjects\MyApplication\app\src\main\res\mipmap-xxxhdpi\ic_launcher.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\AndroidStudioProjects\MyApplication\app\src\main\res\mipmap-xxxhdpi\ic_launcher_round.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="nav_graph" path="C:\Users\<USER>\AndroidStudioProjects\MyApplication\app\src\main\res\navigation\nav_graph.xml" qualifiers="" type="navigation"/><file path="C:\Users\<USER>\AndroidStudioProjects\MyApplication\app\src\main\res\values\colors.xml" qualifiers=""><color name="purple_200">#FFBB86FC</color><color name="purple_500">#FF6200EE</color><color name="purple_700">#FF3700B3</color><color name="teal_200">#FF03DAC5</color><color name="teal_700">#FF018786</color><color name="black">#FF000000</color><color name="white">#FFFFFFFF</color></file><file path="C:\Users\<USER>\AndroidStudioProjects\MyApplication\app\src\main\res\values\dimens.xml" qualifiers=""><dimen name="fab_margin">16dp</dimen></file><file path="C:\Users\<USER>\AndroidStudioProjects\MyApplication\app\src\main\res\values\strings.xml" qualifiers=""><string name="app_name">My Application</string><string name="action_settings">Settings</string><string name="first_fragment_label">First Fragment</string><string name="second_fragment_label">Second Fragment</string><string name="next">Next</string><string name="previous">Previous</string><string name="hello_first_fragment">Hello first fragment</string><string name="hello_second_fragment">Hello second fragment. Arg: %1$s</string><string name="delete_task">Xóa</string><string name="category_all">Tất cả</string><string name="edit">Chỉnh sửa</string><string name="task_detail">Chi tiết Task</string><string name="nav_calendar">Lịch</string><string name="complete_task">Hoàn thành</string><string name="task_manager_title">Task Manager</string><string name="add_category">+ Thêm chủ đề</string><string name="search">Tìm kiếm</string><string name="category_personal">Cá nhân</string><string name="nav_profile">Của tôi</string><string name="nav_tasks">Nhiệm Vụ</string><string name="status_in_progress">Đang thực hiện</string><string name="status_completed">Hoàn thành</string><string name="status_pending">Chờ xử lý</string><string name="category_work">Công việc</string><string name="add_task">Thêm task mới</string></file><file path="C:\Users\<USER>\AndroidStudioProjects\MyApplication\app\src\main\res\values\themes.xml" qualifiers=""><style name="Theme.MyApplication" parent="Theme.MaterialComponents.DayNight.DarkActionBar">
        
        <item name="colorPrimary">@color/purple_500</item>
        <item name="colorPrimaryVariant">@color/purple_700</item>
        <item name="colorOnPrimary">@color/white</item>
        
        <item name="colorSecondary">@color/teal_200</item>
        <item name="colorSecondaryVariant">@color/teal_700</item>
        <item name="colorOnSecondary">@color/black</item>
        
        <item name="android:statusBarColor">?attr/colorPrimaryVariant</item>
        
    </style><style name="Theme.MyApplication.NoActionBar">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
    </style><style name="Theme.MyApplication.AppBarOverlay" parent="ThemeOverlay.AppCompat.Dark.ActionBar"/><style name="Theme.MyApplication.PopupOverlay" parent="ThemeOverlay.AppCompat.Light"/></file><file path="C:\Users\<USER>\AndroidStudioProjects\MyApplication\app\src\main\res\values-land\dimens.xml" qualifiers="land"><dimen name="fab_margin">48dp</dimen></file><file path="C:\Users\<USER>\AndroidStudioProjects\MyApplication\app\src\main\res\values-night\themes.xml" qualifiers="night-v8"><style name="Theme.MyApplication" parent="Theme.MaterialComponents.DayNight.DarkActionBar">
        
        <item name="colorPrimary">@color/purple_200</item>
        <item name="colorPrimaryVariant">@color/purple_700</item>
        <item name="colorOnPrimary">@color/black</item>
        
        <item name="colorSecondary">@color/teal_200</item>
        <item name="colorSecondaryVariant">@color/teal_200</item>
        <item name="colorOnSecondary">@color/black</item>
        
        <item name="android:statusBarColor">?attr/colorPrimaryVariant</item>
        
    </style></file><file path="C:\Users\<USER>\AndroidStudioProjects\MyApplication\app\src\main\res\values-w1240dp\dimens.xml" qualifiers="w1240dp-v13"><dimen name="fab_margin">200dp</dimen></file><file path="C:\Users\<USER>\AndroidStudioProjects\MyApplication\app\src\main\res\values-w600dp\dimens.xml" qualifiers="w600dp-v13"><dimen name="fab_margin">48dp</dimen></file><file name="backup_rules" path="C:\Users\<USER>\AndroidStudioProjects\MyApplication\app\src\main\res\xml\backup_rules.xml" qualifiers="" type="xml"/><file name="data_extraction_rules" path="C:\Users\<USER>\AndroidStudioProjects\MyApplication\app\src\main\res\xml\data_extraction_rules.xml" qualifiers="" type="xml"/><file name="fragment_task_detail" path="C:\Users\<USER>\AndroidStudioProjects\MyApplication\app\src\main\res\layout\fragment_task_detail.xml" qualifiers="" type="layout"/><file name="item_task" path="C:\Users\<USER>\AndroidStudioProjects\MyApplication\app\src\main\res\layout\item_task.xml" qualifiers="" type="layout"/></source><source path="C:\Users\<USER>\AndroidStudioProjects\MyApplication\app\build\generated\res\rs\debug"/><source path="C:\Users\<USER>\AndroidStudioProjects\MyApplication\app\build\generated\res\resValues\debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\AndroidStudioProjects\MyApplication\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\AndroidStudioProjects\MyApplication\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"/><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"/><mergedItems/></merger>
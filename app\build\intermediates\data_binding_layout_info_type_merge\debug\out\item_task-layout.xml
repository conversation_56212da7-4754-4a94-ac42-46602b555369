<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_task" modulePackage="com.example.myapplication" filePath="app\src\main\res\layout\item_task.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.cardview.widget.CardView"><Targets><Target tag="layout/item_task_0" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="1" startOffset="0" endLine="98" endOffset="35"/></Target><Target id="@+id/tv_task_title" view="TextView"><Expressions/><location startLine="24" startOffset="12" endLine="32" endOffset="45"/></Target><Target id="@+id/tv_task_category" view="TextView"><Expressions/><location startLine="34" startOffset="12" endLine="44" endOffset="50"/></Target><Target id="@+id/tv_task_description" view="TextView"><Expressions/><location startLine="49" startOffset="8" endLine="58" endOffset="37"/></Target><Target id="@+id/tv_task_deadline" view="TextView"><Expressions/><location startLine="73" startOffset="12" endLine="80" endOffset="50"/></Target><Target id="@+id/view_status_indicator" view="View"><Expressions/><location startLine="88" startOffset="12" endLine="92" endOffset="46"/></Target></Targets></Layout>
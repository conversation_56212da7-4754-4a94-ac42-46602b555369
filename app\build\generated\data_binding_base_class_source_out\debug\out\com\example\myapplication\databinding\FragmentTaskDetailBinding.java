// Generated by view binder compiler. Do not edit!
package com.example.myapplication.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.ImageButton;
import android.widget.ScrollView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.myapplication.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class FragmentTaskDetailBinding implements ViewBinding {
  @NonNull
  private final ScrollView rootView;

  @NonNull
  public final ImageButton btnBack;

  @NonNull
  public final Button btnDeleteTask;

  @NonNull
  public final ImageButton btnEdit;

  @NonNull
  public final Button btnMarkComplete;

  @NonNull
  public final TextView tvDetailCategory;

  @NonNull
  public final TextView tvDetailDeadline;

  @NonNull
  public final TextView tvDetailDescription;

  @NonNull
  public final TextView tvDetailStatus;

  @NonNull
  public final TextView tvDetailTitle;

  @NonNull
  public final View viewDetailStatus;

  private FragmentTaskDetailBinding(@NonNull ScrollView rootView, @NonNull ImageButton btnBack,
      @NonNull Button btnDeleteTask, @NonNull ImageButton btnEdit, @NonNull Button btnMarkComplete,
      @NonNull TextView tvDetailCategory, @NonNull TextView tvDetailDeadline,
      @NonNull TextView tvDetailDescription, @NonNull TextView tvDetailStatus,
      @NonNull TextView tvDetailTitle, @NonNull View viewDetailStatus) {
    this.rootView = rootView;
    this.btnBack = btnBack;
    this.btnDeleteTask = btnDeleteTask;
    this.btnEdit = btnEdit;
    this.btnMarkComplete = btnMarkComplete;
    this.tvDetailCategory = tvDetailCategory;
    this.tvDetailDeadline = tvDetailDeadline;
    this.tvDetailDescription = tvDetailDescription;
    this.tvDetailStatus = tvDetailStatus;
    this.tvDetailTitle = tvDetailTitle;
    this.viewDetailStatus = viewDetailStatus;
  }

  @Override
  @NonNull
  public ScrollView getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentTaskDetailBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentTaskDetailBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_task_detail, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentTaskDetailBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btn_back;
      ImageButton btnBack = ViewBindings.findChildViewById(rootView, id);
      if (btnBack == null) {
        break missingId;
      }

      id = R.id.btn_delete_task;
      Button btnDeleteTask = ViewBindings.findChildViewById(rootView, id);
      if (btnDeleteTask == null) {
        break missingId;
      }

      id = R.id.btn_edit;
      ImageButton btnEdit = ViewBindings.findChildViewById(rootView, id);
      if (btnEdit == null) {
        break missingId;
      }

      id = R.id.btn_mark_complete;
      Button btnMarkComplete = ViewBindings.findChildViewById(rootView, id);
      if (btnMarkComplete == null) {
        break missingId;
      }

      id = R.id.tv_detail_category;
      TextView tvDetailCategory = ViewBindings.findChildViewById(rootView, id);
      if (tvDetailCategory == null) {
        break missingId;
      }

      id = R.id.tv_detail_deadline;
      TextView tvDetailDeadline = ViewBindings.findChildViewById(rootView, id);
      if (tvDetailDeadline == null) {
        break missingId;
      }

      id = R.id.tv_detail_description;
      TextView tvDetailDescription = ViewBindings.findChildViewById(rootView, id);
      if (tvDetailDescription == null) {
        break missingId;
      }

      id = R.id.tv_detail_status;
      TextView tvDetailStatus = ViewBindings.findChildViewById(rootView, id);
      if (tvDetailStatus == null) {
        break missingId;
      }

      id = R.id.tv_detail_title;
      TextView tvDetailTitle = ViewBindings.findChildViewById(rootView, id);
      if (tvDetailTitle == null) {
        break missingId;
      }

      id = R.id.view_detail_status;
      View viewDetailStatus = ViewBindings.findChildViewById(rootView, id);
      if (viewDetailStatus == null) {
        break missingId;
      }

      return new FragmentTaskDetailBinding((ScrollView) rootView, btnBack, btnDeleteTask, btnEdit,
          btnMarkComplete, tvDetailCategory, tvDetailDeadline, tvDetailDescription, tvDetailStatus,
          tvDetailTitle, viewDetailStatus);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}

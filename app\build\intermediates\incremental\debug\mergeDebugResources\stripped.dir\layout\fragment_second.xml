<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="#F5F5F5"
    tools:context=".SecondFragment">

    <!-- Header với Logo và Navbar -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="16dp"
        android:background="#2196F3"
        android:gravity="center_vertical">

        <ImageView
            android:id="@+id/logo_calendar"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:src="@android:drawable/ic_menu_my_calendar"
            android:tint="@android:color/white" />

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="Lịch"
            android:textColor="@android:color/white"
            android:textSize="20sp"
            android:textStyle="bold"
            android:layout_marginStart="12dp" />

        <ImageButton
            android:id="@+id/btn_calendar_today"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:src="@android:drawable/ic_menu_today"
            android:background="?android:attr/selectableItemBackgroundBorderless"
            android:tint="@android:color/white" />

    </LinearLayout>

    <!-- Calendar View -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:background="@android:color/white"
        android:layout_margin="16dp"
        android:elevation="4dp"
        android:padding="16dp">

        <!-- Month/Year Header -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_marginBottom="16dp">

            <ImageButton
                android:id="@+id/btn_prev_month"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:src="@android:drawable/ic_media_previous"
                android:background="?android:attr/selectableItemBackgroundBorderless"
                android:tint="#2196F3" />

            <TextView
                android:id="@+id/tv_month_year"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="Tháng 12, 2024"
                android:textSize="18sp"
                android:textStyle="bold"
                android:textColor="#212121"
                android:gravity="center" />

            <ImageButton
                android:id="@+id/btn_next_month"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:src="@android:drawable/ic_media_next"
                android:background="?android:attr/selectableItemBackgroundBorderless"
                android:tint="#2196F3" />

        </LinearLayout>

        <!-- Days of Week Header -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="8dp">

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="CN"
                android:textSize="14sp"
                android:textStyle="bold"
                android:textColor="#757575"
                android:gravity="center"
                android:padding="8dp" />

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="T2"
                android:textSize="14sp"
                android:textStyle="bold"
                android:textColor="#757575"
                android:gravity="center"
                android:padding="8dp" />

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="T3"
                android:textSize="14sp"
                android:textStyle="bold"
                android:textColor="#757575"
                android:gravity="center"
                android:padding="8dp" />

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="T4"
                android:textSize="14sp"
                android:textStyle="bold"
                android:textColor="#757575"
                android:gravity="center"
                android:padding="8dp" />

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="T5"
                android:textSize="14sp"
                android:textStyle="bold"
                android:textColor="#757575"
                android:gravity="center"
                android:padding="8dp" />

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="T6"
                android:textSize="14sp"
                android:textStyle="bold"
                android:textColor="#757575"
                android:gravity="center"
                android:padding="8dp" />

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="T7"
                android:textSize="14sp"
                android:textStyle="bold"
                android:textColor="#757575"
                android:gravity="center"
                android:padding="8dp" />

        </LinearLayout>

        <!-- Calendar Grid (Sample Week) -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <TextView
                android:layout_width="0dp"
                android:layout_height="40dp"
                android:layout_weight="1"
                android:text="22"
                android:textSize="16sp"
                android:textColor="#212121"
                android:gravity="center"
                android:background="?android:attr/selectableItemBackground" />

            <TextView
                android:layout_width="0dp"
                android:layout_height="40dp"
                android:layout_weight="1"
                android:text="23"
                android:textSize="16sp"
                android:textColor="#212121"
                android:gravity="center"
                android:background="?android:attr/selectableItemBackground" />

            <TextView
                android:layout_width="0dp"
                android:layout_height="40dp"
                android:layout_weight="1"
                android:text="24"
                android:textSize="16sp"
                android:textColor="#212121"
                android:gravity="center"
                android:background="?android:attr/selectableItemBackground" />

            <TextView
                android:id="@+id/tv_today"
                android:layout_width="0dp"
                android:layout_height="40dp"
                android:layout_weight="1"
                android:text="25"
                android:textSize="16sp"
                android:textColor="@android:color/white"
                android:gravity="center"
                android:background="#2196F3" />

            <TextView
                android:layout_width="0dp"
                android:layout_height="40dp"
                android:layout_weight="1"
                android:text="26"
                android:textSize="16sp"
                android:textColor="#212121"
                android:gravity="center"
                android:background="?android:attr/selectableItemBackground" />

            <TextView
                android:layout_width="0dp"
                android:layout_height="40dp"
                android:layout_weight="1"
                android:text="27"
                android:textSize="16sp"
                android:textColor="#212121"
                android:gravity="center"
                android:background="?android:attr/selectableItemBackground" />

            <TextView
                android:layout_width="0dp"
                android:layout_height="40dp"
                android:layout_weight="1"
                android:text="28"
                android:textSize="16sp"
                android:textColor="#212121"
                android:gravity="center"
                android:background="?android:attr/selectableItemBackground" />

        </LinearLayout>

    </LinearLayout>

    <!-- Tasks for Selected Date -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:background="@android:color/white"
        android:layout_marginHorizontal="16dp"
        android:layout_marginBottom="16dp"
        android:elevation="4dp"
        android:padding="16dp">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Nhiệm vụ hôm nay (25/12/2024)"
            android:textSize="16sp"
            android:textStyle="bold"
            android:textColor="#212121"
            android:layout_marginBottom="12dp" />

        <!-- Task List for Today -->
        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recycler_calendar_tasks"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:nestedScrollingEnabled="false"
            tools:listitem="@layout/item_calendar_task" />

    </LinearLayout>

    <!-- Footer Navigation -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:background="@android:color/white"
        android:elevation="8dp">

        <LinearLayout
            android:id="@+id/nav_tasks_calendar"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical"
            android:padding="12dp"
            android:gravity="center"
            android:background="?android:attr/selectableItemBackground">

            <ImageView
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:src="@android:drawable/ic_menu_agenda"
                android:tint="#757575" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Nhiệm Vụ"
                android:textSize="12sp"
                android:textColor="#757575"
                android:layout_marginTop="4dp" />

        </LinearLayout>

        <LinearLayout
            android:id="@+id/nav_calendar_active"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical"
            android:padding="12dp"
            android:gravity="center"
            android:background="?android:attr/selectableItemBackground">

            <ImageView
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:src="@android:drawable/ic_menu_my_calendar"
                android:tint="#2196F3" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Lịch"
                android:textSize="12sp"
                android:textColor="#2196F3"
                android:layout_marginTop="4dp" />

        </LinearLayout>

        <LinearLayout
            android:id="@+id/nav_profile_calendar"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical"
            android:padding="12dp"
            android:gravity="center"
            android:background="?android:attr/selectableItemBackground">

            <ImageView
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:src="@android:drawable/ic_menu_myplaces"
                android:tint="#757575" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Của tôi"
                android:textSize="12sp"
                android:textColor="#757575"
                android:layout_marginTop="4dp" />

        </LinearLayout>

    </LinearLayout>

</LinearLayout>
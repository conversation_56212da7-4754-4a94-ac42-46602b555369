[{"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.6\\com.example.myapplication.app-merged_res-32:\\mipmap-xxxhdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.6\\com.example.myapplication.app-main-34:\\mipmap-xxxhdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.6\\com.example.myapplication.app-merged_res-32:\\layout_fragment_task_detail.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.6\\com.example.myapplication.app-main-34:\\layout\\fragment_task_detail.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.6\\com.example.myapplication.app-merged_res-32:\\mipmap-xhdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.6\\com.example.myapplication.app-main-34:\\mipmap-xhdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.6\\com.example.myapplication.app-merged_res-32:\\mipmap-xhdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.6\\com.example.myapplication.app-main-34:\\mipmap-xhdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.6\\com.example.myapplication.app-merged_res-32:\\mipmap-anydpi-v26_ic_launcher.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.6\\com.example.myapplication.app-main-34:\\mipmap-anydpi-v26\\ic_launcher.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.6\\com.example.myapplication.app-merged_res-32:\\drawable_ic_launcher_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.6\\com.example.myapplication.app-main-34:\\drawable\\ic_launcher_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.6\\com.example.myapplication.app-merged_res-32:\\mipmap-xxhdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.6\\com.example.myapplication.app-main-34:\\mipmap-xxhdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.6\\com.example.myapplication.app-merged_res-32:\\layout_item_task.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.6\\com.example.myapplication.app-main-34:\\layout\\item_task.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.6\\com.example.myapplication.app-merged_res-32:\\layout_fragment_first.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.6\\com.example.myapplication.app-main-34:\\layout\\fragment_first.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.6\\com.example.myapplication.app-merged_res-32:\\layout_fragment_second.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.6\\com.example.myapplication.app-main-34:\\layout\\fragment_second.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.6\\com.example.myapplication.app-merged_res-32:\\mipmap-hdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.6\\com.example.myapplication.app-main-34:\\mipmap-hdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.6\\com.example.myapplication.app-merged_res-32:\\drawable-anydpi-v24_ic_launcher_foreground.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.6\\com.example.myapplication.app-pngs-27:\\drawable-anydpi-v24\\ic_launcher_foreground.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.6\\com.example.myapplication.app-merged_res-32:\\mipmap-hdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.6\\com.example.myapplication.app-main-34:\\mipmap-hdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.6\\com.example.myapplication.app-merged_res-32:\\mipmap-mdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.6\\com.example.myapplication.app-main-34:\\mipmap-mdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.6\\com.example.myapplication.app-merged_res-32:\\mipmap-anydpi-v26_ic_launcher_round.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.6\\com.example.myapplication.app-main-34:\\mipmap-anydpi-v26\\ic_launcher_round.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.6\\com.example.myapplication.app-merged_res-32:\\xml_data_extraction_rules.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.6\\com.example.myapplication.app-main-34:\\xml\\data_extraction_rules.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.6\\com.example.myapplication.app-merged_res-32:\\xml_backup_rules.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.6\\com.example.myapplication.app-main-34:\\xml\\backup_rules.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.6\\com.example.myapplication.app-merged_res-32:\\layout_content_main.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.6\\com.example.myapplication.app-main-34:\\layout\\content_main.xml"}, {"merged": "com.example.myapplication.app-merged_res-32:/layout_fragment_task_detail.xml.flat", "source": "com.example.myapplication.app-main-34:/layout/fragment_task_detail.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.6\\com.example.myapplication.app-merged_res-32:\\navigation_nav_graph.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.6\\com.example.myapplication.app-main-34:\\navigation\\nav_graph.xml"}, {"merged": "com.example.myapplication.app-merged_res-32:/layout_fragment_first.xml.flat", "source": "com.example.myapplication.app-main-34:/layout/fragment_first.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.6\\com.example.myapplication.app-merged_res-32:\\mipmap-xxxhdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.6\\com.example.myapplication.app-main-34:\\mipmap-xxxhdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.6\\com.example.myapplication.app-merged_res-32:\\mipmap-mdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.6\\com.example.myapplication.app-main-34:\\mipmap-mdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.6\\com.example.myapplication.app-merged_res-32:\\layout_activity_main.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.6\\com.example.myapplication.app-main-34:\\layout\\activity_main.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.6\\com.example.myapplication.app-merged_res-32:\\mipmap-xxhdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.6\\com.example.myapplication.app-main-34:\\mipmap-xxhdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.6\\com.example.myapplication.app-merged_res-32:\\menu_menu_main.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.6\\com.example.myapplication.app-main-34:\\menu\\menu_main.xml"}]
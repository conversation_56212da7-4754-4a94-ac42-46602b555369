// Generated by view binder compiler. Do not edit!
package com.example.myapplication.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.EditText;
import android.widget.LinearLayout;
import android.widget.Spinner;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.myapplication.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class DialogAddTaskBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final Button btnCancel;

  @NonNull
  public final Button btnSave;

  @NonNull
  public final Button btnSelectDate;

  @NonNull
  public final EditText etTaskDescription;

  @NonNull
  public final EditText etTaskTitle;

  @NonNull
  public final Spinner spinnerCategory;

  private DialogAddTaskBinding(@NonNull LinearLayout rootView, @NonNull Button btnCancel,
      @NonNull Button btnSave, @NonNull Button btnSelectDate, @NonNull EditText etTaskDescription,
      @NonNull EditText etTaskTitle, @NonNull Spinner spinnerCategory) {
    this.rootView = rootView;
    this.btnCancel = btnCancel;
    this.btnSave = btnSave;
    this.btnSelectDate = btnSelectDate;
    this.etTaskDescription = etTaskDescription;
    this.etTaskTitle = etTaskTitle;
    this.spinnerCategory = spinnerCategory;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static DialogAddTaskBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static DialogAddTaskBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.dialog_add_task, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static DialogAddTaskBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btn_cancel;
      Button btnCancel = ViewBindings.findChildViewById(rootView, id);
      if (btnCancel == null) {
        break missingId;
      }

      id = R.id.btn_save;
      Button btnSave = ViewBindings.findChildViewById(rootView, id);
      if (btnSave == null) {
        break missingId;
      }

      id = R.id.btn_select_date;
      Button btnSelectDate = ViewBindings.findChildViewById(rootView, id);
      if (btnSelectDate == null) {
        break missingId;
      }

      id = R.id.et_task_description;
      EditText etTaskDescription = ViewBindings.findChildViewById(rootView, id);
      if (etTaskDescription == null) {
        break missingId;
      }

      id = R.id.et_task_title;
      EditText etTaskTitle = ViewBindings.findChildViewById(rootView, id);
      if (etTaskTitle == null) {
        break missingId;
      }

      id = R.id.spinner_category;
      Spinner spinnerCategory = ViewBindings.findChildViewById(rootView, id);
      if (spinnerCategory == null) {
        break missingId;
      }

      return new DialogAddTaskBinding((LinearLayout) rootView, btnCancel, btnSave, btnSelectDate,
          etTaskDescription, etTaskTitle, spinnerCategory);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}

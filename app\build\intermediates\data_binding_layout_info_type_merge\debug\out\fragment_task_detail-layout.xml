<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_task_detail" modulePackage="com.example.myapplication" filePath="app\src\main\res\layout\fragment_task_detail.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.ScrollView"><Targets><Target tag="layout/fragment_task_detail_0" view="ScrollView"><Expressions/><location startLine="1" startOffset="0" endLine="218" endOffset="12"/></Target><Target id="@+id/btn_back" view="ImageButton"><Expressions/><location startLine="21" startOffset="12" endLine="26" endOffset="87"/></Target><Target id="@+id/btn_edit" view="ImageButton"><Expressions/><location startLine="39" startOffset="12" endLine="44" endOffset="87"/></Target><Target id="@+id/tv_detail_title" view="TextView"><Expressions/><location startLine="70" startOffset="16" endLine="78" endOffset="56"/></Target><Target id="@+id/tv_detail_category" view="TextView"><Expressions/><location startLine="89" startOffset="16" endLine="99" endOffset="56"/></Target><Target id="@+id/tv_detail_description" view="TextView"><Expressions/><location startLine="110" startOffset="16" endLine="118" endOffset="56"/></Target><Target id="@+id/tv_detail_deadline" view="TextView"><Expressions/><location startLine="142" startOffset="20" endLine="149" endOffset="58"/></Target><Target id="@+id/view_detail_status" view="View"><Expressions/><location startLine="168" startOffset="20" endLine="172" endOffset="54"/></Target><Target id="@+id/tv_detail_status" view="TextView"><Expressions/><location startLine="174" startOffset="20" endLine="181" endOffset="58"/></Target><Target id="@+id/btn_mark_complete" view="Button"><Expressions/><location startLine="195" startOffset="12" endLine="202" endOffset="48"/></Target><Target id="@+id/btn_delete_task" view="Button"><Expressions/><location startLine="204" startOffset="12" endLine="212" endOffset="50"/></Target></Targets></Layout>
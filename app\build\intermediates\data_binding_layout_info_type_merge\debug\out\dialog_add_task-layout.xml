<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="dialog_add_task" modulePackage="com.example.myapplication" filePath="app\src\main\res\layout\dialog_add_task.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/dialog_add_task_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="110" endOffset="14"/></Target><Target id="@+id/et_task_title" view="EditText"><Expressions/><location startLine="25" startOffset="4" endLine="31" endOffset="44"/></Target><Target id="@+id/et_task_description" view="EditText"><Expressions/><location startLine="42" startOffset="4" endLine="49" endOffset="44"/></Target><Target id="@+id/spinner_category" view="Spinner"><Expressions/><location startLine="60" startOffset="4" endLine="64" endOffset="44"/></Target><Target id="@+id/btn_select_date" view="Button"><Expressions/><location startLine="75" startOffset="4" endLine="82" endOffset="37"/></Target><Target id="@+id/btn_cancel" view="Button"><Expressions/><location startLine="91" startOffset="8" endLine="98" endOffset="41"/></Target><Target id="@+id/btn_save" view="Button"><Expressions/><location startLine="100" startOffset="8" endLine="106" endOffset="54"/></Target></Targets></Layout>
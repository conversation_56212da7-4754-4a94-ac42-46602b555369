<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_calendar_task" modulePackage="com.example.myapplication" filePath="app\src\main\res\layout\item_calendar_task.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/item_calendar_task_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="59" endOffset="14"/></Target><Target id="@+id/tv_calendar_task_time" view="TextView"><Expressions/><location startLine="10" startOffset="4" endLine="18" endOffset="34"/></Target><Target id="@+id/tv_calendar_task_title" view="TextView"><Expressions/><location startLine="28" startOffset="8" endLine="36" endOffset="47"/></Target><Target id="@+id/tv_calendar_task_category" view="TextView"><Expressions/><location startLine="38" startOffset="8" endLine="47" endOffset="43"/></Target><Target id="@+id/view_calendar_task_status" view="View"><Expressions/><location startLine="52" startOffset="4" endLine="57" endOffset="42"/></Target></Targets></Layout>
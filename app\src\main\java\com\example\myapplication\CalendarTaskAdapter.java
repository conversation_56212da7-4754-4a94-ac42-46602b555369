package com.example.myapplication;

import android.graphics.Color;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;
import java.util.List;

public class CalendarTaskAdapter extends RecyclerView.Adapter<CalendarTaskAdapter.CalendarTaskViewHolder> {
    
    private List<CalendarTask> taskList;

    public CalendarTaskAdapter(List<CalendarTask> taskList) {
        this.taskList = taskList;
    }

    @NonNull
    @Override
    public CalendarTaskViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext())
                .inflate(R.layout.item_calendar_task, parent, false);
        return new CalendarTaskViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull CalendarTaskViewHolder holder, int position) {
        CalendarTask task = taskList.get(position);
        
        holder.tvTime.setText(task.getTime());
        holder.tvTitle.setText(task.getTitle());
        holder.tvCategory.setText(task.getCategory());
        
        // Set category background color
        if (task.getCategory().equals("Cá nhân")) {
            holder.tvCategory.setBackgroundColor(Color.parseColor("#2196F3"));
        } else if (task.getCategory().equals("Công việc")) {
            holder.tvCategory.setBackgroundColor(Color.parseColor("#FF9800"));
        } else {
            holder.tvCategory.setBackgroundColor(Color.parseColor("#4CAF50"));
        }
        
        // Set status indicator color
        holder.viewStatusIndicator.setBackgroundColor(task.getStatusColor());
    }

    @Override
    public int getItemCount() {
        return taskList.size();
    }

    static class CalendarTaskViewHolder extends RecyclerView.ViewHolder {
        TextView tvTime, tvTitle, tvCategory;
        View viewStatusIndicator;

        public CalendarTaskViewHolder(@NonNull View itemView) {
            super(itemView);
            tvTime = itemView.findViewById(R.id.tv_calendar_task_time);
            tvTitle = itemView.findViewById(R.id.tv_calendar_task_title);
            tvCategory = itemView.findViewById(R.id.tv_calendar_task_category);
            viewStatusIndicator = itemView.findViewById(R.id.view_calendar_task_status);
        }
    }
}

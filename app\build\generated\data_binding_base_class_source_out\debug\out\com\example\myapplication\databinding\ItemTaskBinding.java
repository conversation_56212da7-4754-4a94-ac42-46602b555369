// Generated by view binder compiler. Do not edit!
package com.example.myapplication.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.myapplication.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemTaskBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final TextView tvTaskCategory;

  @NonNull
  public final TextView tvTaskDeadline;

  @NonNull
  public final TextView tvTaskDescription;

  @NonNull
  public final TextView tvTaskTitle;

  @NonNull
  public final View viewStatusIndicator;

  private ItemTaskBinding(@NonNull LinearLayout rootView, @NonNull TextView tvTaskCategory,
      @NonNull TextView tvTaskDeadline, @NonNull TextView tvTaskDescription,
      @NonNull TextView tvTaskTitle, @NonNull View viewStatusIndicator) {
    this.rootView = rootView;
    this.tvTaskCategory = tvTaskCategory;
    this.tvTaskDeadline = tvTaskDeadline;
    this.tvTaskDescription = tvTaskDescription;
    this.tvTaskTitle = tvTaskTitle;
    this.viewStatusIndicator = viewStatusIndicator;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemTaskBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemTaskBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_task, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemTaskBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.tv_task_category;
      TextView tvTaskCategory = ViewBindings.findChildViewById(rootView, id);
      if (tvTaskCategory == null) {
        break missingId;
      }

      id = R.id.tv_task_deadline;
      TextView tvTaskDeadline = ViewBindings.findChildViewById(rootView, id);
      if (tvTaskDeadline == null) {
        break missingId;
      }

      id = R.id.tv_task_description;
      TextView tvTaskDescription = ViewBindings.findChildViewById(rootView, id);
      if (tvTaskDescription == null) {
        break missingId;
      }

      id = R.id.tv_task_title;
      TextView tvTaskTitle = ViewBindings.findChildViewById(rootView, id);
      if (tvTaskTitle == null) {
        break missingId;
      }

      id = R.id.view_status_indicator;
      View viewStatusIndicator = ViewBindings.findChildViewById(rootView, id);
      if (viewStatusIndicator == null) {
        break missingId;
      }

      return new ItemTaskBinding((LinearLayout) rootView, tvTaskCategory, tvTaskDeadline,
          tvTaskDescription, tvTaskTitle, viewStatusIndicator);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}

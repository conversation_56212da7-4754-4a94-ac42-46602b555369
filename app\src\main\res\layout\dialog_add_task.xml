<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="24dp">

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Thêm Task Mới"
        android:textSize="20sp"
        android:textStyle="bold"
        android:textColor="#212121"
        android:layout_marginBottom="16dp" />

    <!-- Tiêu đề -->
    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Tiêu đề:"
        android:textSize="16sp"
        android:textColor="#757575"
        android:layout_marginBottom="8dp" />

    <EditText
        android:id="@+id/et_task_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:hint="Nhập tiêu đề task"
        android:inputType="text"
        android:layout_marginBottom="16dp" />

    <!-- Mô tả -->
    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Mô tả:"
        android:textSize="16sp"
        android:textColor="#757575"
        android:layout_marginBottom="8dp" />

    <EditText
        android:id="@+id/et_task_description"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:hint="Nhập mô tả task"
        android:inputType="textMultiLine"
        android:lines="3"
        android:layout_marginBottom="16dp" />

    <!-- Chủ đề -->
    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Chủ đề:"
        android:textSize="16sp"
        android:textColor="#757575"
        android:layout_marginBottom="8dp" />

    <Spinner
        android:id="@+id/spinner_category"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="16dp" />

    <!-- Deadline -->
    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Hạn chót:"
        android:textSize="16sp"
        android:textColor="#757575"
        android:layout_marginBottom="8dp" />

    <Button
        android:id="@+id/btn_select_date"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="24dp"
        android:background="@android:drawable/btn_default"
        android:text="Chọn ngày"
        android:textColor="#EAEEF1" />

    <!-- Buttons -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="end">

        <Button
            android:id="@+id/btn_cancel"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="8dp"
            android:backgroundTint="#F44336"
            android:text="Hủy"
            android:textColor="#F6F4F4" />

        <Button
            android:id="@+id/btn_save"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:backgroundTint="#4CAF50"
            android:text="Lưu"
            android:textColor="@android:color/white" />

    </LinearLayout>

</LinearLayout>

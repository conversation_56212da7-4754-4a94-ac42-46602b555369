// Generated by view binder compiler. Do not edit!
package com.example.myapplication.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.myapplication.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemCalendarTaskBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final TextView tvCalendarTaskCategory;

  @NonNull
  public final TextView tvCalendarTaskTime;

  @NonNull
  public final TextView tvCalendarTaskTitle;

  @NonNull
  public final View viewCalendarTaskStatus;

  private ItemCalendarTaskBinding(@NonNull LinearLayout rootView,
      @NonNull TextView tvCalendarTaskCategory, @NonNull TextView tvCalendarTaskTime,
      @NonNull TextView tvCalendarTaskTitle, @NonNull View viewCalendarTaskStatus) {
    this.rootView = rootView;
    this.tvCalendarTaskCategory = tvCalendarTaskCategory;
    this.tvCalendarTaskTime = tvCalendarTaskTime;
    this.tvCalendarTaskTitle = tvCalendarTaskTitle;
    this.viewCalendarTaskStatus = viewCalendarTaskStatus;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemCalendarTaskBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemCalendarTaskBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_calendar_task, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemCalendarTaskBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.tv_calendar_task_category;
      TextView tvCalendarTaskCategory = ViewBindings.findChildViewById(rootView, id);
      if (tvCalendarTaskCategory == null) {
        break missingId;
      }

      id = R.id.tv_calendar_task_time;
      TextView tvCalendarTaskTime = ViewBindings.findChildViewById(rootView, id);
      if (tvCalendarTaskTime == null) {
        break missingId;
      }

      id = R.id.tv_calendar_task_title;
      TextView tvCalendarTaskTitle = ViewBindings.findChildViewById(rootView, id);
      if (tvCalendarTaskTitle == null) {
        break missingId;
      }

      id = R.id.view_calendar_task_status;
      View viewCalendarTaskStatus = ViewBindings.findChildViewById(rootView, id);
      if (viewCalendarTaskStatus == null) {
        break missingId;
      }

      return new ItemCalendarTaskBinding((LinearLayout) rootView, tvCalendarTaskCategory,
          tvCalendarTaskTime, tvCalendarTaskTitle, viewCalendarTaskStatus);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}

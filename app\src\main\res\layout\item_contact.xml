<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    android:padding="16dp"
    android:background="?android:attr/selectableItemBackground"
    android:gravity="center_vertical">

    <!-- Contact Icon -->
    <ImageView
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:src="@android:drawable/ic_menu_call"
        android:tint="#2196F3"
        android:layout_marginEnd="16dp" />

    <!-- Contact Info -->
    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:orientation="vertical">

        <TextView
            android:id="@+id/tv_contact_name"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Tên liên hệ"
            android:textSize="16sp"
            android:textStyle="bold"
            android:textColor="#212121"
            android:layout_marginBottom="4dp" />

        <TextView
            android:id="@+id/tv_contact_phone"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Số điện thoại"
            android:textSize="14sp"
            android:textColor="#757575" />

    </LinearLayout>

    <!-- Action Button -->
    <TextView
        android:id="@+id/tv_contact_action"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Tạo task"
        android:textSize="12sp"
        android:textColor="@android:color/white"
        android:background="#4CAF50"
        android:paddingHorizontal="12dp"
        android:paddingVertical="6dp"
        android:layout_marginStart="8dp" />

</LinearLayout>

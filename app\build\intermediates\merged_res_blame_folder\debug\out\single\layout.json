[{"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.6\\com.example.myapplication.app-mergeDebugResources-31:\\layout\\activity_main.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.6\\com.example.myapplication.app-main-34:\\layout\\activity_main.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.6\\com.example.myapplication.app-mergeDebugResources-31:\\layout\\fragment_first.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.6\\com.example.myapplication.app-main-34:\\layout\\fragment_first.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.6\\com.example.myapplication.app-mergeDebugResources-31:\\layout\\dialog_add_task.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.6\\com.example.myapplication.app-main-34:\\layout\\dialog_add_task.xml"}, {"merged": "com.example.myapplication.app-mergeDebugResources-31:/layout/fragment_first.xml", "source": "com.example.myapplication.app-main-34:/layout/fragment_first.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.6\\com.example.myapplication.app-mergeDebugResources-31:\\layout\\item_calendar_task.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.6\\com.example.myapplication.app-main-34:\\layout\\item_calendar_task.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.6\\com.example.myapplication.app-mergeDebugResources-31:\\layout\\fragment_second.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.6\\com.example.myapplication.app-main-34:\\layout\\fragment_second.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.6\\com.example.myapplication.app-mergeDebugResources-31:\\layout\\item_task.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.6\\com.example.myapplication.app-main-34:\\layout\\item_task.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.6\\com.example.myapplication.app-mergeDebugResources-31:\\layout\\fragment_task_detail.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.6\\com.example.myapplication.app-main-34:\\layout\\fragment_task_detail.xml"}, {"merged": "com.example.myapplication.app-mergeDebugResources-31:/layout/dialog_add_task.xml", "source": "com.example.myapplication.app-main-34:/layout/dialog_add_task.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.6\\com.example.myapplication.app-mergeDebugResources-31:\\layout\\content_main.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.6\\com.example.myapplication.app-main-34:\\layout\\content_main.xml"}]
<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_task_detail" modulePackage="com.example.myapplication" filePath="app\src\main\res\layout\fragment_task_detail.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.ScrollView"><Targets><Target tag="layout/fragment_task_detail_0" view="ScrollView"><Expressions/><location startLine="1" startOffset="0" endLine="222" endOffset="12"/></Target><Target id="@+id/btn_back" view="ImageButton"><Expressions/><location startLine="21" startOffset="12" endLine="27" endOffset="53"/></Target><Target id="@+id/btn_edit" view="ImageButton"><Expressions/><location startLine="39" startOffset="12" endLine="45" endOffset="53"/></Target><Target id="@+id/tv_detail_title" view="TextView"><Expressions/><location startLine="72" startOffset="16" endLine="80" endOffset="56"/></Target><Target id="@+id/tv_detail_category" view="TextView"><Expressions/><location startLine="91" startOffset="16" endLine="101" endOffset="56"/></Target><Target id="@+id/tv_detail_description" view="TextView"><Expressions/><location startLine="112" startOffset="16" endLine="120" endOffset="56"/></Target><Target id="@+id/tv_detail_deadline" view="TextView"><Expressions/><location startLine="144" startOffset="20" endLine="151" endOffset="58"/></Target><Target id="@+id/view_detail_status" view="View"><Expressions/><location startLine="170" startOffset="20" endLine="174" endOffset="54"/></Target><Target id="@+id/tv_detail_status" view="TextView"><Expressions/><location startLine="176" startOffset="20" endLine="183" endOffset="58"/></Target><Target id="@+id/btn_mark_complete" view="Button"><Expressions/><location startLine="199" startOffset="12" endLine="206" endOffset="48"/></Target><Target id="@+id/btn_delete_task" view="Button"><Expressions/><location startLine="208" startOffset="12" endLine="216" endOffset="50"/></Target></Targets></Layout>
<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_task" modulePackage="com.example.myapplication" filePath="app\src\main\res\layout\item_task.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/item_task_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="93" endOffset="14"/></Target><Target id="@+id/tv_task_title" view="TextView"><Expressions/><location startLine="21" startOffset="12" endLine="29" endOffset="45"/></Target><Target id="@+id/tv_task_category" view="TextView"><Expressions/><location startLine="31" startOffset="12" endLine="41" endOffset="50"/></Target><Target id="@+id/tv_task_description" view="TextView"><Expressions/><location startLine="46" startOffset="8" endLine="55" endOffset="37"/></Target><Target id="@+id/tv_task_deadline" view="TextView"><Expressions/><location startLine="70" startOffset="12" endLine="77" endOffset="50"/></Target><Target id="@+id/view_status_indicator" view="View"><Expressions/><location startLine="85" startOffset="12" endLine="89" endOffset="46"/></Target></Targets></Layout>
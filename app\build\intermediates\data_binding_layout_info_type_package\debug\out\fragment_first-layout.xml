<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_first" modulePackage="com.example.myapplication" filePath="app\src\main\res\layout\fragment_first.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/fragment_first_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="226" endOffset="14"/></Target><Target id="@+id/logo" view="ImageView"><Expressions/><location startLine="19" startOffset="8" endLine="24" endOffset="49"/></Target><Target id="@+id/btn_search" view="ImageButton"><Expressions/><location startLine="36" startOffset="8" endLine="42" endOffset="49"/></Target><Target id="@+id/btn_all_tasks" view="Button"><Expressions/><location startLine="72" startOffset="16" endLine="79" endOffset="62"/></Target><Target id="@+id/btn_personal_tasks" view="Button"><Expressions/><location startLine="81" startOffset="16" endLine="88" endOffset="49"/></Target><Target id="@+id/btn_work_tasks" view="Button"><Expressions/><location startLine="90" startOffset="16" endLine="97" endOffset="49"/></Target><Target id="@+id/btn_add_category" view="Button"><Expressions/><location startLine="99" startOffset="16" endLine="105" endOffset="49"/></Target><Target id="@+id/recycler_tasks" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="117" startOffset="8" endLine="123" endOffset="48"/></Target><Target id="@+id/fab_add_task" view="com.google.android.material.floatingactionbutton.FloatingActionButton"><Expressions/><location startLine="126" startOffset="8" endLine="134" endOffset="42"/></Target><Target id="@+id/nav_tasks" view="LinearLayout"><Expressions/><location startLine="146" startOffset="8" endLine="170" endOffset="22"/></Target><Target id="@+id/nav_calendar" view="LinearLayout"><Expressions/><location startLine="172" startOffset="8" endLine="196" endOffset="22"/></Target><Target id="@+id/nav_profile" view="LinearLayout"><Expressions/><location startLine="198" startOffset="8" endLine="222" endOffset="22"/></Target></Targets></Layout>
<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_second" modulePackage="com.example.myapplication" filePath="app\src\main\res\layout\fragment_second.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/fragment_second_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="378" endOffset="14"/></Target><Target id="@+id/logo_calendar" view="ImageView"><Expressions/><location startLine="19" startOffset="8" endLine="24" endOffset="49"/></Target><Target id="@+id/btn_calendar_today" view="ImageButton"><Expressions/><location startLine="36" startOffset="8" endLine="42" endOffset="49"/></Target><Target id="@+id/btn_prev_month" view="ImageButton"><Expressions/><location startLine="64" startOffset="12" endLine="70" endOffset="40"/></Target><Target id="@+id/tv_month_year" view="TextView"><Expressions/><location startLine="72" startOffset="12" endLine="81" endOffset="42"/></Target><Target id="@+id/btn_next_month" view="ImageButton"><Expressions/><location startLine="83" startOffset="12" endLine="89" endOffset="40"/></Target><Target id="@+id/tv_today" view="TextView"><Expressions/><location startLine="215" startOffset="12" endLine="224" endOffset="46"/></Target><Target id="@+id/recycler_calendar_tasks" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="281" startOffset="8" endLine="286" endOffset="57"/></Target><Target id="@+id/nav_tasks_calendar" view="LinearLayout"><Expressions/><location startLine="298" startOffset="8" endLine="322" endOffset="22"/></Target><Target id="@+id/nav_calendar_active" view="LinearLayout"><Expressions/><location startLine="324" startOffset="8" endLine="348" endOffset="22"/></Target><Target id="@+id/nav_profile_calendar" view="LinearLayout"><Expressions/><location startLine="350" startOffset="8" endLine="374" endOffset="22"/></Target></Targets></Layout>
1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.myapplication"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
8-->C:\Users\<USER>\AndroidStudioProjects\MyApplication\app\src\main\AndroidManifest.xml
9        android:targetSdkVersion="32" />
9-->C:\Users\<USER>\AndroidStudioProjects\MyApplication\app\src\main\AndroidManifest.xml
10
11    <application
11-->C:\Users\<USER>\AndroidStudioProjects\MyApplication\app\src\main\AndroidManifest.xml:5:5-30:19
12        android:allowBackup="true"
12-->C:\Users\<USER>\AndroidStudioProjects\MyApplication\app\src\main\AndroidManifest.xml:6:9-35
13        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
13-->[androidx.core:core:1.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\b0d84b0184661e10e9c8971206f8064b\transformed\core-1.7.0\AndroidManifest.xml:24:18-86
14        android:dataExtractionRules="@xml/data_extraction_rules"
14-->C:\Users\<USER>\AndroidStudioProjects\MyApplication\app\src\main\AndroidManifest.xml:7:9-65
15        android:debuggable="true"
16        android:fullBackupContent="@xml/backup_rules"
16-->C:\Users\<USER>\AndroidStudioProjects\MyApplication\app\src\main\AndroidManifest.xml:8:9-54
17        android:icon="@mipmap/ic_launcher"
17-->C:\Users\<USER>\AndroidStudioProjects\MyApplication\app\src\main\AndroidManifest.xml:9:9-43
18        android:label="@string/app_name"
18-->C:\Users\<USER>\AndroidStudioProjects\MyApplication\app\src\main\AndroidManifest.xml:10:9-41
19        android:roundIcon="@mipmap/ic_launcher_round"
19-->C:\Users\<USER>\AndroidStudioProjects\MyApplication\app\src\main\AndroidManifest.xml:11:9-54
20        android:supportsRtl="true"
20-->C:\Users\<USER>\AndroidStudioProjects\MyApplication\app\src\main\AndroidManifest.xml:12:9-35
21        android:testOnly="true"
22        android:theme="@style/Theme.MyApplication" >
22-->C:\Users\<USER>\AndroidStudioProjects\MyApplication\app\src\main\AndroidManifest.xml:13:9-51
23        <activity
23-->C:\Users\<USER>\AndroidStudioProjects\MyApplication\app\src\main\AndroidManifest.xml:15:9-29:20
24            android:name="com.example.myapplication.MainActivity"
24-->C:\Users\<USER>\AndroidStudioProjects\MyApplication\app\src\main\AndroidManifest.xml:16:13-41
25            android:exported="true"
25-->C:\Users\<USER>\AndroidStudioProjects\MyApplication\app\src\main\AndroidManifest.xml:17:13-36
26            android:label="@string/app_name"
26-->C:\Users\<USER>\AndroidStudioProjects\MyApplication\app\src\main\AndroidManifest.xml:18:13-45
27            android:theme="@style/Theme.MyApplication.NoActionBar" >
27-->C:\Users\<USER>\AndroidStudioProjects\MyApplication\app\src\main\AndroidManifest.xml:19:13-67
28            <intent-filter>
28-->C:\Users\<USER>\AndroidStudioProjects\MyApplication\app\src\main\AndroidManifest.xml:20:13-24:29
29                <action android:name="android.intent.action.MAIN" />
29-->C:\Users\<USER>\AndroidStudioProjects\MyApplication\app\src\main\AndroidManifest.xml:21:17-69
29-->C:\Users\<USER>\AndroidStudioProjects\MyApplication\app\src\main\AndroidManifest.xml:21:25-66
30
31                <category android:name="android.intent.category.LAUNCHER" />
31-->C:\Users\<USER>\AndroidStudioProjects\MyApplication\app\src\main\AndroidManifest.xml:23:17-77
31-->C:\Users\<USER>\AndroidStudioProjects\MyApplication\app\src\main\AndroidManifest.xml:23:27-74
32            </intent-filter>
33
34            <meta-data
34-->C:\Users\<USER>\AndroidStudioProjects\MyApplication\app\src\main\AndroidManifest.xml:26:13-28:36
35                android:name="android.app.lib_name"
35-->C:\Users\<USER>\AndroidStudioProjects\MyApplication\app\src\main\AndroidManifest.xml:27:17-52
36                android:value="" />
36-->C:\Users\<USER>\AndroidStudioProjects\MyApplication\app\src\main\AndroidManifest.xml:28:17-33
37        </activity>
38
39        <provider
39-->[androidx.emoji2:emoji2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\5c51388ef6023aa51428a217e7ac386b\transformed\jetified-emoji2-1.0.0\AndroidManifest.xml:26:9-34:20
40            android:name="androidx.startup.InitializationProvider"
40-->[androidx.emoji2:emoji2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\5c51388ef6023aa51428a217e7ac386b\transformed\jetified-emoji2-1.0.0\AndroidManifest.xml:27:13-67
41            android:authorities="com.example.myapplication.androidx-startup"
41-->[androidx.emoji2:emoji2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\5c51388ef6023aa51428a217e7ac386b\transformed\jetified-emoji2-1.0.0\AndroidManifest.xml:28:13-68
42            android:exported="false" >
42-->[androidx.emoji2:emoji2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\5c51388ef6023aa51428a217e7ac386b\transformed\jetified-emoji2-1.0.0\AndroidManifest.xml:29:13-37
43            <meta-data
43-->[androidx.emoji2:emoji2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\5c51388ef6023aa51428a217e7ac386b\transformed\jetified-emoji2-1.0.0\AndroidManifest.xml:31:13-33:52
44                android:name="androidx.emoji2.text.EmojiCompatInitializer"
44-->[androidx.emoji2:emoji2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\5c51388ef6023aa51428a217e7ac386b\transformed\jetified-emoji2-1.0.0\AndroidManifest.xml:32:17-75
45                android:value="androidx.startup" />
45-->[androidx.emoji2:emoji2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\5c51388ef6023aa51428a217e7ac386b\transformed\jetified-emoji2-1.0.0\AndroidManifest.xml:33:17-49
46            <meta-data
46-->[androidx.lifecycle:lifecycle-process:2.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\c9a10fea1164a8f27f0514a81c5996f7\transformed\jetified-lifecycle-process-2.4.0\AndroidManifest.xml:31:13-33:52
47                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
47-->[androidx.lifecycle:lifecycle-process:2.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\c9a10fea1164a8f27f0514a81c5996f7\transformed\jetified-lifecycle-process-2.4.0\AndroidManifest.xml:32:17-78
48                android:value="androidx.startup" />
48-->[androidx.lifecycle:lifecycle-process:2.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\c9a10fea1164a8f27f0514a81c5996f7\transformed\jetified-lifecycle-process-2.4.0\AndroidManifest.xml:33:17-49
49        </provider>
50
51        <uses-library
51-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\e1bc3a0075959d1d61cffec1a5625715\transformed\jetified-window-1.0.0\AndroidManifest.xml:25:9-27:40
52            android:name="androidx.window.extensions"
52-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\e1bc3a0075959d1d61cffec1a5625715\transformed\jetified-window-1.0.0\AndroidManifest.xml:26:13-54
53            android:required="false" />
53-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\e1bc3a0075959d1d61cffec1a5625715\transformed\jetified-window-1.0.0\AndroidManifest.xml:27:13-37
54        <uses-library
54-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\e1bc3a0075959d1d61cffec1a5625715\transformed\jetified-window-1.0.0\AndroidManifest.xml:28:9-30:40
55            android:name="androidx.window.sidecar"
55-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\e1bc3a0075959d1d61cffec1a5625715\transformed\jetified-window-1.0.0\AndroidManifest.xml:29:13-51
56            android:required="false" />
56-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\e1bc3a0075959d1d61cffec1a5625715\transformed\jetified-window-1.0.0\AndroidManifest.xml:30:13-37
57    </application>
58
59</manifest>

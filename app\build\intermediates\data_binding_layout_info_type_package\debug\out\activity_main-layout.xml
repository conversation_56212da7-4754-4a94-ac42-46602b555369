<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_main" modulePackage="com.example.myapplication" filePath="app\src\main\res\layout\activity_main.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.coordinatorlayout.widget.CoordinatorLayout"><Targets><Target tag="layout/activity_main_0" view="androidx.coordinatorlayout.widget.CoordinatorLayout"><Expressions/><location startLine="1" startOffset="0" endLine="56" endOffset="53"/></Target><Target tag="layout/activity_main_0" include="content_main"><Expressions/><location startLine="36" startOffset="4" endLine="36" endOffset="44"/></Target><Target id="@+id/toolbar" view="androidx.appcompat.widget.Toolbar"><Expressions/><location startLine="13" startOffset="8" endLine="18" endOffset="70"/></Target><Target id="@+id/toolbar" view="androidx.appcompat.widget.Toolbar"><Expressions/><location startLine="27" startOffset="8" endLine="32" endOffset="70"/></Target><Target id="@+id/fab" view="com.google.android.material.floatingactionbutton.FloatingActionButton"><Expressions/><location startLine="38" startOffset="4" endLine="45" endOffset="59"/></Target><Target id="@+id/fab" view="com.google.android.material.floatingactionbutton.FloatingActionButton"><Expressions/><location startLine="47" startOffset="4" endLine="54" endOffset="59"/></Target></Targets></Layout>
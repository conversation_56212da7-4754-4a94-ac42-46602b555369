com.example.myapplication:styleable/LinearLayoutCompat = 0x7f12004d
com.example.myapplication:styleable/NavHost = 0x7f120070
com.example.myapplication:drawable/ic_clock_black_24dp = 0x7f070065
com.example.myapplication:style/ThemeOverlay.Material3.MaterialCalendar = 0x7f11026c
com.example.myapplication:id/btn_next_month = 0x7f08020a
com.example.myapplication:style/Widget.Material3.FloatingActionButton.Primary = 0x7f11033c
com.example.myapplication:id/logo_calendar = 0x7f08020c
com.example.myapplication:style/Widget.MaterialComponents.MaterialCalendar.Day = 0x7f1103b7
com.example.myapplication:styleable/KeyAttribute = 0x7f120044
com.example.myapplication:style/Widget.AppCompat.CompoundButton.CheckBox = 0x7f1102b6
com.example.myapplication:style/Theme.MaterialComponents.NoActionBar.Bridge = 0x7f11023c
com.example.myapplication:styleable/RangeSlider = 0x7f12007e
com.example.myapplication:style/TextAppearance.AppCompat.Display1 = 0x7f110177
com.example.myapplication:style/Widget.Material3.MaterialCalendar.MonthNavigationButton = 0x7f110352
com.example.myapplication:style/Widget.MaterialComponents.Toolbar.Surface = 0x7f1103fb
com.example.myapplication:styleable/SplitPairRule = 0x7f12008b
com.example.myapplication:style/Base.V21.Theme.MaterialComponents.Dialog = 0x7f11009c
com.example.myapplication:styleable/KeyFramesVelocity = 0x7f120048
com.example.myapplication:style/Base.TextAppearance.AppCompat.Small.Inverse = 0x7f110029
com.example.myapplication:style/Widget.AppCompat.SearchView = 0x7f1102e0
com.example.myapplication:style/Theme.Material3.Light.Dialog.Alert = 0x7f110208
com.example.myapplication:styleable/AlertDialog = 0x7f120009
com.example.myapplication:style/Base.Widget.AppCompat.DrawerArrowToggle = 0x7f1100cd
com.example.myapplication:style/ThemeOverlay.Material3.MaterialCalendar.Fullscreen = 0x7f11026d
com.example.myapplication:layout/abc_dialog_title_material = 0x7f0b000c
com.example.myapplication:style/Widget.AppCompat.Light.Spinner.DropDown.ActionBar = 0x7f1102d2
com.example.myapplication:style/MaterialAlertDialog.Material3.Title.Icon.CenterStacked = 0x7f110119
com.example.myapplication:string/m3_sys_typescale_body_large_font = 0x7f100042
com.example.myapplication:dimen/mtrl_fab_elevation = 0x7f0601d3
com.example.myapplication:styleable/MotionLayout = 0x7f120069
com.example.myapplication:styleable/ClockHandView = 0x7f120024
com.example.myapplication:style/AndroidThemeColorAccentYellow = 0x7f110002
com.example.myapplication:styleable/AppBarLayout = 0x7f12000d
com.example.myapplication:style/Widget.MaterialComponents.Button.OutlinedButton.Icon = 0x7f110398
com.example.myapplication:style/Widget.MaterialComponents.Button.OutlinedButton = 0x7f110397
com.example.myapplication:style/Widget.Design.TextInputEditText = 0x7f1102f7
com.example.myapplication:dimen/m3_sys_typescale_headline_small_letter_spacing = 0x7f06012c
com.example.myapplication:style/Base.Widget.Material3.TabLayout.OnSurface = 0x7f110102
com.example.myapplication:style/Widget.MaterialComponents.CircularProgressIndicator.Medium = 0x7f1103aa
com.example.myapplication:style/Base.Widget.AppCompat.Button.Borderless = 0x7f1100c3
com.example.myapplication:string/category_personal = 0x7f100024
com.example.myapplication:style/Base.Theme.AppCompat.Light.DarkActionBar = 0x7f110054
com.example.myapplication:styleable/MaterialCardView = 0x7f120058
com.example.myapplication:style/Base.Widget.AppCompat.RatingBar = 0x7f1100e4
com.example.myapplication:style/Widget.MaterialComponents.MaterialCalendar.DayOfWeekLabel = 0x7f1103bb
com.example.myapplication:style/ThemeOverlay.AppCompat.Dialog.Alert = 0x7f110248
com.example.myapplication:style/Base.TextAppearance.AppCompat.Caption = 0x7f110017
com.example.myapplication:style/Widget.MaterialComponents.PopupMenu.Overflow = 0x7f1103d7
com.example.myapplication:style/Widget.MaterialComponents.BottomNavigationView = 0x7f110390
com.example.myapplication:style/Widget.Material3.MaterialDivider = 0x7f110358
com.example.myapplication:style/ShapeAppearanceOverlay.Material3.Button = 0x7f110157
com.example.myapplication:id/ghost_view = 0x7f0800d3
com.example.myapplication:styleable/View = 0x7f12009f
com.example.myapplication:style/Widget.MaterialComponents.AutoCompleteTextView.FilledBox.Dense = 0x7f110389
com.example.myapplication:style/TextAppearance.Design.Prefix = 0x7f1101ae
com.example.myapplication:style/Widget.Design.NavigationView = 0x7f1102f3
com.example.myapplication:styleable/OnClick = 0x7f120078
com.example.myapplication:style/Widget.Material3.CompoundButton.Switch = 0x7f11032e
com.example.myapplication:style/Widget.Compat.NotificationActionContainer = 0x7f1102ec
com.example.myapplication:styleable/Variant = 0x7f12009e
com.example.myapplication:string/abc_capital_off = 0x7f100006
com.example.myapplication:style/Theme.MaterialComponents.DayNight.Dialog.FixedSize = 0x7f110219
com.example.myapplication:styleable/MaterialAlertDialogTheme = 0x7f120052
com.example.myapplication:style/Widget.MaterialComponents.CircularProgressIndicator = 0x7f1103a8
com.example.myapplication:style/Base.TextAppearance.AppCompat = 0x7f110013
com.example.myapplication:style/Widget.Material3.ExtendedFloatingActionButton.Surface = 0x7f110336
com.example.myapplication:style/Widget.Material3.ChipGroup = 0x7f110324
com.example.myapplication:style/Widget.Material3.MaterialTimePicker.ImageButton = 0x7f110362
com.example.myapplication:style/Widget.Material3.MaterialTimePicker.Display = 0x7f11035d
com.example.myapplication:style/Widget.Material3.MaterialCalendar.Day.Selected = 0x7f110345
com.example.myapplication:style/MaterialAlertDialog.Material3.Title.Text = 0x7f11011c
com.example.myapplication:style/Widget.MaterialComponents.Button.TextButton.Icon = 0x7f11039d
com.example.myapplication:dimen/mtrl_shape_corner_size_large_component = 0x7f060200
com.example.myapplication:styleable/DialogFragmentNavigator = 0x7f120032
com.example.myapplication:color/m3_textfield_filled_background_color = 0x7f05018a
com.example.myapplication:style/Widget.Material3.Button.ElevatedButton.Icon = 0x7f110308
com.example.myapplication:style/ThemeOverlay.MaterialComponents.TextInputEditText = 0x7f110297
com.example.myapplication:style/Widget.Material3.FloatingActionButton.Surface = 0x7f11033e
com.example.myapplication:style/Theme.MaterialComponents.DayNight.NoActionBar = 0x7f11021e
com.example.myapplication:dimen/abc_dropdownitem_text_padding_left = 0x7f06002a
com.example.myapplication:style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.Dense.ExposedDropdownMenu = 0x7f1103eb
com.example.myapplication:style/Widget.MaterialComponents.NavigationRailView.Colored.Compact = 0x7f1103d0
com.example.myapplication:styleable/SwitchMaterial = 0x7f120092
com.example.myapplication:interpolator/mtrl_linear = 0x7f0a0009
com.example.myapplication:styleable/Slider = 0x7f120086
com.example.myapplication:string/mtrl_picker_announce_current_selection = 0x7f10006b
com.example.myapplication:style/Widget.Material3.FloatingActionButton.Secondary = 0x7f11033d
com.example.myapplication:style/Widget.Material3.CollapsingToolbar = 0x7f110329
com.example.myapplication:style/Base.Widget.MaterialComponents.Snackbar = 0x7f11010d
com.example.myapplication:styleable/ButtonBarLayout = 0x7f12001b
com.example.myapplication:style/Base.V7.Widget.AppCompat.AutoCompleteTextView = 0x7f1100b4
com.example.myapplication:style/Widget.MaterialComponents.TabLayout.Colored = 0x7f1103df
com.example.myapplication:color/m3_tonal_button_ripple_color_selector = 0x7f050199
com.example.myapplication:style/TextAppearance.AppCompat.Body1 = 0x7f110173
com.example.myapplication:style/Theme.MaterialComponents.DayNight.Dialog.Alert.Bridge = 0x7f110217
com.example.myapplication:style/ThemeOverlay.MaterialComponents.ActionBar.Surface = 0x7f11027d
com.example.myapplication:style/Theme.MaterialComponents.Light = 0x7f110229
com.example.myapplication:layout/item_calendar_task = 0x7f0b0079
com.example.myapplication:style/Widget.Material3.PopupMenu.ListPopupWindow = 0x7f110368
com.example.myapplication:color/material_dynamic_neutral95 = 0x7f0501ac
com.example.myapplication:style/Base.Theme.MaterialComponents.Light.DarkActionBar.Bridge = 0x7f11006c
com.example.myapplication:color/m3_primary_text_disable_only = 0x7f05008c
com.example.myapplication:style/Widget.Material3.MaterialTimePicker.Display.TextInputEditText = 0x7f110360
com.example.myapplication:style/Widget.MaterialComponents.NavigationView = 0x7f1103d3
com.example.myapplication:dimen/mtrl_progress_circular_size_medium = 0x7f0601f9
com.example.myapplication:style/Base.V24.Theme.Material3.Light.Dialog = 0x7f1100a9
com.example.myapplication:style/TestThemeWithLineHeightDisabled = 0x7f110171
com.example.myapplication:color/ripple_material_dark = 0x7f05023f
com.example.myapplication:string/second_fragment_label = 0x7f100097
com.example.myapplication:style/Test.Widget.MaterialComponents.MaterialCalendar = 0x7f110169
com.example.myapplication:style/ThemeOverlay.Material3 = 0x7f11024b
com.example.myapplication:style/ThemeOverlay.Material3.ActionBar = 0x7f11024c
com.example.myapplication:style/Widget.MaterialComponents.ActionBar.Solid = 0x7f110383
com.example.myapplication:id/parentPanel = 0x7f08015c
com.example.myapplication:string/mtrl_picker_date_header_unselected = 0x7f100070
com.example.myapplication:style/Theme.MaterialComponents.Dialog.MinWidth = 0x7f110226
com.example.myapplication:style/Widget.Material3.Button.OutlinedButton.Icon = 0x7f11030c
com.example.myapplication:style/Widget.MaterialComponents.Snackbar = 0x7f1103db
com.example.myapplication:style/Base.ThemeOverlay.Material3.AutoCompleteTextView = 0x7f11007a
com.example.myapplication:style/Widget.MaterialComponents.Slider = 0x7f1103da
com.example.myapplication:styleable/BottomSheetBehavior_Layout = 0x7f12001a
com.example.myapplication:style/Theme.Material3.Light.NoActionBar = 0x7f11020b
com.example.myapplication:style/TextAppearance.Design.HelperText = 0x7f1101ab
com.example.myapplication:style/Widget.AppCompat.Light.ActionBar = 0x7f1102bd
com.example.myapplication:layout/material_time_chip = 0x7f0b003e
com.example.myapplication:style/Widget.Compat.NotificationActionText = 0x7f1102ed
com.example.myapplication:xml/standalone_badge_gravity_bottom_start = 0x7f130004
com.example.myapplication:layout/mtrl_picker_actions = 0x7f0b0056
com.example.myapplication:styleable/DrawerLayout = 0x7f120034
com.example.myapplication:styleable/FlowLayout = 0x7f120039
com.example.myapplication:style/ThemeOverlay.Material3.DynamicColors.Light = 0x7f110263
com.example.myapplication:style/Theme.AppCompat.CompactMenu = 0x7f1101d9
com.example.myapplication:style/Widget.MaterialComponents.AppBarLayout.PrimarySurface = 0x7f110386
com.example.myapplication:style/Theme.Material3.DynamicColors.DayNight = 0x7f110203
com.example.myapplication:xml/data_extraction_rules = 0x7f130001
com.example.myapplication:string/task_manager_title = 0x7f10009d
com.example.myapplication:style/Widget.MaterialComponents.TextInputEditText.FilledBox.Dense = 0x7f1103e2
com.example.myapplication:style/ThemeOverlay.Material3.Button.TextButton = 0x7f110256
com.example.myapplication:style/TextAppearance.Material3.ActionBar.Subtitle = 0x7f1101b2
com.example.myapplication:style/Base.TextAppearance.AppCompat.Display4 = 0x7f11001b
com.example.myapplication:style/Widget.Material3.Button.TonalButton.Icon = 0x7f110314
com.example.myapplication:style/Widget.AppCompat.Light.ActionBar.TabText.Inverse = 0x7f1102c3
com.example.myapplication:layout/mtrl_calendar_days_of_week = 0x7f0b004b
com.example.myapplication:style/Base.TextAppearance.Material3.LabelLarge = 0x7f110040
com.example.myapplication:styleable/AnimatedStateListDrawableTransition = 0x7f12000c
com.example.myapplication:style/Widget.Material3.CardView.Elevated = 0x7f110316
com.example.myapplication:id/checkbox = 0x7f08007d
com.example.myapplication:id/spread_inside = 0x7f0801a2
com.example.myapplication:style/Widget.AppCompat.Light.DropDownItem.Spinner = 0x7f1102cc
com.example.myapplication:style/ThemeOverlay.MaterialComponents.Light = 0x7f11028c
com.example.myapplication:style/ThemeOverlay.AppCompat.Light = 0x7f110249
com.example.myapplication:color/m3_sys_color_light_error = 0x7f05016c
com.example.myapplication:layout/mtrl_calendar_months = 0x7f0b0050
com.example.myapplication:style/ThemeOverlay.MaterialComponents.Dialog.Alert.Framework = 0x7f11028b
com.example.myapplication:style/Theme.MaterialComponents.Light.DarkActionBar = 0x7f11022d
com.example.myapplication:style/Widget.MaterialComponents.Chip.Filter = 0x7f1103a6
com.example.myapplication:dimen/m3_navigation_rail_item_active_indicator_width = 0x7f060103
com.example.myapplication:style/Widget.AppCompat.ActionButton = 0x7f1102a8
com.example.myapplication:style/Base.Widget.MaterialComponents.PopupMenu.Overflow = 0x7f11010b
com.example.myapplication:color/m3_ref_palette_error10 = 0x7f0500d0
com.example.myapplication:string/abc_activitychooserview_choose_application = 0x7f100005
com.example.myapplication:animator/mtrl_extended_fab_change_size_collapse_motion_spec = 0x7f020017
com.example.myapplication:string/mtrl_exceed_max_badge_number_suffix = 0x7f100068
com.example.myapplication:style/Widget.Material3.MaterialCalendar.Year.Today = 0x7f110356
com.example.myapplication:style/Widget.Material3.Chip.Input = 0x7f11031e
com.example.myapplication:style/Widget.Material3.PopupMenu = 0x7f110366
com.example.myapplication:style/TextAppearance.AppCompat.Widget.ActionMode.Subtitle = 0x7f110194
com.example.myapplication:style/Widget.Material3.MaterialCalendar.Year = 0x7f110354
com.example.myapplication:xml/standalone_badge_offset = 0x7f130006
com.example.myapplication:id/neverCompleteToStart = 0x7f08014a
com.example.myapplication:style/Widget.Material3.MaterialCalendar.HeaderSelection = 0x7f11034d
com.example.myapplication:string/mtrl_badge_numberless_content_description = 0x7f100065
com.example.myapplication:styleable/CoordinatorLayout = 0x7f12002f
com.example.myapplication:style/TextAppearance.AppCompat.Display4 = 0x7f11017a
com.example.myapplication:style/Widget.Material3.TextInputLayout.OutlinedBox = 0x7f110379
com.example.myapplication:style/Base.TextAppearance.AppCompat.Light.Widget.PopupMenu.Small = 0x7f110021
com.example.myapplication:style/ThemeOverlay.Material3.MaterialAlertDialog = 0x7f11026a
com.example.myapplication:styleable/LinearProgressIndicator = 0x7f12004f
com.example.myapplication:styleable/NavigationBarActiveIndicator = 0x7f120073
com.example.myapplication:style/Theme.Material3.Dark = 0x7f1101f4
com.example.myapplication:style/Widget.Material3.Chip.Input.Icon.Elevated = 0x7f110321
com.example.myapplication:style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.Dense = 0x7f1103ea
com.example.myapplication:style/Base.Widget.Material3.MaterialCalendar.NavigationButton = 0x7f1100ff
com.example.myapplication:string/mtrl_picker_range_header_only_end_selected = 0x7f100078
com.example.myapplication:style/Widget.MaterialComponents.Button.TextButton.Snackbar = 0x7f11039e
com.example.myapplication:styleable/KeyFramesAcceleration = 0x7f120047
com.example.myapplication:style/Widget.AppCompat.Light.ActionBar.TabView.Inverse = 0x7f1102c5
com.example.myapplication:id/chip_group = 0x7f080083
com.example.myapplication:layout/notification_template_part_time = 0x7f0b0065
com.example.myapplication:string/chip_text = 0x7f100029
com.example.myapplication:dimen/abc_control_corner_material = 0x7f060018
com.example.myapplication:dimen/abc_button_inset_vertical_material = 0x7f060013
com.example.myapplication:string/exposed_dropdown_menu_content_description = 0x7f10002f
com.example.myapplication:style/Theme.MaterialComponents = 0x7f11020c
com.example.myapplication:id/mtrl_calendar_year_selector_frame = 0x7f08012a
com.example.myapplication:style/Widget.AppCompat.CompoundButton.Switch = 0x7f1102b8
com.example.myapplication:attr/waveOffset = 0x7f03045c
com.example.myapplication:styleable/MenuView = 0x7f120063
com.example.myapplication:drawable/abc_btn_switch_to_on_mtrl_00001 = 0x7f070014
com.example.myapplication:style/Widget.AppCompat.Toolbar.Button.Navigation = 0x7f1102eb
com.example.myapplication:style/Theme.AppCompat.Light.Dialog.MinWidth = 0x7f1101ea
com.example.myapplication:style/MaterialAlertDialog.MaterialComponents.Title.Panel.CenterStacked = 0x7f110125
com.example.myapplication:styleable/AppCompatTheme = 0x7f120015
com.example.myapplication:drawable/abc_list_selector_background_transition_holo_dark = 0x7f070031
com.example.myapplication:id/never = 0x7f080148
com.example.myapplication:style/Widget.AppCompat.CompoundButton.RadioButton = 0x7f1102b7
com.example.myapplication:style/ThemeOverlay.Material3.Snackbar = 0x7f110272
com.example.myapplication:style/Widget.Material3.ExtendedFloatingActionButton.Icon.Secondary = 0x7f110331
com.example.myapplication:string/m3_ref_typeface_brand_medium = 0x7f100039
com.example.myapplication:styleable/MaterialButtonToggleGroup = 0x7f120055
com.example.myapplication:style/Base.TextAppearance.AppCompat.Widget.PopupMenu.Header = 0x7f11003b
com.example.myapplication:style/Widget.Material3.Button.TextButton = 0x7f11030d
com.example.myapplication:drawable/abc_action_bar_item_background_material = 0x7f070008
com.example.myapplication:style/Base.TextAppearance.AppCompat.Medium.Inverse = 0x7f110023
com.example.myapplication:dimen/abc_action_button_min_width_material = 0x7f06000e
com.example.myapplication:style/ThemeOverlay.MaterialComponents.TextInputEditText.FilledBox.Dense = 0x7f110299
com.example.myapplication:id/outline = 0x7f080156
com.example.myapplication:style/Widget.MaterialComponents.MaterialCalendar.Year.Today = 0x7f1103cb
com.example.myapplication:dimen/abc_dialog_corner_radius_material = 0x7f06001b
com.example.myapplication:layout/mtrl_picker_header_dialog = 0x7f0b0059
com.example.myapplication:integer/mtrl_view_gone = 0x7f09002a
com.example.myapplication:style/Widget.MaterialComponents.BottomSheet.Modal = 0x7f110394
com.example.myapplication:styleable/ActionBarLayout = 0x7f120001
com.example.myapplication:style/ThemeOverlay.MaterialComponents.MaterialAlertDialog.Picker.Date = 0x7f110290
com.example.myapplication:style/Base.Widget.AppCompat.CompoundButton.RadioButton = 0x7f1100cb
com.example.myapplication:style/Widget.Material3.TextInputLayout.OutlinedBox.ExposedDropdownMenu = 0x7f11037c
com.example.myapplication:id/east = 0x7f0800bb
com.example.myapplication:dimen/mtrl_btn_letter_spacing = 0x7f06017e
com.example.myapplication:color/m3_ref_palette_tertiary50 = 0x7f050116
com.example.myapplication:drawable/design_fab_background = 0x7f070060
com.example.myapplication:style/Base.V7.Theme.AppCompat = 0x7f1100af
com.example.myapplication:drawable/abc_switch_track_mtrl_alpha = 0x7f070049
com.example.myapplication:id/SHIFT = 0x7f080008
com.example.myapplication:string/status_pending = 0x7f10009b
com.example.myapplication:style/Widget.Material3.BottomAppBar = 0x7f110301
com.example.myapplication:styleable/MenuGroup = 0x7f120061
com.example.myapplication:styleable/StateListDrawableItem = 0x7f12008f
com.example.myapplication:style/Widget.Material3.Button.TextButton.Icon = 0x7f110311
com.example.myapplication:style/ThemeOverlay.Material3.DayNight.BottomSheetDialog = 0x7f11025d
com.example.myapplication:color/m3_sys_color_dynamic_light_on_primary_container = 0x7f05015b
com.example.myapplication:id/actionDownUp = 0x7f080033
com.example.myapplication:style/ThemeOverlay.AppCompat.Dark.ActionBar = 0x7f110244
com.example.myapplication:color/m3_textfield_label_color = 0x7f05018d
com.example.myapplication:style/Theme.Material3.Light.BottomSheetDialog = 0x7f110206
com.example.myapplication:style/Widget.MaterialComponents.Toolbar.PrimarySurface = 0x7f1103fa
com.example.myapplication:style/ThemeOverlay.Material3.Dark.ActionBar = 0x7f11025c
com.example.myapplication:style/TextAppearance.MaterialComponents.Headline5 = 0x7f1101ce
com.example.myapplication:style/Widget.Material3.Button.OutlinedButton = 0x7f11030b
com.example.myapplication:style/Base.V14.Theme.Material3.Light.Dialog = 0x7f110088
com.example.myapplication:style/Widget.Material3.Button.TextButton.Dialog = 0x7f11030e
com.example.myapplication:style/ThemeOverlay.Material3.TextInputEditText = 0x7f110273
com.example.myapplication:id/accessibility_custom_action_13 = 0x7f080017
com.example.myapplication:attr/yearTodayStyle = 0x7f03046d
com.example.myapplication:style/Theme.MaterialComponents.Light.Dialog.Bridge = 0x7f110232
com.example.myapplication:style/Base.V14.Theme.MaterialComponents.Light.Dialog.Bridge = 0x7f110091
com.example.myapplication:layout/material_clockface_view = 0x7f0b003b
com.example.myapplication:dimen/mtrl_progress_circular_track_thickness_small = 0x7f0601fd
com.example.myapplication:styleable/CardView = 0x7f12001d
com.example.myapplication:id/item3 = 0x7f0800f0
com.example.myapplication:style/Base.AlertDialog.AppCompat = 0x7f110008
com.example.myapplication:style/Base.Theme.AppCompat.Light = 0x7f110053
com.example.myapplication:dimen/m3_sys_typescale_label_medium_text_size = 0x7f060131
com.example.myapplication:id/mtrl_calendar_days_of_week = 0x7f080124
com.example.myapplication:style/TextAppearance.AppCompat.Tooltip = 0x7f11018e
com.example.myapplication:style/Test.Widget.MaterialComponents.MaterialCalendar.Day = 0x7f11016a
com.example.myapplication:style/ThemeOverlay.Material3.BottomAppBar = 0x7f110252
com.example.myapplication:style/Widget.MaterialComponents.TimePicker.ImageButton.ShapeAppearance = 0x7f1103f7
com.example.myapplication:mipmap/ic_launcher_round = 0x7f0d0001
com.example.myapplication:style/Theme.Material3.DayNight.Dialog.Alert = 0x7f1101fe
com.example.myapplication:style/ShapeAppearance.MaterialComponents.LargeComponent = 0x7f11014d
com.example.myapplication:style/Theme.MaterialComponents.NoActionBar = 0x7f11023b
com.example.myapplication:styleable/ShapeAppearance = 0x7f120084
com.example.myapplication:style/Widget.Material3.Snackbar.TextView = 0x7f11036d
com.example.myapplication:style/ThemeOverlay.AppCompat.Dialog = 0x7f110247
com.example.myapplication:style/Widget.MaterialComponents.MaterialCalendar.MonthNavigationButton = 0x7f1103c7
com.example.myapplication:style/Theme.MaterialComponents.Dialog.Bridge = 0x7f110223
com.example.myapplication:style/Widget.MaterialComponents.AutoCompleteTextView.OutlinedBox = 0x7f11038a
com.example.myapplication:style/ShapeAppearance.Material3.NavigationBarView.ActiveIndicator = 0x7f110149
com.example.myapplication:attr/splitLayoutDirection = 0x7f030378
com.example.myapplication:style/Theme.MaterialComponents.Light.Dialog.MinWidth.Bridge = 0x7f110236
com.example.myapplication:style/Test.ShapeAppearanceOverlay.MaterialComponents.MaterialCalendar.Day = 0x7f110167
com.example.myapplication:xml/standalone_badge_gravity_top_start = 0x7f130005
com.example.myapplication:color/m3_card_foreground_color = 0x7f050069
com.example.myapplication:style/Base.Theme.AppCompat.Dialog.Alert = 0x7f11004f
com.example.myapplication:style/Widget.AppCompat.AutoCompleteTextView = 0x7f1102ad
com.example.myapplication:id/nav_profile_calendar = 0x7f08020e
com.example.myapplication:style/Base.Widget.AppCompat.Button.Borderless.Colored = 0x7f1100c4
com.example.myapplication:dimen/m3_alert_dialog_icon_size = 0x7f0600a5
com.example.myapplication:style/ThemeOverlay.Material3.AutoCompleteTextView.OutlinedBox = 0x7f110250
com.example.myapplication:style/Base.Theme.MaterialComponents.Dialog.FixedSize = 0x7f110066
com.example.myapplication:style/Base.Theme.MaterialComponents.Light.DialogWhenLarge = 0x7f110072
com.example.myapplication:style/Widget.MaterialComponents.Chip.Choice = 0x7f1103a4
com.example.myapplication:styleable/AppCompatSeekBar = 0x7f120012
com.example.myapplication:style/Base.V14.ThemeOverlay.MaterialComponents.Dialog = 0x7f110094
com.example.myapplication:style/Widget.MaterialComponents.MaterialCalendar.Item = 0x7f1103c6
com.example.myapplication:style/Base.V14.ThemeOverlay.MaterialComponents.BottomSheetDialog = 0x7f110093
com.example.myapplication:style/Widget.AppCompat.Button.Small = 0x7f1102b3
com.example.myapplication:id/time = 0x7f0801d7
com.example.myapplication:layout/mtrl_alert_dialog_actions = 0x7f0b0044
com.example.myapplication:style/Base.Widget.AppCompat.TextView = 0x7f1100ed
com.example.myapplication:style/Base.V22.Theme.AppCompat = 0x7f1100a2
com.example.myapplication:string/m3_sys_motion_easing_emphasized = 0x7f10003f
com.example.myapplication:interpolator/btn_checkbox_checked_mtrl_animation_interpolator_0 = 0x7f0a0000
com.example.myapplication:style/Base.Theme.Material3.Dark = 0x7f11005a
com.example.myapplication:integer/mtrl_tab_indicator_anim_duration_ms = 0x7f090029
com.example.myapplication:string/m3_sys_typescale_display_medium_font = 0x7f100046
com.example.myapplication:style/Widget.Material3.AutoCompleteTextView.OutlinedBox.Dense = 0x7f1102ff
com.example.myapplication:style/TextAppearance.Design.Tab = 0x7f1101b1
com.example.myapplication:style/TextAppearance.AppCompat.Widget.ActionBar.Title = 0x7f110192
com.example.myapplication:style/Base.TextAppearance.AppCompat.Widget.ActionMode.Title = 0x7f110035
com.example.myapplication:style/Widget.Material3.Chip.Suggestion.Elevated = 0x7f110323
com.example.myapplication:id/META = 0x7f080006
com.example.myapplication:style/Widget.Material3.BottomSheet.Modal = 0x7f110305
com.example.myapplication:color/m3_ref_palette_dynamic_neutral_variant70 = 0x7f0500a3
com.example.myapplication:style/Theme.Material3.Dark.DialogWhenLarge = 0x7f1101f9
com.example.myapplication:dimen/notification_big_circle_margin = 0x7f060226
com.example.myapplication:styleable/Tooltip = 0x7f12009b
com.example.myapplication:style/Theme.AppCompat.Dialog.MinWidth = 0x7f1101e3
com.example.myapplication:attr/transitionShapeAppearance = 0x7f030447
com.example.myapplication:style/Theme.Material3.Dark.Dialog.MinWidth = 0x7f1101f8
com.example.myapplication:dimen/material_time_picker_minimum_screen_width = 0x7f06015f
com.example.myapplication:style/ThemeOverlay.AppCompat.Dark = 0x7f110243
com.example.myapplication:attr/textAppearanceBodyLarge = 0x7f0303cd
com.example.myapplication:drawable/mtrl_navigation_bar_item_background = 0x7f070088
com.example.myapplication:style/Base.Widget.AppCompat.ActionBar = 0x7f1100b7
com.example.myapplication:style/Theme.AppCompat.Light.Dialog = 0x7f1101e8
com.example.myapplication:style/TextAppearance.AppCompat.SearchResult.Title = 0x7f110187
com.example.myapplication:dimen/abc_button_padding_horizontal_material = 0x7f060014
com.example.myapplication:style/Theme.MaterialComponents.DayNight.Dialog = 0x7f110215
com.example.myapplication:id/tv_calendar_task_category = 0x7f080211
com.example.myapplication:style/TextAppearance.MaterialComponents.Subtitle1 = 0x7f1101d1
com.example.myapplication:string/mtrl_picker_date_header_title = 0x7f10006f
com.example.myapplication:drawable/notification_action_background = 0x7f07008d
com.example.myapplication:id/dragDown = 0x7f0800b1
com.example.myapplication:style/Theme.AppCompat.Light.DarkActionBar = 0x7f1101e7
com.example.myapplication:styleable/ConstraintLayout_ReactiveGuide = 0x7f12002b
com.example.myapplication:style/Widget.MaterialComponents.TextInputLayout.FilledBox = 0x7f1103e5
com.example.myapplication:style/Widget.AppCompat.ListView.DropDown = 0x7f1102d6
com.example.myapplication:style/Theme.MaterialComponents.DayNight.Bridge = 0x7f110212
com.example.myapplication:style/ThemeOverlay.MaterialComponents.TimePicker.Display.TextInputEditText = 0x7f11029e
com.example.myapplication:style/Widget.MaterialComponents.TextInputEditText.OutlinedBox.Dense = 0x7f1103e4
com.example.myapplication:style/MaterialAlertDialog.MaterialComponents = 0x7f11011e
com.example.myapplication:style/ThemeOverlay.Material3.BottomSheetDialog = 0x7f110253
com.example.myapplication:styleable/ActivityNavigator = 0x7f120007
com.example.myapplication:style/Base.TextAppearance.AppCompat.Display3 = 0x7f11001a
com.example.myapplication:style/Theme.AppCompat.Light = 0x7f1101e6
com.example.myapplication:styleable/PropertySet = 0x7f12007c
com.example.myapplication:style/ThemeOverlay.Material3.AutoCompleteTextView.FilledBox = 0x7f11024e
com.example.myapplication:style/Widget.MaterialComponents.CircularProgressIndicator.Small = 0x7f1103ab
com.example.myapplication:style/Widget.Material3.TextInputLayout.FilledBox.Dense.ExposedDropdownMenu = 0x7f110377
com.example.myapplication:style/Theme.AppCompat.DayNight.Dialog.MinWidth = 0x7f1101de
com.example.myapplication:style/Base.Widget.Design.TabLayout = 0x7f1100f1
com.example.myapplication:dimen/mtrl_bottomappbar_fab_cradle_rounded_corner_radius = 0x7f060171
com.example.myapplication:style/Widget.Material3.CircularProgressIndicator.ExtraSmall = 0x7f110326
com.example.myapplication:id/row_index_key = 0x7f080173
com.example.myapplication:style/Widget.MaterialComponents.TextInputLayout.OutlinedBox = 0x7f1103e9
com.example.myapplication:style/TextAppearance.Widget.AppCompat.Toolbar.Title = 0x7f1101d7
com.example.myapplication:drawable/abc_list_selector_disabled_holo_light = 0x7f070034
com.example.myapplication:string/task_detail = 0x7f10009c
com.example.myapplication:string/mtrl_picker_a11y_next_month = 0x7f100069
com.example.myapplication:string/m3_sys_typescale_title_large_font = 0x7f10004e
com.example.myapplication:style/Theme.MaterialComponents.DayNight.NoActionBar.Bridge = 0x7f11021f
com.example.myapplication:style/Theme.Material3.Light.DialogWhenLarge = 0x7f11020a
com.example.myapplication:style/TextAppearance.MaterialComponents.Subtitle2 = 0x7f1101d2
com.example.myapplication:id/bounce = 0x7f080064
com.example.myapplication:attr/cardBackgroundColor = 0x7f030088
com.example.myapplication:style/Widget.MaterialComponents.TextInputLayout.FilledBox.Dense = 0x7f1103e6
com.example.myapplication:color/material_dynamic_primary99 = 0x7f0501c7
com.example.myapplication:id/tv_task_description = 0x7f0801ef
com.example.myapplication:style/ThemeOverlay.Material3.MaterialTimePicker.Display.TextInputEditText = 0x7f110270
com.example.myapplication:dimen/m3_btn_elevation = 0x7f0600c5
com.example.myapplication:id/action_bar_container = 0x7f080039
com.example.myapplication:dimen/mtrl_card_checked_icon_margin = 0x7f0601b6
com.example.myapplication:style/TextAppearance.MaterialComponents.Headline6 = 0x7f1101cf
com.example.myapplication:style/TextAppearance.MaterialComponents.Overline = 0x7f1101d0
com.example.myapplication:dimen/abc_dropdownitem_text_padding_right = 0x7f06002b
com.example.myapplication:styleable/NavigationView = 0x7f120076
com.example.myapplication:style/TextAppearance.MaterialComponents.Headline1 = 0x7f1101ca
com.example.myapplication:style/Base.Widget.MaterialComponents.PopupMenu = 0x7f110108
com.example.myapplication:style/TextAppearance.MaterialComponents.Caption = 0x7f1101c8
com.example.myapplication:style/TextAppearance.AppCompat.Widget.PopupMenu.Header = 0x7f11019d
com.example.myapplication:style/ThemeOverlay.Material3.Toolbar.Surface = 0x7f110278
com.example.myapplication:style/TextAppearance.MaterialComponents.Body2 = 0x7f1101c6
com.example.myapplication:styleable/MaterialAlertDialog = 0x7f120051
com.example.myapplication:string/mtrl_picker_confirm = 0x7f10006d
com.example.myapplication:style/ThemeOverlay.Material3.FloatingActionButton.Primary = 0x7f110264
com.example.myapplication:style/Widget.Material3.CardView.Filled = 0x7f110317
com.example.myapplication:style/Theme.MaterialComponents.Dialog.MinWidth.Bridge = 0x7f110227
com.example.myapplication:dimen/material_clock_period_toggle_width = 0x7f060145
com.example.myapplication:attr/deltaPolarRadius = 0x7f030149
com.example.myapplication:style/Base.Widget.Material3.TabLayout = 0x7f110101
com.example.myapplication:style/Widget.Material3.Button.TonalButton = 0x7f110313
com.example.myapplication:style/TextAppearance.Material3.ActionBar.Title = 0x7f1101b3
com.example.myapplication:style/AlertDialog.AppCompat = 0x7f110000
com.example.myapplication:dimen/abc_config_prefDialogWidth = 0x7f060017
com.example.myapplication:id/clockwise = 0x7f080089
com.example.myapplication:style/Base.Widget.AppCompat.CompoundButton.Switch = 0x7f1100cc
com.example.myapplication:style/Base.Widget.Material3.Light.ActionBar.Solid = 0x7f1100fe
com.example.myapplication:dimen/mtrl_chip_text_size = 0x7f0601bd
com.example.myapplication:id/submenuarrow = 0x7f0801b1
com.example.myapplication:style/Widget.Material3.Button.IconButton = 0x7f11030a
com.example.myapplication:attr/thumbStrokeColor = 0x7f030411
com.example.myapplication:style/Widget.Material3.MaterialTimePicker.Display.HelperText = 0x7f11035f
com.example.myapplication:style/Widget.AppCompat.Button.Borderless = 0x7f1102af
com.example.myapplication:string/m3_ref_typeface_plain_medium = 0x7f10003b
com.example.myapplication:style/TextAppearance.Widget.AppCompat.ExpandedMenu.Item = 0x7f1101d5
com.example.myapplication:style/Widget.MaterialComponents.Button.TextButton.Dialog.Icon = 0x7f11039c
com.example.myapplication:drawable/material_ic_menu_arrow_up_black_24dp = 0x7f070081
com.example.myapplication:style/Base.TextAppearance.AppCompat.Widget.ActionBar.Subtitle.Inverse = 0x7f110031
com.example.myapplication:color/m3_dynamic_primary_text_disable_only = 0x7f050081
com.example.myapplication:dimen/mtrl_progress_circular_inset_extra_small = 0x7f0601f3
com.example.myapplication:id/mtrl_picker_text_input_range_end = 0x7f080135
com.example.myapplication:style/Widget.Material3.TextInputEditText.FilledBox.Dense = 0x7f110372
com.example.myapplication:style/Theme.AppCompat.DayNight.Dialog = 0x7f1101dc
com.example.myapplication:style/Widget.MaterialComponents.BottomAppBar.Colored = 0x7f11038e
com.example.myapplication:color/material_deep_teal_500 = 0x7f05019f
com.example.myapplication:style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.ExposedDropdownMenu = 0x7f1103ec
com.example.myapplication:string/mtrl_picker_invalid_format = 0x7f100072
com.example.myapplication:style/TextAppearance.Design.Counter = 0x7f1101a8
com.example.myapplication:style/TextAppearance.AppCompat.Widget.Switch = 0x7f1101a0
com.example.myapplication:styleable/ShapeableImageView = 0x7f120085
com.example.myapplication:style/Widget.MaterialComponents.MaterialCalendar.Day.Selected = 0x7f1103b9
com.example.myapplication:string/material_clock_display_divider = 0x7f100051
com.example.myapplication:styleable/AppCompatTextHelper = 0x7f120013
com.example.myapplication:style/TextAppearance.AppCompat.Widget.PopupMenu.Large = 0x7f11019e
com.example.myapplication:style/Widget.Material3.TextInputLayout.OutlinedBox.Dense.ExposedDropdownMenu = 0x7f11037b
com.example.myapplication:style/ThemeOverlay.Material3.AutoCompleteTextView = 0x7f11024d
com.example.myapplication:layout/mtrl_navigation_rail_item = 0x7f0b0055
com.example.myapplication:style/Base.Widget.MaterialComponents.AutoCompleteTextView = 0x7f110104
com.example.myapplication:style/TextAppearance.AppCompat.Title.Inverse = 0x7f11018d
com.example.myapplication:style/RtlOverlay.Widget.AppCompat.Search.DropDown = 0x7f11013f
com.example.myapplication:style/Widget.AppCompat.ButtonBar = 0x7f1102b4
com.example.myapplication:style/Widget.Material3.TextInputEditText.OutlinedBox.Dense = 0x7f110374
com.example.myapplication:id/split_action_bar = 0x7f0801a0
com.example.myapplication:style/Platform.Widget.AppCompat.Spinner = 0x7f110135
com.example.myapplication:style/Widget.Material3.CheckedTextView = 0x7f110319
com.example.myapplication:string/path_password_eye_mask_strike_through = 0x7f100091
com.example.myapplication:dimen/m3_fab_border_width = 0x7f0600ef
com.example.myapplication:attr/flow_lastHorizontalBias = 0x7f0301ba
com.example.myapplication:color/background_floating_material_light = 0x7f05001e
com.example.myapplication:color/mtrl_tabs_colored_ripple_color = 0x7f050226
com.example.myapplication:attr/contentPaddingRight = 0x7f030119
com.example.myapplication:style/Widget.Material3.DrawerLayout = 0x7f11032f
com.example.myapplication:color/material_dynamic_neutral100 = 0x7f0501a3
com.example.myapplication:style/ThemeOverlay.AppCompat = 0x7f110241
com.example.myapplication:drawable/abc_ab_share_pack_mtrl_alpha = 0x7f070007
com.example.myapplication:dimen/mtrl_card_checked_icon_size = 0x7f0601b7
com.example.myapplication:dimen/mtrl_navigation_item_horizontal_padding = 0x7f0601e4
com.example.myapplication:id/view_tree_saved_state_registry_owner = 0x7f0801fc
com.example.myapplication:style/TextAppearance.AppCompat.Medium.Inverse = 0x7f110184
com.example.myapplication:style/Widget.Material3.TabLayout = 0x7f11036e
com.example.myapplication:style/TextAppearance.AppCompat.Large = 0x7f11017d
com.example.myapplication:color/m3_sys_color_dynamic_light_surface = 0x7f050167
com.example.myapplication:style/Theme.MaterialComponents.DayNight.BottomSheetDialog = 0x7f110211
com.example.myapplication:attr/staggered = 0x7f030384
com.example.myapplication:dimen/abc_star_small = 0x7f06003d
com.example.myapplication:style/TextAppearance.Material3.MaterialTimePicker.Title = 0x7f1101c0
com.example.myapplication:style/Theme.MaterialComponents.DayNight.Dialog.Alert = 0x7f110216
com.example.myapplication:color/m3_radiobutton_ripple_tint = 0x7f05008d
com.example.myapplication:style/Base.Theme.AppCompat.Light.Dialog.FixedSize = 0x7f110057
com.example.myapplication:styleable/ConstraintLayout_placeholder = 0x7f12002c
com.example.myapplication:style/Base.V14.Theme.Material3.Light.BottomSheetDialog = 0x7f110087
com.example.myapplication:drawable/ic_m3_chip_checked_circle = 0x7f07006a
com.example.myapplication:attr/windowFixedHeightMinor = 0x7f030465
com.example.myapplication:styleable/MaterialTextView = 0x7f12005e
com.example.myapplication:style/Widget.Material3.ExtendedFloatingActionButton.Primary = 0x7f110334
com.example.myapplication:style/Widget.MaterialComponents.MaterialCalendar.HeaderSelection = 0x7f1103c2
com.example.myapplication:attr/switchStyle = 0x7f0303a7
com.example.myapplication:attr/dividerColor = 0x7f030151
com.example.myapplication:style/TestStyleWithLineHeight = 0x7f11016c
com.example.myapplication:style/Base.Widget.Material3.CompoundButton.Switch = 0x7f1100f9
com.example.myapplication:style/ShapeAppearanceOverlay.MaterialComponents.MaterialCalendar.Day = 0x7f110161
com.example.myapplication:id/action_bar = 0x7f080037
com.example.myapplication:id/mtrl_motion_snapshot_view = 0x7f08012e
com.example.myapplication:style/ShapeAppearanceOverlay.MaterialComponents.Chip = 0x7f11015e
com.example.myapplication:dimen/compat_button_padding_vertical_material = 0x7f06005a
com.example.myapplication:style/ShapeAppearanceOverlay.MaterialComponents.BottomSheet = 0x7f11015d
com.example.myapplication:id/material_textinput_timepicker = 0x7f080110
com.example.myapplication:style/ShapeAppearanceOverlay.Material3.TextField.Filled = 0x7f11015b
com.example.myapplication:style/ShapeAppearanceOverlay.Material3.FloatingActionButton = 0x7f110159
com.example.myapplication:style/ThemeOverlay.MaterialComponents.MaterialCalendar = 0x7f110295
com.example.myapplication:style/ShapeAppearanceOverlay.DifferentCornerSize = 0x7f110156
com.example.myapplication:styleable/RecycleListView = 0x7f12007f
com.example.myapplication:string/abc_menu_meta_shortcut_label = 0x7f10000d
com.example.myapplication:style/ShapeAppearanceOverlay = 0x7f110152
com.example.myapplication:style/ShapeAppearance.MaterialComponents.SmallComponent = 0x7f11014f
com.example.myapplication:style/ShapeAppearance.MaterialComponents = 0x7f11014c
com.example.myapplication:style/ShapeAppearance.Material3.MediumComponent = 0x7f110148
com.example.myapplication:style/TextAppearance.Material3.LabelSmall = 0x7f1101bf
com.example.myapplication:style/RtlUnderlay.Widget.AppCompat.ActionButton.Overflow = 0x7f110146
com.example.myapplication:color/material_dynamic_tertiary60 = 0x7f0501dc
com.example.myapplication:style/ShapeAppearanceOverlay.BottomRightCut = 0x7f110154
com.example.myapplication:style/Widget.MaterialComponents.BottomSheet = 0x7f110393
com.example.myapplication:style/Base.Widget.Material3.FloatingActionButton = 0x7f1100fc
com.example.myapplication:styleable/MaterialTimePicker = 0x7f12005f
com.example.myapplication:style/Widget.Design.TextInputLayout = 0x7f1102f8
com.example.myapplication:menu/example_menu2 = 0x7f0c0001
com.example.myapplication:attr/subtitle = 0x7f03039c
com.example.myapplication:style/Base.Theme.MaterialComponents.Light.Dialog.MinWidth = 0x7f110071
com.example.myapplication:style/Theme.MyApplication.AppBarOverlay = 0x7f11023e
com.example.myapplication:drawable/abc_list_selector_holo_light = 0x7f070036
com.example.myapplication:id/tag_accessibility_actions = 0x7f0801b5
com.example.myapplication:style/ThemeOverlay.Material3.Button.TonalButton = 0x7f110258
com.example.myapplication:style/Platform.MaterialComponents.Light = 0x7f11012c
com.example.myapplication:style/Widget.Material3.TabLayout.OnSurface = 0x7f11036f
com.example.myapplication:dimen/mtrl_btn_focused_z = 0x7f060179
com.example.myapplication:style/Base.Widget.Material3.ExtendedFloatingActionButton = 0x7f1100fa
com.example.myapplication:color/primary_text_default_material_light = 0x7f050238
com.example.myapplication:style/Widget.MaterialComponents.CompoundButton.RadioButton = 0x7f1103ae
com.example.myapplication:dimen/abc_action_bar_elevation_material = 0x7f060005
com.example.myapplication:styleable/CollapsingToolbarLayout_Layout = 0x7f120026
com.example.myapplication:dimen/m3_snackbar_action_text_color_alpha = 0x7f06010d
com.example.myapplication:color/abc_tint_edittext = 0x7f050015
com.example.myapplication:attr/headerLayout = 0x7f0301db
com.example.myapplication:id/south = 0x7f08019c
com.example.myapplication:id/save_non_transition_alpha = 0x7f080175
com.example.myapplication:id/mtrl_calendar_selection_frame = 0x7f080128
com.example.myapplication:interpolator/btn_checkbox_unchecked_mtrl_animation_interpolator_0 = 0x7f0a0002
com.example.myapplication:style/Widget.MaterialComponents.Button.UnelevatedButton.Icon = 0x7f1103a0
com.example.myapplication:style/MaterialAlertDialog.MaterialComponents.Title.Icon.CenterStacked = 0x7f110123
com.example.myapplication:style/MaterialAlertDialog.MaterialComponents.Picker.Date.Spinner = 0x7f110121
com.example.myapplication:style/Base.V21.Theme.AppCompat.Light = 0x7f110099
com.example.myapplication:style/Base.Theme.MaterialComponents.Light.Dialog.FixedSize = 0x7f110070
com.example.myapplication:style/Widget.Material3.FloatingActionButton.Tertiary = 0x7f11033f
com.example.myapplication:style/Base.Theme.Material3.Dark.Dialog = 0x7f11005c
com.example.myapplication:integer/bottom_sheet_slide_duration = 0x7f090003
com.example.myapplication:string/nav_app_bar_open_drawer_description = 0x7f10008a
com.example.myapplication:color/bright_foreground_material_light = 0x7f050027
com.example.myapplication:string/hide_bottom_view_on_scroll_behavior = 0x7f100035
com.example.myapplication:attr/checkMarkTint = 0x7f03009c
com.example.myapplication:style/Widget.AppCompat.Light.ListView.DropDown = 0x7f1102ce
com.example.myapplication:style/MaterialAlertDialog.Material3.Title.Panel.CenterStacked = 0x7f11011b
com.example.myapplication:interpolator/mtrl_fast_out_slow_in = 0x7f0a0008
com.example.myapplication:style/TextAppearance.Design.Snackbar.Message = 0x7f1101af
com.example.myapplication:id/rtl = 0x7f080174
com.example.myapplication:id/snapMargins = 0x7f08019b
com.example.myapplication:attr/viewInflaterClass = 0x7f030453
com.example.myapplication:style/Widget.Material3.NavigationRailView = 0x7f110363
com.example.myapplication:style/ThemeOverlay.Design.TextInputEditText = 0x7f11024a
com.example.myapplication:style/Theme.Material3.Light = 0x7f110205
com.example.myapplication:style/EmptyTheme = 0x7f110114
com.example.myapplication:style/Base.ThemeOverlay.Material3.TextInputEditText = 0x7f11007d
com.example.myapplication:style/Base.Widget.MaterialComponents.TextView = 0x7f110110
com.example.myapplication:style/Widget.AppCompat.Light.ActionBar.Solid.Inverse = 0x7f1102bf
com.example.myapplication:string/abc_searchview_description_submit = 0x7f100016
com.example.myapplication:style/Base.Widget.MaterialComponents.Slider = 0x7f11010c
com.example.myapplication:attr/endIconTint = 0x7f030179
com.example.myapplication:integer/design_tab_indicator_anim_duration_ms = 0x7f090008
com.example.myapplication:attr/textAppearanceCaption = 0x7f0303d1
com.example.myapplication:styleable/ConstraintSet = 0x7f12002e
com.example.myapplication:attr/cornerFamilyTopLeft = 0x7f030123
com.example.myapplication:string/mtrl_picker_toggle_to_calendar_input_mode = 0x7f100084
com.example.myapplication:style/Base.Widget.MaterialComponents.TextInputEditText = 0x7f11010e
com.example.myapplication:drawable/material_ic_menu_arrow_down_black_24dp = 0x7f070080
com.example.myapplication:style/TextAppearance.Design.Error = 0x7f1101aa
com.example.myapplication:plurals/mtrl_badge_content_description = 0x7f0f0000
com.example.myapplication:integer/hide_password_duration = 0x7f090009
com.example.myapplication:style/ShapeAppearanceOverlay.TopLeftCut = 0x7f110165
com.example.myapplication:style/Base.Widget.MaterialComponents.MaterialCalendar.NavigationButton = 0x7f110107
com.example.myapplication:style/Base.Animation.AppCompat.Dialog = 0x7f11000a
com.example.myapplication:id/search_edit_frame = 0x7f080183
com.example.myapplication:style/Base.Widget.MaterialComponents.Chip = 0x7f110106
com.example.myapplication:style/ThemeOverlay.MaterialComponents.ActionBar = 0x7f11027b
com.example.myapplication:style/Base.Widget.AppCompat.DrawerArrowToggle.Common = 0x7f1100ce
com.example.myapplication:style/ThemeOverlay.Material3.Chip = 0x7f110259
com.example.myapplication:style/Widget.Material3.MaterialDivider.Heavy = 0x7f110359
com.example.myapplication:style/Base.Widget.MaterialComponents.CheckedTextView = 0x7f110105
com.example.myapplication:string/material_slider_range_end = 0x7f10005c
com.example.myapplication:dimen/mtrl_btn_disabled_z = 0x7f060177
com.example.myapplication:id/material_clock_period_toggle = 0x7f08010a
com.example.myapplication:drawable/ic_launcher_foreground = 0x7f070068
com.example.myapplication:style/TextAppearance.Compat.Notification.Time = 0x7f1101a5
com.example.myapplication:style/ThemeOverlay.MaterialComponents.AutoCompleteTextView.FilledBox = 0x7f11027f
com.example.myapplication:style/TextAppearance.AppCompat.Small = 0x7f110188
com.example.myapplication:style/Widget.MaterialComponents.MaterialButtonToggleGroup = 0x7f1103b5
com.example.myapplication:color/test_mtrl_calendar_day_selected = 0x7f05024f
com.example.myapplication:style/Widget.Material3.Button = 0x7f110306
com.example.myapplication:style/Widget.Material3.CollapsingToolbar.Medium = 0x7f11032b
com.example.myapplication:layout/abc_action_mode_bar = 0x7f0b0004
com.example.myapplication:style/Widget.MaterialComponents.Badge = 0x7f11038c
com.example.myapplication:style/Base.Widget.Material3.Snackbar = 0x7f110100
com.example.myapplication:style/Theme.MaterialComponents.DayNight.DialogWhenLarge = 0x7f11021d
com.example.myapplication:style/TextAppearance.Material3.HeadlineMedium = 0x7f1101bb
com.example.myapplication:color/material_dynamic_neutral70 = 0x7f0501a9
com.example.myapplication:style/Widget.Material3.MaterialCalendar.HeaderToggleButton = 0x7f110350
com.example.myapplication:style/Widget.Material3.FloatingActionButton.Large.Surface = 0x7f11033a
com.example.myapplication:style/Base.Widget.Material3.CardView = 0x7f1100f4
com.example.myapplication:style/Widget.Material3.ExtendedFloatingActionButton.Icon.Surface = 0x7f110332
com.example.myapplication:style/Base.Widget.Material3.ActionBar.Solid = 0x7f1100f2
com.example.myapplication:color/m3_timepicker_display_ripple_color = 0x7f050194
com.example.myapplication:style/Base.Widget.AppCompat.ActionBar.TabView = 0x7f1100bb
com.example.myapplication:style/MaterialAlertDialog.MaterialComponents.Body.Text = 0x7f11011f
com.example.myapplication:dimen/m3_ripple_pressed_alpha = 0x7f06010a
com.example.myapplication:attr/roundPercent = 0x7f03034b
com.example.myapplication:style/CardView.Dark = 0x7f110112
com.example.myapplication:dimen/mtrl_alert_dialog_background_inset_start = 0x7f060163
com.example.myapplication:style/Theme.MaterialComponents.Light.Dialog = 0x7f11022f
com.example.myapplication:style/Base.Widget.AppCompat.TextView.SpinnerItem = 0x7f1100ee
com.example.myapplication:color/m3_ref_palette_dynamic_secondary80 = 0x7f0500be
com.example.myapplication:style/Theme.Material3.Light.Dialog = 0x7f110207
com.example.myapplication:styleable/SplitPlaceholderRule = 0x7f12008c
com.example.myapplication:style/Widget.AppCompat.Light.ActivityChooserView = 0x7f1102ca
com.example.myapplication:drawable/notification_bg_low = 0x7f07008f
com.example.myapplication:id/jumpToStart = 0x7f0800f4
com.example.myapplication:dimen/mtrl_bottomappbar_fabOffsetEndMode = 0x7f06016e
com.example.myapplication:style/Base.Widget.AppCompat.Spinner = 0x7f1100eb
com.example.myapplication:style/Base.Widget.AppCompat.SeekBar.Discrete = 0x7f1100ea
com.example.myapplication:id/clip_horizontal = 0x7f080087
com.example.myapplication:string/mtrl_picker_range_header_only_start_selected = 0x7f100079
com.example.myapplication:style/Widget.AppCompat.ListPopupWindow = 0x7f1102d4
com.example.myapplication:string/mtrl_picker_cancel = 0x7f10006c
com.example.myapplication:style/Platform.MaterialComponents = 0x7f11012a
com.example.myapplication:style/Base.Widget.AppCompat.ProgressBar = 0x7f1100e2
com.example.myapplication:attr/transitionPathRotate = 0x7f030446
com.example.myapplication:layout/design_text_input_end_icon = 0x7f0b002c
com.example.myapplication:style/Base.Widget.AppCompat.PopupMenu.Overflow = 0x7f1100e0
com.example.myapplication:style/Base.Widget.AppCompat.ListView = 0x7f1100dc
com.example.myapplication:style/Theme.MaterialComponents.DayNight.Dialog.FixedSize.Bridge = 0x7f11021a
com.example.myapplication:drawable/abc_ic_ab_back_material = 0x7f07001c
com.example.myapplication:styleable/TextEffects = 0x7f120096
com.example.myapplication:style/TextAppearance.AppCompat.Widget.PopupMenu.Small = 0x7f11019f
com.example.myapplication:style/Base.Widget.AppCompat.Light.ActionBar.TabView = 0x7f1100d7
com.example.myapplication:string/bottom_sheet_behavior = 0x7f100021
com.example.myapplication:style/Base.Theme.MaterialComponents.Bridge = 0x7f110061
com.example.myapplication:style/Widget.AppCompat.ActionButton.Overflow = 0x7f1102aa
com.example.myapplication:color/m3_sys_color_dynamic_dark_surface = 0x7f050151
com.example.myapplication:color/design_default_color_on_primary = 0x7f050042
com.example.myapplication:style/ThemeOverlay.Material3.Chip.Assist = 0x7f11025a
com.example.myapplication:styleable/Transform = 0x7f12009c
com.example.myapplication:style/Base.Widget.AppCompat.ImageButton = 0x7f1100d1
com.example.myapplication:id/up = 0x7f0801f4
com.example.myapplication:style/Base.Widget.AppCompat.EditText = 0x7f1100d0
com.example.myapplication:string/category_all = 0x7f100023
com.example.myapplication:style/Base.Theme.MaterialComponents.Dialog.Alert = 0x7f110064
com.example.myapplication:style/Base.TextAppearance.AppCompat.Body2 = 0x7f110015
com.example.myapplication:string/mtrl_picker_text_input_date_range_start_hint = 0x7f100080
com.example.myapplication:color/material_dynamic_primary95 = 0x7f0501c6
com.example.myapplication:style/ThemeOverlay.MaterialComponents.AutoCompleteTextView.FilledBox.Dense = 0x7f110280
com.example.myapplication:id/center_vertical = 0x7f080079
com.example.myapplication:style/Base.Widget.AppCompat.ButtonBar.AlertDialog = 0x7f1100c9
com.example.myapplication:style/Widget.Material3.MaterialCalendar.Day.Invalid = 0x7f110344
com.example.myapplication:style/Base.V14.Theme.Material3.Dark.Dialog = 0x7f110085
com.example.myapplication:style/ThemeOverlay.MaterialComponents.ActionBar.Primary = 0x7f11027c
com.example.myapplication:style/Base.Widget.AppCompat.ButtonBar = 0x7f1100c8
com.example.myapplication:dimen/tooltip_y_offset_touch = 0x7f060248
com.example.myapplication:style/Widget.MaterialComponents.MaterialCalendar.Year.Selected = 0x7f1103ca
com.example.myapplication:style/Base.Widget.AppCompat.AutoCompleteTextView = 0x7f1100c1
com.example.myapplication:style/Base.Widget.AppCompat.ActionButton.Overflow = 0x7f1100be
com.example.myapplication:attr/drawableBottomCompat = 0x7f03015c
com.example.myapplication:style/Base.Theme.MaterialComponents.Dialog.MinWidth = 0x7f110067
com.example.myapplication:attr/state_lifted = 0x7f030390
com.example.myapplication:style/TextAppearance.MaterialComponents.Headline4 = 0x7f1101cd
com.example.myapplication:styleable/KeyCycle = 0x7f120045
com.example.myapplication:style/TextAppearance.MaterialComponents.Body1 = 0x7f1101c5
com.example.myapplication:layout/abc_activity_chooser_view = 0x7f0b0006
com.example.myapplication:styleable/MaterialDivider = 0x7f12005a
com.example.myapplication:style/Widget.Material3.Button.Icon = 0x7f110309
com.example.myapplication:style/Widget.MaterialComponents.MaterialCalendar.HeaderConfirmButton = 0x7f1103bf
com.example.myapplication:string/mtrl_picker_text_input_date_hint = 0x7f10007e
com.example.myapplication:color/m3_chip_ripple_color = 0x7f05006e
com.example.myapplication:style/Widget.MaterialComponents.TimePicker = 0x7f1103ee
com.example.myapplication:id/search_plate = 0x7f080186
com.example.myapplication:color/m3_sys_color_dynamic_dark_outline = 0x7f05014c
com.example.myapplication:color/test_color = 0x7f05024d
com.example.myapplication:style/Platform.V25.AppCompat.Light = 0x7f110134
com.example.myapplication:color/m3_sys_color_dark_surface_variant = 0x7f05013c
com.example.myapplication:id/antiClockwise = 0x7f080054
com.example.myapplication:style/Widget.Material3.CompoundButton.CheckBox = 0x7f11032c
com.example.myapplication:color/m3_sys_color_dynamic_dark_background = 0x7f05013f
com.example.myapplication:style/ThemeOverlay.Material3.NavigationView = 0x7f110271
com.example.myapplication:attr/ratingBarStyleIndicator = 0x7f03033b
com.example.myapplication:styleable/AppCompatEmojiHelper = 0x7f120010
com.example.myapplication:style/Widget.MaterialComponents.MaterialDivider = 0x7f1103cd
com.example.myapplication:style/ThemeOverlay.MaterialComponents.Dialog = 0x7f110289
com.example.myapplication:style/Theme.Material3.DayNight.Dialog.MinWidth = 0x7f1101ff
com.example.myapplication:style/Widget.MaterialComponents.TextInputLayout.FilledBox.ExposedDropdownMenu = 0x7f1103e8
com.example.myapplication:style/Base.V21.ThemeOverlay.MaterialComponents.BottomSheetDialog = 0x7f1100a1
com.example.myapplication:dimen/m3_badge_with_text_horizontal_offset = 0x7f0600b2
com.example.myapplication:id/default_activity_button = 0x7f08009f
com.example.myapplication:style/Theme.MaterialComponents.Bridge = 0x7f11020e
com.example.myapplication:color/purple_500 = 0x7f05023c
com.example.myapplication:style/Base.V21.Theme.AppCompat.Dialog = 0x7f110098
com.example.myapplication:integer/mtrl_badge_max_character_count = 0x7f090020
com.example.myapplication:style/Base.V14.Theme.MaterialComponents.Light.Dialog = 0x7f110090
com.example.myapplication:style/Base.V21.Theme.MaterialComponents.Light = 0x7f11009d
com.example.myapplication:style/Widget.Material3.MaterialCalendar = 0x7f110342
com.example.myapplication:interpolator/btn_radio_to_off_mtrl_animation_interpolator_0 = 0x7f0a0004
com.example.myapplication:styleable/KeyTrigger = 0x7f12004b
com.example.myapplication:style/Widget.Material3.AutoCompleteTextView.FilledBox.Dense = 0x7f1102fd
com.example.myapplication:style/Base.V14.Theme.MaterialComponents.Dialog.Bridge = 0x7f11008c
com.example.myapplication:style/Base.V14.Theme.MaterialComponents.Bridge = 0x7f11008a
com.example.myapplication:style/Base.V14.Theme.Material3.Dark.BottomSheetDialog = 0x7f110084
com.example.myapplication:style/Base.Widget.Material3.FloatingActionButton.Large = 0x7f1100fd
com.example.myapplication:style/Base.ThemeOverlay.AppCompat.Dark.ActionBar = 0x7f110076
com.example.myapplication:style/Base.ThemeOverlay.AppCompat = 0x7f110073
com.example.myapplication:string/abc_shareactionprovider_share_with = 0x7f100018
com.example.myapplication:string/abc_menu_function_shortcut_label = 0x7f10000c
com.example.myapplication:style/Base.Theme.MaterialComponents.Light.Dialog.Bridge = 0x7f11006f
com.example.myapplication:styleable/NavInclude = 0x7f120072
com.example.myapplication:style/Base.Theme.MaterialComponents.Light.DarkActionBar = 0x7f11006b
com.example.myapplication:style/Base.Theme.MaterialComponents.Light = 0x7f110069
com.example.myapplication:style/Widget.AppCompat.EditText = 0x7f1102bb
com.example.myapplication:style/Widget.MaterialComponents.TimePicker.Display.TextInputEditText = 0x7f1103f4
com.example.myapplication:string/edit = 0x7f10002d
com.example.myapplication:style/Widget.MaterialComponents.PopupMenu.ListPopupWindow = 0x7f1103d6
com.example.myapplication:style/Base.Theme.Material3.Light.BottomSheetDialog = 0x7f11005e
com.example.myapplication:id/design_navigation_view = 0x7f0800a6
com.example.myapplication:style/Widget.MaterialComponents.TextInputLayout.FilledBox.Dense.ExposedDropdownMenu = 0x7f1103e7
com.example.myapplication:style/Widget.Material3.Toolbar.OnSurface = 0x7f11037e
com.example.myapplication:style/Base.AlertDialog.AppCompat.Light = 0x7f110009
com.example.myapplication:style/Theme.Material3.Dark.Dialog.Alert = 0x7f1101f7
com.example.myapplication:id/tv_calendar_task_time = 0x7f080212
com.example.myapplication:style/ThemeOverlay.MaterialComponents.Dark.ActionBar = 0x7f110287
com.example.myapplication:style/Widget.MaterialComponents.AppBarLayout.Primary = 0x7f110385
com.example.myapplication:styleable/SwitchCompat = 0x7f120091
com.example.myapplication:style/Base.Theme.Material3.Dark.BottomSheetDialog = 0x7f11005b
com.example.myapplication:style/Theme.MaterialComponents.Light.Dialog.Alert.Bridge = 0x7f110231
com.example.myapplication:style/Base.Theme.AppCompat.Light.Dialog.Alert = 0x7f110056
com.example.myapplication:style/TextAppearance.AppCompat.Medium = 0x7f110183
com.example.myapplication:style/Base.Theme.AppCompat.Light.Dialog = 0x7f110055
com.example.myapplication:style/Base.Theme.AppCompat.DialogWhenLarge = 0x7f110052
com.example.myapplication:style/Widget.Material3.Badge = 0x7f110300
com.example.myapplication:style/Base.Theme.AppCompat.Dialog.FixedSize = 0x7f110050
com.example.myapplication:style/Theme.AppCompat.DialogWhenLarge = 0x7f1101e4
com.example.myapplication:id/test_radiobutton_app_button_tint = 0x7f0801c5
com.example.myapplication:style/Base.Theme.AppCompat.Dialog = 0x7f11004e
com.example.myapplication:style/TextAppearance.AppCompat.Display3 = 0x7f110179
com.example.myapplication:style/Widget.MaterialComponents.FloatingActionButton = 0x7f1103b2
com.example.myapplication:style/Base.Theme.AppCompat.CompactMenu = 0x7f11004d
com.example.myapplication:style/TextAppearance.AppCompat = 0x7f110172
com.example.myapplication:style/Base.TextAppearance.Material3.TitleSmall = 0x7f110044
com.example.myapplication:attr/singleSelection = 0x7f03036e
com.example.myapplication:style/ThemeOverlay.AppCompat.ActionBar = 0x7f110242
com.example.myapplication:id/btn_prev_month = 0x7f08020b
com.example.myapplication:style/Base.TextAppearance.MaterialComponents.Subtitle2 = 0x7f110048
com.example.myapplication:dimen/compat_button_inset_horizontal_material = 0x7f060057
com.example.myapplication:style/Base.TextAppearance.Material3.LabelSmall = 0x7f110042
com.example.myapplication:style/Widget.AppCompat.PopupMenu = 0x7f1102d8
com.example.myapplication:style/Widget.Material3.MaterialCalendar.DayOfWeekLabel = 0x7f110347
com.example.myapplication:string/category_work = 0x7f100025
com.example.myapplication:string/mtrl_picker_text_input_month_abbr = 0x7f100082
com.example.myapplication:style/Widget.Design.AppBarLayout = 0x7f1102ee
com.example.myapplication:id/ifRoom = 0x7f0800e4
com.example.myapplication:style/Theme.MaterialComponents.Light.Dialog.FixedSize.Bridge = 0x7f110234
com.example.myapplication:color/dim_foreground_material_light = 0x7f050058
com.example.myapplication:style/Base.TextAppearance.AppCompat.Widget.PopupMenu.Small = 0x7f11003d
com.example.myapplication:style/Widget.Material3.NavigationRailView.ActiveIndicator = 0x7f110364
com.example.myapplication:integer/m3_card_anim_delay_ms = 0x7f09000c
com.example.myapplication:style/Base.TextAppearance.AppCompat.Widget.DropDownItem = 0x7f11003a
com.example.myapplication:style/Widget.AppCompat.Light.ActionBar.TabText = 0x7f1102c2
com.example.myapplication:style/Theme.Material3.Light.Dialog.MinWidth = 0x7f110209
com.example.myapplication:style/Widget.AppCompat.ActionButton.CloseMode = 0x7f1102a9
com.example.myapplication:styleable/GradientColorItem = 0x7f120041
com.example.myapplication:style/Base.TextAppearance.AppCompat.Widget.Button.Borderless.Colored = 0x7f110037
com.example.myapplication:style/Base.TextAppearance.AppCompat.SearchResult.Subtitle = 0x7f110026
com.example.myapplication:dimen/abc_dialog_title_divider_material = 0x7f060026
com.example.myapplication:string/app_name = 0x7f10001f
com.example.myapplication:style/Theme.Design = 0x7f1101ee
com.example.myapplication:drawable/design_password_eye = 0x7f070063
com.example.myapplication:styleable/ThemeEnforcement = 0x7f120099
com.example.myapplication:style/ShapeAppearanceOverlay.BottomLeftDifferentCornerSize = 0x7f110153
com.example.myapplication:style/Theme.MaterialComponents.Dialog.Alert.Bridge = 0x7f110222
com.example.myapplication:attr/upDuration = 0x7f03044c
com.example.myapplication:style/Base.Widget.AppCompat.ListMenuView = 0x7f1100da
com.example.myapplication:style/Base.TextAppearance.AppCompat.Display2 = 0x7f110019
com.example.myapplication:style/Platform.ThemeOverlay.AppCompat.Light = 0x7f110130
com.example.myapplication:id/autoCompleteToEnd = 0x7f08005b
com.example.myapplication:attr/passwordToggleEnabled = 0x7f030310
com.example.myapplication:styleable/MotionScene = 0x7f12006a
com.example.myapplication:color/m3_slider_inactive_track_color = 0x7f050121
com.example.myapplication:dimen/highlight_alpha_material_colored = 0x7f060096
com.example.myapplication:attr/behavior_autoHide = 0x7f03005a
com.example.myapplication:style/Base.TextAppearance.AppCompat.Title.Inverse = 0x7f11002d
com.example.myapplication:style/Base.TextAppearance.AppCompat.Display1 = 0x7f110018
com.example.myapplication:style/TextAppearance.AppCompat.Widget.TextView.SpinnerItem = 0x7f1101a1
com.example.myapplication:dimen/abc_seekbar_track_progress_height_material = 0x7f060039
com.example.myapplication:styleable/AppCompatTextView = 0x7f120014
com.example.myapplication:id/scrollView = 0x7f08017d
com.example.myapplication:style/Base.MaterialAlertDialog.MaterialComponents.Title.Text = 0x7f110012
com.example.myapplication:style/Widget.Material3.TextInputLayout.OutlinedBox.Dense = 0x7f11037a
com.example.myapplication:style/Base.Theme.Material3.Light = 0x7f11005d
com.example.myapplication:color/m3_ref_palette_neutral30 = 0x7f0500e0
com.example.myapplication:id/decelerateAndComplete = 0x7f08009d
com.example.myapplication:attr/tabIndicatorFullWidth = 0x7f0303b2
com.example.myapplication:style/TextAppearance.AppCompat.SearchResult.Subtitle = 0x7f110186
com.example.myapplication:dimen/m3_chip_elevated_elevation = 0x7f0600e4
com.example.myapplication:attr/checkedIconMargin = 0x7f0300a3
com.example.myapplication:style/MaterialAlertDialog.Material3.Title.Panel = 0x7f11011a
com.example.myapplication:layout/design_navigation_item = 0x7f0b0026
com.example.myapplication:id/customPanel = 0x7f080099
com.example.myapplication:attr/layout_constraintBottom_toTopOf = 0x7f030241
com.example.myapplication:style/Base.V21.Theme.MaterialComponents.Light.Dialog = 0x7f11009e
com.example.myapplication:id/tag_transition_group = 0x7f0801be
com.example.myapplication:attr/windowFixedWidthMinor = 0x7f030467
com.example.myapplication:dimen/m3_badge_with_text_radius = 0x7f0600b3
com.example.myapplication:styleable/FloatingActionButton = 0x7f120037
com.example.myapplication:style/ThemeOverlay.Material3.MaterialAlertDialog.Centered = 0x7f11026b
com.example.myapplication:style/Base.MaterialAlertDialog.MaterialComponents.Title.Icon = 0x7f110010
com.example.myapplication:style/Animation.Design.BottomSheetDialog = 0x7f110006
com.example.myapplication:style/Widget.AppCompat.ProgressBar.Horizontal = 0x7f1102dc
com.example.myapplication:string/status_in_progress = 0x7f10009a
com.example.myapplication:styleable/NavAction = 0x7f12006c
com.example.myapplication:string/status_bar_notification_info_overflow = 0x7f100098
com.example.myapplication:style/Theme.Material3.DynamicColors.Light = 0x7f110204
com.example.myapplication:styleable/MaterialToolbar = 0x7f120060
com.example.myapplication:attr/titleEnabled = 0x7f030422
com.example.myapplication:dimen/item_touch_helper_max_drag_scroll_per_frame = 0x7f06009d
com.example.myapplication:style/ThemeOverlay.MaterialComponents.TextInputEditText.OutlinedBox.Dense = 0x7f11029b
com.example.myapplication:string/path_password_strike_through = 0x7f100093
com.example.myapplication:styleable/MaterialRadioButton = 0x7f12005b
com.example.myapplication:color/m3_sys_color_dynamic_light_inverse_primary = 0x7f050157
com.example.myapplication:styleable/Fragment = 0x7f12003d
com.example.myapplication:style/ThemeOverlay.MaterialComponents.TextInputEditText.OutlinedBox = 0x7f11029a
com.example.myapplication:style/ShapeAppearanceOverlay.Cut = 0x7f110155
com.example.myapplication:style/ThemeOverlay.MaterialComponents.MaterialAlertDialog = 0x7f11028e
com.example.myapplication:style/ThemeOverlay.AppCompat.DayNight.ActionBar = 0x7f110246
com.example.myapplication:string/nav_app_bar_navigate_up_description = 0x7f100089
com.example.myapplication:style/Theme.MaterialComponents.DayNight.Dialog.MinWidth.Bridge = 0x7f11021c
com.example.myapplication:string/mtrl_picker_toggle_to_day_selection = 0x7f100085
com.example.myapplication:drawable/abc_seekbar_tick_mark_material = 0x7f070042
com.example.myapplication:string/mtrl_picker_text_input_date_range_end_hint = 0x7f10007f
com.example.myapplication:id/month_navigation_previous = 0x7f08011f
com.example.myapplication:string/m3_sys_typescale_headline_small_font = 0x7f10004a
com.example.myapplication:style/Widget.Material3.BottomSheet = 0x7f110304
com.example.myapplication:string/mtrl_picker_save = 0x7f10007d
com.example.myapplication:style/Base.Widget.AppCompat.ActionMode = 0x7f1100bf
com.example.myapplication:dimen/tooltip_corner_radius = 0x7f060241
com.example.myapplication:attr/iconPadding = 0x7f0301f1
com.example.myapplication:id/rectangles = 0x7f08016b
com.example.myapplication:attr/textureHeight = 0x7f030409
com.example.myapplication:style/Widget.Material3.MaterialTimePicker.Display.TextInputLayout = 0x7f110361
com.example.myapplication:id/tag_on_apply_window_listener = 0x7f0801b9
com.example.myapplication:string/mtrl_picker_navigate_to_year_description = 0x7f100076
com.example.myapplication:string/mtrl_picker_day_of_week_column_header = 0x7f100071
com.example.myapplication:style/Theme.Design.Light.BottomSheetDialog = 0x7f1101f1
com.example.myapplication:attr/itemShapeAppearanceOverlay = 0x7f030219
com.example.myapplication:id/sin = 0x7f080192
com.example.myapplication:string/mtrl_exceed_max_badge_number_content_description = 0x7f100067
com.example.myapplication:string/material_timepicker_select_time = 0x7f100063
com.example.myapplication:styleable/CollapsingToolbarLayout = 0x7f120025
com.example.myapplication:id/cache_measures = 0x7f080073
com.example.myapplication:string/material_timepicker_pm = 0x7f100062
com.example.myapplication:styleable/PopupWindow = 0x7f12007a
com.example.myapplication:string/material_timepicker_am = 0x7f10005e
com.example.myapplication:dimen/mtrl_fab_min_touch_target = 0x7f0601d4
com.example.myapplication:attr/methodName = 0x7f0302c2
com.example.myapplication:color/material_grey_300 = 0x7f0501e3
com.example.myapplication:style/Theme.MaterialComponents.Light.DialogWhenLarge = 0x7f110237
com.example.myapplication:drawable/abc_list_selector_holo_dark = 0x7f070035
com.example.myapplication:string/bottomsheet_action_expand_halfway = 0x7f100022
com.example.myapplication:layout/abc_expanded_menu_layout = 0x7f0b000d
com.example.myapplication:string/material_motion_easing_emphasized = 0x7f100059
com.example.myapplication:drawable/abc_scrubber_control_off_mtrl_alpha = 0x7f07003c
com.example.myapplication:style/ThemeOverlay.MaterialComponents.Toolbar.Primary = 0x7f1102a0
com.example.myapplication:string/material_motion_easing_accelerated = 0x7f100057
com.example.myapplication:id/btn_mark_complete = 0x7f08006d
com.example.myapplication:string/abc_activity_chooser_view_see_all = 0x7f100004
com.example.myapplication:string/material_hour_selection = 0x7f100053
com.example.myapplication:style/MaterialAlertDialog.MaterialComponents.Picker.Date.Calendar = 0x7f110120
com.example.myapplication:style/MaterialAlertDialog.MaterialComponents.Title.Panel = 0x7f110124
com.example.myapplication:string/m3_sys_typescale_title_medium_font = 0x7f10004f
com.example.myapplication:style/Widget.AppCompat.RatingBar.Indicator = 0x7f1102de
com.example.myapplication:string/m3_sys_typescale_label_medium_font = 0x7f10004c
com.example.myapplication:integer/m3_sys_shape_small_corner_family = 0x7f090018
com.example.myapplication:style/Base.Widget.MaterialComponents.PopupMenu.ContextMenu = 0x7f110109
com.example.myapplication:dimen/mtrl_fab_translation_z_pressed = 0x7f0601d6
com.example.myapplication:style/Widget.Material3.TextInputLayout.FilledBox.ExposedDropdownMenu = 0x7f110378
com.example.myapplication:id/transition_layout_save = 0x7f0801e3
com.example.myapplication:style/ThemeOverlay.MaterialAlertDialog.Material3.Title.Icon = 0x7f110279
com.example.myapplication:style/Base.TextAppearance.AppCompat.Large = 0x7f11001e
com.example.myapplication:string/m3_sys_typescale_headline_large_font = 0x7f100048
com.example.myapplication:drawable/abc_btn_colored_material = 0x7f07000e
com.example.myapplication:id/header_title = 0x7f0800dc
com.example.myapplication:color/m3_ref_palette_dynamic_primary20 = 0x7f0500ab
com.example.myapplication:id/logo = 0x7f080100
com.example.myapplication:id/nav_tasks_calendar = 0x7f08020f
com.example.myapplication:attr/layout_collapseParallaxMultiplier = 0x7f030238
com.example.myapplication:style/Base.V14.ThemeOverlay.MaterialComponents.MaterialAlertDialog = 0x7f110096
com.example.myapplication:color/cardview_dark_background = 0x7f05002a
com.example.myapplication:attr/autoSizeTextType = 0x7f030043
com.example.myapplication:style/Theme.MaterialComponents.Light.Dialog.FixedSize = 0x7f110233
com.example.myapplication:styleable/TextAppearance = 0x7f120095
com.example.myapplication:style/TextAppearance.AppCompat.Light.SearchResult.Subtitle = 0x7f11017f
com.example.myapplication:string/m3_ref_typeface_brand_display_regular = 0x7f100038
com.example.myapplication:string/hello_second_fragment = 0x7f100034
com.example.myapplication:style/Base.V7.ThemeOverlay.AppCompat.Dialog = 0x7f1100b3
com.example.myapplication:style/TextAppearance.Material3.BodyMedium = 0x7f1101b5
com.example.myapplication:string/first_fragment_label = 0x7f100032
com.example.myapplication:style/Widget.MaterialComponents.NavigationRailView.Compact = 0x7f1103d1
com.example.myapplication:color/m3_ref_palette_dynamic_tertiary95 = 0x7f0500cd
com.example.myapplication:string/appbar_scrolling_view_behavior = 0x7f100020
com.example.myapplication:style/Widget.Material3.TextInputLayout.FilledBox = 0x7f110375
com.example.myapplication:style/Widget.Material3.Button.TextButton.Dialog.Flush = 0x7f11030f
com.example.myapplication:style/Base.TextAppearance.AppCompat.Inverse = 0x7f11001d
com.example.myapplication:styleable/StateSet = 0x7f120090
com.example.myapplication:style/Base.TextAppearance.AppCompat.Widget.PopupMenu.Large = 0x7f11003c
com.example.myapplication:style/ShapeAppearanceOverlay.MaterialComponents.FloatingActionButton = 0x7f110160
com.example.myapplication:style/Widget.MaterialComponents.TimePicker.Display.TextInputLayout = 0x7f1103f5
com.example.myapplication:string/add_task = 0x7f10001d
com.example.myapplication:style/Base.Widget.AppCompat.RatingBar.Indicator = 0x7f1100e5
com.example.myapplication:string/action_settings = 0x7f10001b
com.example.myapplication:style/TextAppearance.Material3.LabelMedium = 0x7f1101be
com.example.myapplication:string/abc_shareactionprovider_share_with_application = 0x7f100019
com.example.myapplication:styleable/CheckedTextView = 0x7f12001f
com.example.myapplication:style/Widget.Material3.CircularProgressIndicator.Medium = 0x7f110327
com.example.myapplication:style/Base.Widget.AppCompat.ListView.DropDown = 0x7f1100dd
com.example.myapplication:string/abc_searchview_description_clear = 0x7f100013
com.example.myapplication:color/m3_ref_palette_error100 = 0x7f0500d1
com.example.myapplication:style/Widget.MaterialComponents.BottomNavigationView.Colored = 0x7f110391
com.example.myapplication:attr/colorSurface = 0x7f0300ff
com.example.myapplication:dimen/m3_sys_state_hover_state_layer_opacity = 0x7f06011a
com.example.myapplication:string/abc_search_hint = 0x7f100012
com.example.myapplication:id/search_src_text = 0x7f080187
com.example.myapplication:drawable/abc_switch_thumb_material = 0x7f070048
com.example.myapplication:string/m3_sys_typescale_body_medium_font = 0x7f100043
com.example.myapplication:style/Theme.Design.Light = 0x7f1101f0
com.example.myapplication:string/abc_searchview_description_voice = 0x7f100017
com.example.myapplication:style/Widget.Material3.Snackbar.FullWidth = 0x7f11036c
com.example.myapplication:string/abc_menu_ctrl_shortcut_label = 0x7f100009
com.example.myapplication:string/abc_action_bar_up_description = 0x7f100001
com.example.myapplication:style/Theme.MaterialComponents.Light.NoActionBar.Bridge = 0x7f11023a
com.example.myapplication:id/peekHeight = 0x7f080162
com.example.myapplication:layout/text_view_with_line_height_from_appearance = 0x7f0b0074
com.example.myapplication:style/Widget.Material3.MaterialCalendar.HeaderCancelButton = 0x7f11034a
com.example.myapplication:style/Base.V7.Widget.AppCompat.EditText = 0x7f1100b5
com.example.myapplication:styleable/State = 0x7f12008d
com.example.myapplication:layout/test_toolbar = 0x7f0b0070
com.example.myapplication:layout/test_reflow_chipgroup = 0x7f0b006f
com.example.myapplication:id/view_tree_lifecycle_owner = 0x7f0801fb
com.example.myapplication:drawable/avd_hide_password = 0x7f070056
com.example.myapplication:style/TextAppearance.Material3.TitleLarge = 0x7f1101c1
com.example.myapplication:style/TextAppearance.Design.CollapsingToolbar.Expanded = 0x7f1101a7
com.example.myapplication:attr/srcCompat = 0x7f030382
com.example.myapplication:attr/percentY = 0x7f030318
com.example.myapplication:drawable/btn_radio_on_to_off_mtrl_animation = 0x7f07005f
com.example.myapplication:id/barrier = 0x7f08005d
com.example.myapplication:layout/test_chip_zero_corner_radius = 0x7f0b006b
com.example.myapplication:attr/clockNumberTextColor = 0x7f0300c9
com.example.myapplication:attr/titleTextAppearance = 0x7f03042a
com.example.myapplication:styleable/Transition = 0x7f12009d
com.example.myapplication:style/ThemeOverlay.MaterialComponents.MaterialAlertDialog.Picker.Date.Header.Text = 0x7f110292
com.example.myapplication:attr/suffixTextAppearance = 0x7f0303a2
com.example.myapplication:layout/select_dialog_multichoice_material = 0x7f0b0067
com.example.myapplication:dimen/mtrl_toolbar_default_height = 0x7f06021d
com.example.myapplication:style/TestThemeWithLineHeight = 0x7f110170
com.example.myapplication:layout/select_dialog_item_material = 0x7f0b0066
com.example.myapplication:color/design_default_color_on_surface = 0x7f050044
com.example.myapplication:id/position = 0x7f080165
com.example.myapplication:layout/notification_template_icon_group = 0x7f0b0063
com.example.myapplication:id/decor_content_parent = 0x7f08009e
com.example.myapplication:layout/notification_template_custom_big = 0x7f0b0062
com.example.myapplication:integer/m3_sys_motion_duration_long2 = 0x7f090010
com.example.myapplication:id/unlabeled = 0x7f0801f3
com.example.myapplication:layout/material_timepicker_textinput_display = 0x7f0b0042
com.example.myapplication:color/accent_material_light = 0x7f05001a
com.example.myapplication:style/Widget.Material3.Tooltip = 0x7f110380
com.example.myapplication:styleable/NavDeepLink = 0x7f12006e
com.example.myapplication:layout/abc_alert_dialog_title_material = 0x7f0b000a
com.example.myapplication:attr/rotationCenterId = 0x7f030349
com.example.myapplication:attr/chipIcon = 0x7f0300ac
com.example.myapplication:style/Base.Theme.MaterialComponents.Light.Dialog.Alert = 0x7f11006e
com.example.myapplication:style/Widget.Design.TabLayout = 0x7f1102f6
com.example.myapplication:layout/notification_action_tombstone = 0x7f0b0061
com.example.myapplication:style/Base.V21.Theme.AppCompat.Light.Dialog = 0x7f11009a
com.example.myapplication:layout/notification_action = 0x7f0b0060
com.example.myapplication:layout/mtrl_picker_text_input_date_range = 0x7f0b005f
com.example.myapplication:styleable/Layout = 0x7f12004c
com.example.myapplication:style/Base.TextAppearance.Widget.AppCompat.Toolbar.Subtitle = 0x7f11004a
com.example.myapplication:style/TextAppearance.AppCompat.Widget.ActionMode.Subtitle.Inverse = 0x7f110195
com.example.myapplication:layout/mtrl_picker_text_input_date = 0x7f0b005e
com.example.myapplication:layout/text_view_with_line_height_from_layout = 0x7f0b0075
com.example.myapplication:dimen/m3_card_elevated_elevation = 0x7f0600db
com.example.myapplication:dimen/m3_btn_icon_only_default_size = 0x7f0600c9
com.example.myapplication:layout/mtrl_calendar_vertical = 0x7f0b0051
com.example.myapplication:layout/mtrl_calendar_month_labeled = 0x7f0b004e
com.example.myapplication:style/Base.ThemeOverlay.AppCompat.Dialog = 0x7f110077
com.example.myapplication:dimen/material_emphasis_medium = 0x7f06014e
com.example.myapplication:layout/mtrl_calendar_month = 0x7f0b004d
com.example.myapplication:color/switch_thumb_disabled_material_dark = 0x7f050245
com.example.myapplication:layout/mtrl_calendar_day_of_week = 0x7f0b004a
com.example.myapplication:drawable/m3_radiobutton_ripple = 0x7f070072
com.example.myapplication:style/Base.Widget.AppCompat.PopupWindow = 0x7f1100e1
com.example.myapplication:layout/mtrl_alert_select_dialog_multichoice = 0x7f0b0047
com.example.myapplication:layout/material_timepicker_dialog = 0x7f0b0041
com.example.myapplication:style/Base.ThemeOverlay.MaterialComponents.Light.Dialog.Alert.Framework = 0x7f110081
com.example.myapplication:layout/material_textinput_timepicker = 0x7f0b003d
com.example.myapplication:style/Base.Widget.AppCompat.Light.ActionBar.TabText.Inverse = 0x7f1100d6
com.example.myapplication:id/textinput_helper_text = 0x7f0801d2
com.example.myapplication:attr/maxImageSize = 0x7f0302bb
com.example.myapplication:layout/material_radial_view_group = 0x7f0b003c
com.example.myapplication:layout/material_clock_display_divider = 0x7f0b0037
com.example.myapplication:id/btn_delete_task = 0x7f08006b
com.example.myapplication:string/abc_menu_delete_shortcut_label = 0x7f10000a
com.example.myapplication:color/material_dynamic_secondary80 = 0x7f0501d1
com.example.myapplication:style/Theme.Material3.Dark.NoActionBar = 0x7f1101fa
com.example.myapplication:layout/fragment_first = 0x7f0b002e
com.example.myapplication:layout/design_navigation_menu = 0x7f0b002a
com.example.myapplication:style/Theme.Design.Light.NoActionBar = 0x7f1101f2
com.example.myapplication:style/Base.Theme.MaterialComponents.Dialog.Bridge = 0x7f110065
com.example.myapplication:layout/design_navigation_item_subheader = 0x7f0b0029
com.example.myapplication:style/Base.TextAppearance.AppCompat.Button = 0x7f110016
com.example.myapplication:layout/custom_dialog = 0x7f0b001e
com.example.myapplication:dimen/mtrl_shape_corner_size_medium_component = 0x7f060201
com.example.myapplication:id/view_calendar_task_status = 0x7f080216
com.example.myapplication:id/custom = 0x7f080098
com.example.myapplication:layout/abc_search_view = 0x7f0b0019
com.example.myapplication:layout/abc_search_dropdown_item_icons_2line = 0x7f0b0018
com.example.myapplication:style/Widget.MaterialComponents.BottomAppBar.PrimarySurface = 0x7f11038f
com.example.myapplication:styleable/MenuItem = 0x7f120062
com.example.myapplication:styleable/ColorStateListItem = 0x7f120027
com.example.myapplication:layout/abc_list_menu_item_radio = 0x7f0b0011
com.example.myapplication:string/path_password_eye_mask_visible = 0x7f100092
com.example.myapplication:dimen/m3_appbar_expanded_title_margin_horizontal = 0x7f0600a8
com.example.myapplication:style/Base.ThemeOverlay.AppCompat.ActionBar = 0x7f110074
com.example.myapplication:color/m3_ref_palette_dynamic_neutral90 = 0x7f050098
com.example.myapplication:layout/abc_alert_dialog_button_bar_material = 0x7f0b0008
com.example.myapplication:layout/abc_action_menu_layout = 0x7f0b0003
com.example.myapplication:id/fragment_container_view_tag = 0x7f0800d1
com.example.myapplication:layout/abc_action_bar_title_item = 0x7f0b0000
com.example.myapplication:integer/show_password_duration = 0x7f09002d
com.example.myapplication:attr/paddingStart = 0x7f030308
com.example.myapplication:layout/design_navigation_menu_item = 0x7f0b002b
com.example.myapplication:style/Base.TextAppearance.Material3.LabelMedium = 0x7f110041
com.example.myapplication:dimen/mtrl_slider_thumb_radius = 0x7f060208
com.example.myapplication:drawable/notification_bg_normal = 0x7f070092
com.example.myapplication:styleable/Capability = 0x7f12001c
com.example.myapplication:color/abc_search_url_text_normal = 0x7f05000e
com.example.myapplication:color/material_dynamic_tertiary10 = 0x7f0501d6
com.example.myapplication:style/MaterialAlertDialog.MaterialComponents.Title.Icon = 0x7f110122
com.example.myapplication:style/Theme.MaterialComponents.Light.BarSize = 0x7f11022a
com.example.myapplication:id/sliding_pane_detail_container = 0x7f080196
com.example.myapplication:style/Widget.AppCompat.ActionBar.TabBar = 0x7f1102a5
com.example.myapplication:layout/abc_screen_simple = 0x7f0b0015
com.example.myapplication:style/Base.Widget.MaterialComponents.TextInputLayout = 0x7f11010f
com.example.myapplication:styleable/BottomAppBar = 0x7f120018
com.example.myapplication:integer/mtrl_card_anim_duration_ms = 0x7f090027
com.example.myapplication:dimen/material_textinput_min_width = 0x7f06015d
com.example.myapplication:style/Widget.MaterialComponents.CompoundButton.Switch = 0x7f1103af
com.example.myapplication:integer/mtrl_calendar_header_orientation = 0x7f090023
com.example.myapplication:id/tv_calendar_task_title = 0x7f080213
com.example.myapplication:dimen/m3_badge_with_text_vertical_offset = 0x7f0600b4
com.example.myapplication:id/search_bar = 0x7f080180
com.example.myapplication:style/TextAppearance.AppCompat.Display2 = 0x7f110178
com.example.myapplication:style/Base.Widget.AppCompat.DropDownItem.Spinner = 0x7f1100cf
com.example.myapplication:integer/mtrl_btn_anim_delay_ms = 0x7f090021
com.example.myapplication:attr/background = 0x7f030045
com.example.myapplication:color/m3_hint_foreground = 0x7f050084
com.example.myapplication:dimen/design_fab_translation_z_pressed = 0x7f060076
com.example.myapplication:layout/content_main = 0x7f0b001d
com.example.myapplication:integer/material_motion_path = 0x7f09001f
com.example.myapplication:integer/material_motion_duration_medium_1 = 0x7f09001b
com.example.myapplication:styleable/CustomAttribute = 0x7f120031
com.example.myapplication:string/m3_sys_motion_easing_decelerated = 0x7f10003e
com.example.myapplication:dimen/abc_list_item_height_material = 0x7f060031
com.example.myapplication:integer/material_motion_duration_long_1 = 0x7f090019
com.example.myapplication:drawable/abc_edit_text_material = 0x7f07001b
com.example.myapplication:id/tabMode = 0x7f0801b4
com.example.myapplication:string/character_counter_content_description = 0x7f100026
com.example.myapplication:id/month_navigation_next = 0x7f08011e
com.example.myapplication:integer/m3_sys_shape_medium_corner_family = 0x7f090017
com.example.myapplication:style/Base.TextAppearance.AppCompat.Widget.ActionBar.Menu = 0x7f11002f
com.example.myapplication:style/Base.TextAppearance.AppCompat.Subhead.Inverse = 0x7f11002b
com.example.myapplication:integer/m3_sys_motion_path = 0x7f090015
com.example.myapplication:attr/seekBarStyle = 0x7f030357
com.example.myapplication:string/mtrl_picker_range_header_title = 0x7f10007b
com.example.myapplication:string/clear_text_end_icon_content_description = 0x7f10002a
com.example.myapplication:style/Widget.AppCompat.Light.PopupMenu.Overflow = 0x7f1102d0
com.example.myapplication:dimen/m3_alert_dialog_corner_size = 0x7f0600a2
com.example.myapplication:integer/m3_sys_motion_duration_medium1 = 0x7f090011
com.example.myapplication:color/m3_ref_palette_neutral_variant90 = 0x7f0500f3
com.example.myapplication:string/mtrl_picker_toggle_to_year_selection = 0x7f100087
com.example.myapplication:mipmap/ic_launcher = 0x7f0d0000
com.example.myapplication:style/Widget.Material3.MaterialCalendar.HeaderSelection.Fullscreen = 0x7f11034e
com.example.myapplication:id/submit_area = 0x7f0801b2
com.example.myapplication:attr/maxAcceleration = 0x7f0302b6
com.example.myapplication:color/m3_ref_palette_secondary70 = 0x7f05010b
com.example.myapplication:integer/m3_sys_motion_duration_long1 = 0x7f09000f
com.example.myapplication:style/Widget.Material3.MaterialCalendar.HeaderDivider = 0x7f11034b
com.example.myapplication:integer/m3_btn_anim_delay_ms = 0x7f09000a
com.example.myapplication:color/m3_sys_color_light_on_surface = 0x7f050178
com.example.myapplication:style/ThemeOverlay.AppCompat.DayNight = 0x7f110245
com.example.myapplication:attr/collapsingToolbarLayoutMediumSize = 0x7f0300da
com.example.myapplication:id/scrollIndicatorDown = 0x7f08017b
com.example.myapplication:styleable/ViewPager2 = 0x7f1200a1
com.example.myapplication:style/Widget.Material3.Chip.Assist.Elevated = 0x7f11031b
com.example.myapplication:style/ThemeOverlay.MaterialComponents.TextInputEditText.FilledBox = 0x7f110298
com.example.myapplication:string/fab_transformation_sheet_behavior = 0x7f100031
com.example.myapplication:attr/warmth = 0x7f03045a
com.example.myapplication:id/fab_add_task = 0x7f0800c6
com.example.myapplication:bool/m3_sys_typescale_display_large_text_all_caps = 0x7f040005
com.example.myapplication:style/Base.V24.Theme.Material3.Dark = 0x7f1100a6
com.example.myapplication:style/Base.V22.Theme.AppCompat.Light = 0x7f1100a3
com.example.myapplication:string/abc_searchview_description_search = 0x7f100015
com.example.myapplication:string/item_view_role_description = 0x7f100037
com.example.myapplication:color/design_default_color_on_error = 0x7f050041
com.example.myapplication:attr/switchMinWidth = 0x7f0303a5
com.example.myapplication:id/withinBounds = 0x7f080202
com.example.myapplication:attr/tabIndicatorHeight = 0x7f0303b4
com.example.myapplication:id/withText = 0x7f080201
com.example.myapplication:string/material_clock_toggle_content_description = 0x7f100052
com.example.myapplication:id/dependency_ordering = 0x7f0800a1
com.example.myapplication:id/TOP_END = 0x7f08000e
com.example.myapplication:attr/tabContentStart = 0x7f0303aa
com.example.myapplication:id/visible = 0x7f0801fe
com.example.myapplication:id/navigation_bar_item_small_label_view = 0x7f080146
com.example.myapplication:dimen/mtrl_slider_widget_height = 0x7f06020c
com.example.myapplication:style/TextAppearance.Material3.HeadlineSmall = 0x7f1101bc
com.example.myapplication:id/view_tree_view_model_store_owner = 0x7f0801fd
com.example.myapplication:style/Platform.AppCompat.Light = 0x7f110129
com.example.myapplication:id/tv_detail_description = 0x7f0801ea
com.example.myapplication:layout/test_toolbar_custom_background = 0x7f0b0071
com.example.myapplication:interpolator/btn_radio_to_on_mtrl_animation_interpolator_0 = 0x7f0a0005
com.example.myapplication:id/transition_position = 0x7f0801e4
com.example.myapplication:attr/dragDirection = 0x7f030158
com.example.myapplication:id/transition_current_scene = 0x7f0801e2
com.example.myapplication:id/btn_calendar_today = 0x7f080209
com.example.myapplication:dimen/m3_navigation_rail_item_padding_top = 0x7f060106
com.example.myapplication:id/transitionToStart = 0x7f0801e1
com.example.myapplication:style/Widget.MaterialComponents.NavigationRailView = 0x7f1103ce
com.example.myapplication:id/tag_unhandled_key_listeners = 0x7f0801c0
com.example.myapplication:dimen/notification_subtext_size = 0x7f060230
com.example.myapplication:style/TextAppearance.Compat.Notification.Title = 0x7f1101a6
com.example.myapplication:dimen/tooltip_vertical_padding = 0x7f060246
com.example.myapplication:attr/expandedTitleMargin = 0x7f03018b
com.example.myapplication:attr/layoutDescription = 0x7f030231
com.example.myapplication:dimen/m3_fab_translation_z_pressed = 0x7f0600f2
com.example.myapplication:attr/materialCalendarMonth = 0x7f0302a4
com.example.myapplication:id/transitionToEnd = 0x7f0801e0
com.example.myapplication:drawable/m3_tabs_background = 0x7f070074
com.example.myapplication:attr/transitionFlags = 0x7f030445
com.example.myapplication:styleable/Toolbar = 0x7f12009a
com.example.myapplication:layout/m3_alert_dialog = 0x7f0b0032
com.example.myapplication:color/m3_sys_color_dynamic_light_on_tertiary = 0x7f050160
com.example.myapplication:style/Base.Widget.MaterialComponents.PopupMenu.ListPopupWindow = 0x7f11010a
com.example.myapplication:id/topPanel = 0x7f0801de
com.example.myapplication:attr/drawableRightCompat = 0x7f03015f
com.example.myapplication:id/toggle = 0x7f0801db
com.example.myapplication:id/message = 0x7f080118
com.example.myapplication:id/title_template = 0x7f0801da
com.example.myapplication:attr/dialogTheme = 0x7f03014e
com.example.myapplication:style/TextAppearance.MaterialComponents.Badge = 0x7f1101c4
com.example.myapplication:id/skipCollapsed = 0x7f080193
com.example.myapplication:attr/isMaterial3Theme = 0x7f030208
com.example.myapplication:style/Widget.MaterialComponents.TabLayout.PrimarySurface = 0x7f1103e0
com.example.myapplication:style/ThemeOverlay.Material3.Light.Dialog.Alert.Framework = 0x7f110269
com.example.myapplication:style/Widget.AppCompat.ActionMode = 0x7f1102ab
com.example.myapplication:style/MaterialAlertDialog.Material3.Body.Text = 0x7f110116
com.example.myapplication:id/title = 0x7f0801d8
com.example.myapplication:id/accessibility_custom_action_2 = 0x7f08001e
com.example.myapplication:interpolator/fast_out_slow_in = 0x7f0a0006
com.example.myapplication:style/Widget.Material3.TextInputLayout.FilledBox.Dense = 0x7f110376
com.example.myapplication:style/TestStyleWithoutLineHeight = 0x7f11016f
com.example.myapplication:layout/mtrl_alert_select_dialog_item = 0x7f0b0046
com.example.myapplication:id/textinput_prefix_text = 0x7f0801d4
com.example.myapplication:id/selection_type = 0x7f08018b
com.example.myapplication:attr/endIconContentDescription = 0x7f030176
com.example.myapplication:style/ThemeOverlay.MaterialComponents.BottomAppBar.Surface = 0x7f110284
com.example.myapplication:string/error_icon_content_description = 0x7f10002e
com.example.myapplication:layout/mtrl_calendar_day = 0x7f0b0049
com.example.myapplication:dimen/design_bottom_navigation_height = 0x7f060065
com.example.myapplication:style/Widget.AppCompat.TextView = 0x7f1102e8
com.example.myapplication:id/search_mag_icon = 0x7f080185
com.example.myapplication:style/RtlOverlay.Widget.AppCompat.Search.DropDown.Icon2 = 0x7f110141
com.example.myapplication:attr/menu = 0x7f0302c0
com.example.myapplication:id/textinput_counter = 0x7f0801d0
com.example.myapplication:id/text_input_end_icon = 0x7f0801cd
com.example.myapplication:layout/notification_template_part_chronometer = 0x7f0b0064
com.example.myapplication:attr/springBoundary = 0x7f03037d
com.example.myapplication:id/textStart = 0x7f0801cb
com.example.myapplication:color/design_fab_stroke_end_outer_color = 0x7f050050
com.example.myapplication:style/TextAppearance.AppCompat.Small.Inverse = 0x7f110189
com.example.myapplication:color/material_dynamic_neutral_variant40 = 0x7f0501b3
com.example.myapplication:style/Base.TextAppearance.AppCompat.Small = 0x7f110028
com.example.myapplication:style/Base.V14.Theme.MaterialComponents.Dialog = 0x7f11008b
com.example.myapplication:style/Widget.Material3.NavigationView = 0x7f110365
com.example.myapplication:style/Widget.Material3.FloatingActionButton.Large.Primary = 0x7f110338
com.example.myapplication:layout/design_layout_snackbar = 0x7f0b0021
com.example.myapplication:id/textSpacerNoTitle = 0x7f0801ca
com.example.myapplication:attr/isMaterialTheme = 0x7f030209
com.example.myapplication:attr/logoDescription = 0x7f03028f
com.example.myapplication:style/Platform.V25.AppCompat = 0x7f110133
com.example.myapplication:layout/abc_list_menu_item_layout = 0x7f0b0010
com.example.myapplication:style/Base.Theme.AppCompat.Light.DialogWhenLarge = 0x7f110059
com.example.myapplication:color/m3_ref_palette_secondary99 = 0x7f05010f
com.example.myapplication:attr/shapeAppearanceOverlay = 0x7f030360
com.example.myapplication:id/textSpacerNoButtons = 0x7f0801c9
com.example.myapplication:style/Widget.MaterialComponents.AutoCompleteTextView.FilledBox = 0x7f110388
com.example.myapplication:id/tag_accessibility_pane_title = 0x7f0801b8
com.example.myapplication:attr/pivotAnchor = 0x7f03031a
com.example.myapplication:id/stretch = 0x7f0801b0
com.example.myapplication:style/Widget.MaterialComponents.ExtendedFloatingActionButton = 0x7f1103b0
com.example.myapplication:color/m3_dark_highlighted_text = 0x7f050073
com.example.myapplication:id/staticPostLayout = 0x7f0801ae
com.example.myapplication:styleable/Snackbar = 0x7f120087
com.example.myapplication:color/m3_chip_text_color = 0x7f050070
com.example.myapplication:integer/m3_sys_motion_duration_short2 = 0x7f090014
com.example.myapplication:attr/chipSpacingHorizontal = 0x7f0300b4
com.example.myapplication:id/staticLayout = 0x7f0801ad
com.example.myapplication:id/startVertical = 0x7f0801ac
com.example.myapplication:id/start = 0x7f0801a9
com.example.myapplication:string/m3_sys_motion_easing_linear = 0x7f100040
com.example.myapplication:id/standard = 0x7f0801a8
com.example.myapplication:dimen/m3_bottomappbar_fab_cradle_rounded_corner_radius = 0x7f0600be
com.example.myapplication:dimen/mtrl_slider_thumb_elevation = 0x7f060207
com.example.myapplication:dimen/m3_card_elevated_hovered_z = 0x7f0600dc
com.example.myapplication:layout/material_clock_period_toggle_land = 0x7f0b0039
com.example.myapplication:id/snap = 0x7f08019a
com.example.myapplication:drawable/abc_scrubber_primary_mtrl_alpha = 0x7f07003f
com.example.myapplication:style/Theme.MaterialComponents.DayNight.DarkActionBar = 0x7f110213
com.example.myapplication:id/direct = 0x7f0800a9
com.example.myapplication:id/skipped = 0x7f080194
com.example.myapplication:style/Widget.MaterialComponents.MaterialCalendar.HeaderSelection.Fullscreen = 0x7f1103c3
com.example.myapplication:id/parent_matrix = 0x7f08015e
com.example.myapplication:attr/subtitleTextColor = 0x7f03039f
com.example.myapplication:id/search_voice_btn = 0x7f080188
com.example.myapplication:style/Base.ThemeOverlay.MaterialComponents.Dialog.Alert = 0x7f11007f
com.example.myapplication:style/Base.Theme.AppCompat.Light.Dialog.MinWidth = 0x7f110058
com.example.myapplication:id/search_badge = 0x7f08017f
com.example.myapplication:dimen/m3_ripple_selectable_pressed_alpha = 0x7f06010b
com.example.myapplication:id/screen = 0x7f080179
com.example.myapplication:styleable/GradientColor = 0x7f120040
com.example.myapplication:color/m3_ref_palette_neutral20 = 0x7f0500df
com.example.myapplication:id/scale = 0x7f080178
com.example.myapplication:id/right_side = 0x7f080171
com.example.myapplication:color/design_snackbar_background_color = 0x7f050054
com.example.myapplication:style/Base.TextAppearance.AppCompat.Widget.Switch = 0x7f11003e
com.example.myapplication:style/TextAppearance.Design.Hint = 0x7f1101ac
com.example.myapplication:style/TextAppearance.AppCompat.Widget.Button = 0x7f110198
com.example.myapplication:id/right_icon = 0x7f080170
com.example.myapplication:drawable/abc_list_focused_holo = 0x7f07002d
com.example.myapplication:style/Widget.AppCompat.Light.AutoCompleteTextView = 0x7f1102cb
com.example.myapplication:string/material_timepicker_clock_mode_description = 0x7f10005f
com.example.myapplication:dimen/mtrl_btn_padding_bottom = 0x7f060180
com.example.myapplication:style/Widget.Material3.MaterialCalendar.HeaderTitle = 0x7f11034f
com.example.myapplication:id/disjoint = 0x7f0800ae
com.example.myapplication:color/material_on_background_disabled = 0x7f0501e9
com.example.myapplication:id/overshoot = 0x7f080158
com.example.myapplication:drawable/material_ic_calendar_black_24dp = 0x7f070079
com.example.myapplication:color/blue_700 = 0x7f050255
com.example.myapplication:dimen/test_mtrl_calendar_day_cornerSize = 0x7f060235
com.example.myapplication:styleable/ActivityRule = 0x7f120008
com.example.myapplication:attr/cardCornerRadius = 0x7f030089
com.example.myapplication:id/postLayout = 0x7f080166
com.example.myapplication:attr/submitBackground = 0x7f03039b
com.example.myapplication:style/Widget.Material3.CircularProgressIndicator = 0x7f110325
com.example.myapplication:style/Widget.MaterialComponents.Chip.Action = 0x7f1103a3
com.example.myapplication:id/parent = 0x7f08015b
com.example.myapplication:id/parallax = 0x7f08015a
com.example.myapplication:id/outward = 0x7f080157
com.example.myapplication:id/notification_main_column = 0x7f080151
com.example.myapplication:attr/maxActionInlineWidth = 0x7f0302b7
com.example.myapplication:id/notification_background = 0x7f080150
com.example.myapplication:bool/m3_sys_typescale_body_small_text_all_caps = 0x7f040004
com.example.myapplication:dimen/mtrl_btn_padding_top = 0x7f060183
com.example.myapplication:dimen/material_cursor_width = 0x7f060149
com.example.myapplication:style/Widget.AppCompat.Spinner = 0x7f1102e4
com.example.myapplication:id/normal = 0x7f08014e
com.example.myapplication:dimen/mtrl_calendar_header_text_padding = 0x7f06019f
com.example.myapplication:style/Widget.MaterialComponents.LinearProgressIndicator = 0x7f1103b4
com.example.myapplication:id/noState = 0x7f08014c
com.example.myapplication:id/home = 0x7f0800de
com.example.myapplication:id/pin = 0x7f080164
com.example.myapplication:id/noScroll = 0x7f08014b
com.example.myapplication:style/TextAppearance.Compat.Notification.Line2 = 0x7f1101a4
com.example.myapplication:style/Widget.Material3.Button.TextButton.Snackbar = 0x7f110312
com.example.myapplication:style/Base.TextAppearance.AppCompat.Large.Inverse = 0x7f11001f
com.example.myapplication:styleable/Badge = 0x7f120016
com.example.myapplication:color/material_blue_grey_800 = 0x7f05019a
com.example.myapplication:id/navigation_bar_item_icon_view = 0x7f080143
com.example.myapplication:style/Base.Widget.Material3.ExtendedFloatingActionButton.Icon = 0x7f1100fb
com.example.myapplication:dimen/cardview_compat_inset_shadow = 0x7f060053
com.example.myapplication:id/navigation_bar_item_icon_container = 0x7f080142
com.example.myapplication:color/m3_button_outline_color_selector = 0x7f050064
com.example.myapplication:style/TextAppearance.AppCompat.Body2 = 0x7f110174
com.example.myapplication:attr/pressedTranslationZ = 0x7f03032e
com.example.myapplication:attr/suffixTextColor = 0x7f0303a3
com.example.myapplication:id/nav_host_fragment_container = 0x7f08013d
com.example.myapplication:id/mtrl_view_tag_bottom_padding = 0x7f080138
com.example.myapplication:dimen/material_clock_number_text_size = 0x7f060142
com.example.myapplication:id/mtrl_picker_title_text = 0x7f080137
com.example.myapplication:id/transition_transform = 0x7f0801e6
com.example.myapplication:id/action_text = 0x7f080048
com.example.myapplication:style/Base.V26.Theme.AppCompat = 0x7f1100aa
com.example.myapplication:id/mtrl_picker_text_input_range_start = 0x7f080136
com.example.myapplication:id/recycler_calendar_tasks = 0x7f080210
com.example.myapplication:id/progress_circular = 0x7f080167
com.example.myapplication:attr/materialButtonOutlinedStyle = 0x7f030297
com.example.myapplication:id/mtrl_picker_header_toggle = 0x7f080133
com.example.myapplication:id/mtrl_picker_header_title_and_selection = 0x7f080132
com.example.myapplication:styleable/ViewStubCompat = 0x7f1200a2
com.example.myapplication:style/ShapeAppearanceOverlay.MaterialComponents.TextInputLayout.FilledBox = 0x7f110164
com.example.myapplication:id/tv_task_deadline = 0x7f0801ee
com.example.myapplication:id/mtrl_anchor_parent = 0x7f080122
com.example.myapplication:drawable/m3_popupmenu_background_overlay = 0x7f070071
com.example.myapplication:id/contentPanel = 0x7f080091
com.example.myapplication:id/square = 0x7f0801a4
com.example.myapplication:style/Widget.AppCompat.TextView.SpinnerItem = 0x7f1102e9
com.example.myapplication:color/material_dynamic_tertiary40 = 0x7f0501da
com.example.myapplication:id/month_title = 0x7f080120
com.example.myapplication:id/shortcut = 0x7f08018e
com.example.myapplication:dimen/mtrl_calendar_month_horizontal_padding = 0x7f0601a4
com.example.myapplication:id/material_minute_tv = 0x7f08010f
com.example.myapplication:style/Widget.Material3.CardView.Outlined = 0x7f110318
com.example.myapplication:drawable/m3_tabs_transparent_background = 0x7f070077
com.example.myapplication:style/Base.ThemeOverlay.AppCompat.Dialog.Alert = 0x7f110078
com.example.myapplication:style/Theme.MaterialComponents.Light.Dialog.MinWidth = 0x7f110235
com.example.myapplication:style/Base.Widget.AppCompat.ActionButton = 0x7f1100bc
com.example.myapplication:id/material_value_index = 0x7f080117
com.example.myapplication:string/mtrl_picker_invalid_format_use = 0x7f100074
com.example.myapplication:attr/listPreferredItemPaddingEnd = 0x7f03028a
com.example.myapplication:color/m3_ref_palette_neutral90 = 0x7f0500e6
com.example.myapplication:id/material_timepicker_ok_button = 0x7f080115
com.example.myapplication:id/material_timepicker_mode_button = 0x7f080114
com.example.myapplication:layout/mtrl_alert_dialog_title = 0x7f0b0045
com.example.myapplication:id/material_timepicker_edit_text = 0x7f080113
com.example.myapplication:style/Widget.MaterialComponents.MaterialCalendar.Year = 0x7f1103c9
com.example.myapplication:id/chronometer = 0x7f080084
com.example.myapplication:id/material_clock_hand = 0x7f080107
com.example.myapplication:dimen/design_snackbar_elevation = 0x7f060083
com.example.myapplication:style/Widget.MaterialComponents.Button.Icon = 0x7f110396
com.example.myapplication:color/material_dynamic_neutral_variant60 = 0x7f0501b5
com.example.myapplication:id/material_clock_face = 0x7f080106
com.example.myapplication:id/locale = 0x7f0800ff
com.example.myapplication:attr/keyPositionType = 0x7f030227
com.example.myapplication:attr/materialCardViewElevatedStyle = 0x7f0302a9
com.example.myapplication:style/Platform.V21.AppCompat.Light = 0x7f110132
com.example.myapplication:id/list_item = 0x7f0800fe
com.example.myapplication:id/pathRelative = 0x7f080161
com.example.myapplication:id/linear = 0x7f0800fc
com.example.myapplication:id/jumpToEnd = 0x7f0800f3
com.example.myapplication:id/item4 = 0x7f0800f1
com.example.myapplication:id/item2 = 0x7f0800ef
com.example.myapplication:style/Base.V14.Theme.MaterialComponents.Light.DarkActionBar.Bridge = 0x7f11008f
com.example.myapplication:id/test_checkbox_android_button_tint = 0x7f0801c2
com.example.myapplication:drawable/abc_btn_check_material_anim = 0x7f07000b
com.example.myapplication:styleable/FloatingActionButton_Behavior_Layout = 0x7f120038
com.example.myapplication:id/item1 = 0x7f0800ee
com.example.myapplication:style/Animation.MaterialComponents.BottomSheetDialog = 0x7f110007
com.example.myapplication:id/italic = 0x7f0800ed
com.example.myapplication:style/Base.Widget.AppCompat.Light.ActionBar.Solid = 0x7f1100d3
com.example.myapplication:style/ThemeOverlay.MaterialComponents.TimePicker.Display = 0x7f11029d
com.example.myapplication:id/invisible = 0x7f0800eb
com.example.myapplication:id/immediateStop = 0x7f0800e8
com.example.myapplication:id/image = 0x7f0800e7
com.example.myapplication:id/btn_all_tasks = 0x7f080069
com.example.myapplication:id/ignore = 0x7f0800e5
com.example.myapplication:id/grouping = 0x7f0800d9
com.example.myapplication:color/m3_default_color_primary_text = 0x7f050076
com.example.myapplication:id/accessibility_custom_action_17 = 0x7f08001b
com.example.myapplication:drawable/$avd_show_password__1 = 0x7f070004
com.example.myapplication:animator/nav_default_pop_exit_anim = 0x7f020023
com.example.myapplication:styleable/ActivityChooserView = 0x7f120005
com.example.myapplication:id/graph = 0x7f0800d6
com.example.myapplication:attr/extendedFloatingActionButtonStyle = 0x7f030195
com.example.myapplication:id/ghost_view_holder = 0x7f0800d4
com.example.myapplication:dimen/notification_content_margin_start = 0x7f060227
com.example.myapplication:integer/mtrl_calendar_selection_text_lines = 0x7f090024
com.example.myapplication:dimen/test_navigation_bar_active_text_size = 0x7f060238
com.example.myapplication:attr/backgroundSplit = 0x7f03004c
com.example.myapplication:style/ShapeAppearanceOverlay.TopRightDifferentCornerSize = 0x7f110166
com.example.myapplication:id/leftToRight = 0x7f0800f8
com.example.myapplication:id/tag_on_receive_content_listener = 0x7f0801ba
com.example.myapplication:styleable/FontFamilyFont = 0x7f12003b
com.example.myapplication:style/Base.V26.Theme.AppCompat.Light = 0x7f1100ab
com.example.myapplication:style/Widget.Material3.MaterialCalendar.HeaderLayout = 0x7f11034c
com.example.myapplication:id/forever = 0x7f0800d0
com.example.myapplication:style/Widget.MaterialComponents.MaterialCalendar.YearNavigationButton = 0x7f1103cc
com.example.myapplication:style/Base.ThemeOverlay.AppCompat.Light = 0x7f110079
com.example.myapplication:id/filled = 0x7f0800cb
com.example.myapplication:style/Animation.AppCompat.Tooltip = 0x7f110005
com.example.myapplication:style/ShapeAppearanceOverlay.Material3.NavigationView.Item = 0x7f11015a
com.example.myapplication:dimen/mtrl_textinput_counter_margin_start = 0x7f060219
com.example.myapplication:id/layout = 0x7f0800f6
com.example.myapplication:integer/m3_sys_motion_duration_short1 = 0x7f090013
com.example.myapplication:style/Widget.MaterialComponents.CollapsingToolbar = 0x7f1103ac
com.example.myapplication:id/tv_task_category = 0x7f0801ed
com.example.myapplication:style/MaterialAlertDialog.MaterialComponents.Title.Text.CenterStacked = 0x7f110127
com.example.myapplication:id/fill_vertical = 0x7f0800ca
com.example.myapplication:id/fade = 0x7f0800c7
com.example.myapplication:id/fab = 0x7f0800c5
com.example.myapplication:dimen/m3_sys_typescale_body_medium_text_size = 0x7f06011f
com.example.myapplication:id/navigation_bar_item_large_label_view = 0x7f080145
com.example.myapplication:style/Widget.Material3.MaterialTimePicker.Button = 0x7f11035b
com.example.myapplication:id/top = 0x7f0801dd
com.example.myapplication:attr/barrierAllowsGoneWidgets = 0x7f030057
com.example.myapplication:style/Widget.AppCompat.ListView.Menu = 0x7f1102d7
com.example.myapplication:id/expand_activities_button = 0x7f0800c3
com.example.myapplication:style/Theme.AppCompat.DayNight.DarkActionBar = 0x7f1101db
com.example.myapplication:id/groups = 0x7f0800da
com.example.myapplication:dimen/mtrl_navigation_rail_elevation = 0x7f0601ec
com.example.myapplication:attr/cardViewStyle = 0x7f03008f
com.example.myapplication:id/enterAlwaysCollapsed = 0x7f0800c1
com.example.myapplication:drawable/abc_btn_radio_material = 0x7f070010
com.example.myapplication:id/enterAlways = 0x7f0800c0
com.example.myapplication:id/end = 0x7f0800be
com.example.myapplication:drawable/material_ic_keyboard_arrow_right_black_24dp = 0x7f07007f
com.example.myapplication:id/elastic = 0x7f0800bd
com.example.myapplication:id/accessibility_custom_action_31 = 0x7f08002b
com.example.myapplication:id/dragUp = 0x7f0800b6
com.example.myapplication:id/dragStart = 0x7f0800b5
com.example.myapplication:attr/textAppearanceDisplayMedium = 0x7f0303d3
com.example.myapplication:layout/mtrl_alert_dialog = 0x7f0b0043
com.example.myapplication:string/mtrl_chip_close_icon_content_description = 0x7f100066
com.example.myapplication:layout/mtrl_calendar_horizontal = 0x7f0b004c
com.example.myapplication:integer/config_tooltipAnimTime = 0x7f090006
com.example.myapplication:id/multiply = 0x7f080139
com.example.myapplication:style/Base.TextAppearance.AppCompat.Widget.ActionBar.Title = 0x7f110032
com.example.myapplication:style/Widget.Material3.TabLayout.Secondary = 0x7f110370
com.example.myapplication:id/dragClockwise = 0x7f0800b0
com.example.myapplication:id/dragAnticlockwise = 0x7f0800af
com.example.myapplication:dimen/m3_navigation_rail_default_width = 0x7f060100
com.example.myapplication:attr/subtitleTextStyle = 0x7f0303a0
com.example.myapplication:attr/carousel_infinite = 0x7f030094
com.example.myapplication:id/disablePostScroll = 0x7f0800ac
com.example.myapplication:id/cut = 0x7f08009a
com.example.myapplication:attr/buttonBarStyle = 0x7f03007f
com.example.myapplication:style/Theme.AppCompat.DayNight = 0x7f1101da
com.example.myapplication:style/ThemeOverlay.MaterialComponents.DayNight.BottomSheetDialog = 0x7f110288
com.example.myapplication:string/character_counter_pattern = 0x7f100028
com.example.myapplication:style/Base.Widget.AppCompat.SearchView = 0x7f1100e7
com.example.myapplication:color/androidx_core_ripple_material_light = 0x7f05001b
com.example.myapplication:dimen/mtrl_btn_icon_btn_padding_left = 0x7f06017b
com.example.myapplication:drawable/abc_textfield_activated_mtrl_alpha = 0x7f070050
com.example.myapplication:style/Widget.MaterialComponents.PopupMenu.ContextMenu = 0x7f1103d5
com.example.myapplication:dimen/design_snackbar_min_width = 0x7f060086
com.example.myapplication:attr/customPixelDimension = 0x7f03013b
com.example.myapplication:id/action_menu_divider = 0x7f080042
com.example.myapplication:id/cos = 0x7f080095
com.example.myapplication:id/coordinator = 0x7f080094
com.example.myapplication:style/ThemeOverlay.MaterialComponents.BottomSheetDialog = 0x7f110285
com.example.myapplication:color/m3_sys_color_dark_on_error = 0x7f05012c
com.example.myapplication:id/continuousVelocity = 0x7f080093
com.example.myapplication:dimen/m3_sys_typescale_display_large_letter_spacing = 0x7f060122
com.example.myapplication:attr/motionDurationMedium1 = 0x7f0302d2
com.example.myapplication:dimen/mtrl_navigation_bar_item_default_icon_size = 0x7f0601e1
com.example.myapplication:layout/mtrl_layout_snackbar_include = 0x7f0b0054
com.example.myapplication:id/contiguous = 0x7f080092
com.example.myapplication:style/ShapeAppearance.MaterialComponents.Tooltip = 0x7f110151
com.example.myapplication:color/material_cursor_color = 0x7f05019d
com.example.myapplication:id/unchecked = 0x7f0801f1
com.example.myapplication:style/ThemeOverlay.Material3.AutoCompleteTextView.FilledBox.Dense = 0x7f11024f
com.example.myapplication:style/Base.Widget.Material3.Chip = 0x7f1100f5
com.example.myapplication:style/Widget.AppCompat.ActivityChooserView = 0x7f1102ac
com.example.myapplication:attr/applyMotionScene = 0x7f030037
com.example.myapplication:style/Base.Widget.AppCompat.SeekBar = 0x7f1100e9
com.example.myapplication:styleable/TextInputEditText = 0x7f120097
com.example.myapplication:color/m3_tabs_icon_color = 0x7f050185
com.example.myapplication:id/textinput_suffix_text = 0x7f0801d5
com.example.myapplication:id/confirm_button = 0x7f08008d
com.example.myapplication:attr/actionModeFindDrawable = 0x7f030019
com.example.myapplication:id/closest = 0x7f08008a
com.example.myapplication:color/m3_sys_color_light_on_background = 0x7f050171
com.example.myapplication:dimen/mtrl_slider_track_height = 0x7f060209
com.example.myapplication:id/async = 0x7f080058
com.example.myapplication:id/design_bottom_sheet = 0x7f0800a2
com.example.myapplication:attr/chipIconTint = 0x7f0300af
com.example.myapplication:dimen/mtrl_btn_icon_padding = 0x7f06017c
com.example.myapplication:styleable/ActionBar = 0x7f120000
com.example.myapplication:dimen/mtrl_badge_text_size = 0x7f06016a
com.example.myapplication:id/clear_text = 0x7f080086
com.example.myapplication:style/Base.Widget.AppCompat.Light.PopupMenu = 0x7f1100d8
com.example.myapplication:style/TextAppearance.Material3.BodyLarge = 0x7f1101b4
com.example.myapplication:style/Widget.AppCompat.Light.ActionButton.Overflow = 0x7f1102c8
com.example.myapplication:id/circle_center = 0x7f080085
com.example.myapplication:color/m3_ref_palette_secondary60 = 0x7f05010a
com.example.myapplication:animator/fragment_close_exit = 0x7f020004
com.example.myapplication:dimen/mtrl_calendar_header_selection_line_height = 0x7f06019e
com.example.myapplication:id/flip = 0x7f0800ce
com.example.myapplication:attr/trackCornerRadius = 0x7f03043d
com.example.myapplication:style/Base.Widget.AppCompat.Button.Colored = 0x7f1100c6
com.example.myapplication:style/TextAppearance.Design.Placeholder = 0x7f1101ad
com.example.myapplication:id/chip1 = 0x7f080080
com.example.myapplication:attr/materialCalendarHeaderConfirmButton = 0x7f03029e
com.example.myapplication:drawable/mtrl_dialog_background = 0x7f070082
com.example.myapplication:id/chain2 = 0x7f08007b
com.example.myapplication:id/mtrl_child_content_container = 0x7f08012c
com.example.myapplication:id/chain = 0x7f08007a
com.example.myapplication:color/m3_ref_palette_dynamic_neutral_variant90 = 0x7f0500a5
com.example.myapplication:color/m3_sys_color_dark_on_error_container = 0x7f05012d
com.example.myapplication:id/tag_accessibility_clickable_spans = 0x7f0801b6
com.example.myapplication:id/center_horizontal = 0x7f080078
com.example.myapplication:attr/springMass = 0x7f03037f
com.example.myapplication:attr/nestedScrollable = 0x7f0302f6
com.example.myapplication:dimen/mtrl_snackbar_margin = 0x7f060210
com.example.myapplication:style/Base.Widget.AppCompat.Button.Small = 0x7f1100c7
com.example.myapplication:id/center = 0x7f080077
com.example.myapplication:drawable/material_ic_clear_black_24dp = 0x7f07007a
com.example.myapplication:style/ThemeOverlay.MaterialComponents.Dialog.Alert = 0x7f11028a
com.example.myapplication:id/cancel_button = 0x7f080075
com.example.myapplication:style/Theme.MaterialComponents.Dialog.Alert = 0x7f110221
com.example.myapplication:style/ShapeAppearanceOverlay.MaterialComponents.MaterialCalendar.Year = 0x7f110163
com.example.myapplication:dimen/test_navigation_bar_label_padding = 0x7f06023e
com.example.myapplication:attr/tabIconTint = 0x7f0303ac
com.example.myapplication:attr/pathMotionArc = 0x7f030313
com.example.myapplication:id/button_second = 0x7f080072
com.example.myapplication:dimen/abc_star_medium = 0x7f06003c
com.example.myapplication:id/btn_work_tasks = 0x7f080070
com.example.myapplication:id/slide = 0x7f080195
com.example.myapplication:style/Theme.MaterialComponents.Light.BottomSheetDialog = 0x7f11022b
com.example.myapplication:id/btn_edit = 0x7f08006c
com.example.myapplication:attr/actionModePopupWindowStyle = 0x7f03001b
com.example.myapplication:attr/materialDividerHeavyStyle = 0x7f0302b0
com.example.myapplication:id/btn_back = 0x7f08006a
com.example.myapplication:styleable/ForegroundLinearLayout = 0x7f12003c
com.example.myapplication:attr/popUpTo = 0x7f030323
com.example.myapplication:color/material_on_background_emphasis_high_type = 0x7f0501ea
com.example.myapplication:attr/waveShape = 0x7f03045f
com.example.myapplication:drawable/ic_mtrl_chip_checked_black = 0x7f07006d
com.example.myapplication:style/Widget.MaterialComponents.ActionBar.Primary = 0x7f110381
com.example.myapplication:style/ThemeOverlay.Material3.Dialog.Alert = 0x7f11025f
com.example.myapplication:id/supportScrollUp = 0x7f0801b3
com.example.myapplication:string/mtrl_picker_toggle_to_text_input_mode = 0x7f100086
com.example.myapplication:id/beginOnFirstDraw = 0x7f08005f
com.example.myapplication:color/m3_slider_active_track_color = 0x7f05011f
com.example.myapplication:id/baseline = 0x7f08005e
com.example.myapplication:xml/standalone_badge = 0x7f130002
com.example.myapplication:style/Base.V7.Theme.AppCompat.Light.Dialog = 0x7f1100b2
com.example.myapplication:id/androidx_window_activity_scope = 0x7f080051
com.example.myapplication:dimen/mtrl_tooltip_arrowSize = 0x7f06021e
com.example.myapplication:style/Widget.AppCompat.Light.ActionButton.CloseMode = 0x7f1102c7
com.example.myapplication:style/Widget.Material3.ExtendedFloatingActionButton.Icon.Tertiary = 0x7f110333
com.example.myapplication:id/add = 0x7f08004b
com.example.myapplication:attr/menuGravity = 0x7f0302c1
com.example.myapplication:style/TextAppearance.AppCompat.Subhead.Inverse = 0x7f11018b
com.example.myapplication:style/Widget.MaterialComponents.Button.UnelevatedButton = 0x7f11039f
com.example.myapplication:style/Base.V7.Widget.AppCompat.Toolbar = 0x7f1100b6
com.example.myapplication:id/always = 0x7f080050
com.example.myapplication:style/Theme.MaterialComponents.Light.NoActionBar = 0x7f110239
com.example.myapplication:id/search_close_btn = 0x7f080182
com.example.myapplication:string/search = 0x7f100095
com.example.myapplication:dimen/mtrl_low_ripple_focused_alpha = 0x7f0601dd
com.example.myapplication:style/Widget.MaterialComponents.NavigationRailView.PrimarySurface = 0x7f1103d2
com.example.myapplication:id/action_divider = 0x7f080040
com.example.myapplication:color/m3_ref_palette_neutral50 = 0x7f0500e2
com.example.myapplication:id/actionDown = 0x7f080032
com.example.myapplication:styleable/ImageFilterView = 0x7f120042
com.example.myapplication:style/Widget.Design.CollapsingToolbar = 0x7f1102f1
com.example.myapplication:style/Base.Widget.AppCompat.Light.ActionBar.TabBar = 0x7f1100d4
com.example.myapplication:interpolator/mtrl_fast_out_linear_in = 0x7f0a0007
com.example.myapplication:drawable/abc_tab_indicator_mtrl_alpha = 0x7f07004b
com.example.myapplication:id/accessibility_custom_action_4 = 0x7f08002c
com.example.myapplication:id/accessibility_custom_action_30 = 0x7f08002a
com.example.myapplication:color/primary_dark_material_light = 0x7f050234
com.example.myapplication:styleable/include = 0x7f1200a4
com.example.myapplication:id/accessibility_custom_action_29 = 0x7f080028
com.example.myapplication:color/m3_sys_color_dynamic_light_outline = 0x7f050162
com.example.myapplication:id/accessibility_custom_action_27 = 0x7f080026
com.example.myapplication:id/snackbar_action = 0x7f080198
com.example.myapplication:id/wrap = 0x7f080203
com.example.myapplication:style/ThemeOverlay.MaterialComponents = 0x7f11027a
com.example.myapplication:dimen/mtrl_textinput_box_stroke_width_focused = 0x7f060218
com.example.myapplication:id/expanded_menu = 0x7f0800c4
com.example.myapplication:style/Base.DialogWindowTitleBackground.AppCompat = 0x7f11000f
com.example.myapplication:drawable/material_ic_edit_black_24dp = 0x7f07007b
com.example.myapplication:id/accessibility_custom_action_25 = 0x7f080024
com.example.myapplication:attr/editTextColor = 0x7f03016d
com.example.myapplication:string/nav_calendar = 0x7f10008b
com.example.myapplication:dimen/compat_notification_large_icon_max_width = 0x7f06005d
com.example.myapplication:id/accessibility_custom_action_24 = 0x7f080023
com.example.myapplication:id/mtrl_calendar_text_input_frame = 0x7f080129
com.example.myapplication:id/accessibility_custom_action_22 = 0x7f080021
com.example.myapplication:attr/clockIcon = 0x7f0300c8
com.example.myapplication:attr/colorControlActivated = 0x7f0300e2
com.example.myapplication:dimen/m3_card_elevation = 0x7f0600dd
com.example.myapplication:integer/mtrl_chip_anim_duration = 0x7f090028
com.example.myapplication:style/Widget.AppCompat.PopupMenu.Overflow = 0x7f1102d9
com.example.myapplication:string/m3_sys_typescale_label_small_font = 0x7f10004d
com.example.myapplication:id/accessibility_custom_action_20 = 0x7f08001f
com.example.myapplication:color/bright_foreground_disabled_material_dark = 0x7f050022
com.example.myapplication:style/ThemeOverlay.Material3.FloatingActionButton.Secondary = 0x7f110265
com.example.myapplication:id/accessibility_custom_action_16 = 0x7f08001a
com.example.myapplication:id/bounceEnd = 0x7f080066
com.example.myapplication:styleable/PopupWindowBackgroundState = 0x7f12007b
com.example.myapplication:layout/test_action_chip = 0x7f0b006a
com.example.myapplication:id/x_left = 0x7f080206
com.example.myapplication:dimen/mtrl_transition_shared_axis_slide_distance = 0x7f060223
com.example.myapplication:color/mtrl_choice_chip_ripple_color = 0x7f05020f
com.example.myapplication:dimen/mtrl_switch_thumb_elevation = 0x7f060213
com.example.myapplication:id/accessibility_custom_action_1 = 0x7f080013
com.example.myapplication:id/accessibility_action_clickable_span = 0x7f080011
com.example.myapplication:style/Widget.AppCompat.SeekBar.Discrete = 0x7f1102e3
com.example.myapplication:id/TOP_START = 0x7f08000f
com.example.myapplication:style/Widget.AppCompat.ActionBar.TabView = 0x7f1102a7
com.example.myapplication:attr/checkedIconTint = 0x7f0300a5
com.example.myapplication:id/SecondFragment = 0x7f08000d
com.example.myapplication:color/material_on_surface_emphasis_medium = 0x7f0501f1
com.example.myapplication:attr/thumbElevation = 0x7f03040f
com.example.myapplication:id/SHOW_ALL = 0x7f080009
com.example.myapplication:style/ShapeAppearance.Material3.SmallComponent = 0x7f11014a
com.example.myapplication:string/material_hour_suffix = 0x7f100054
com.example.myapplication:id/tag_state_description = 0x7f0801bd
com.example.myapplication:id/CTRL = 0x7f080003
com.example.myapplication:style/Theme.AppCompat.Dialog.Alert = 0x7f1101e2
com.example.myapplication:id/BOTTOM_START = 0x7f080002
com.example.myapplication:style/Widget.MaterialComponents.MaterialCalendar.DayTextView = 0x7f1103bc
com.example.myapplication:id/startToEnd = 0x7f0801ab
com.example.myapplication:string/abc_menu_sym_shortcut_label = 0x7f100010
com.example.myapplication:id/src_in = 0x7f0801a6
com.example.myapplication:style/RtlOverlay.Widget.AppCompat.Search.DropDown.Query = 0x7f110142
com.example.myapplication:id/BOTTOM_END = 0x7f080001
com.example.myapplication:attr/tickColorActive = 0x7f030417
com.example.myapplication:attr/stackFromEnd = 0x7f030383
com.example.myapplication:id/ALT = 0x7f080000
com.example.myapplication:attr/activityChooserViewStyle = 0x7f030028
com.example.myapplication:color/m3_sys_color_dynamic_dark_on_surface_variant = 0x7f050149
com.example.myapplication:attr/materialClockStyle = 0x7f0302ae
com.example.myapplication:drawable/tooltip_frame_light = 0x7f07009c
com.example.myapplication:layout/item_task = 0x7f0b0031
com.example.myapplication:color/m3_ref_palette_dynamic_tertiary10 = 0x7f0500c3
com.example.myapplication:attr/region_widthLessThan = 0x7f030344
com.example.myapplication:drawable/tooltip_frame_dark = 0x7f07009b
com.example.myapplication:id/buttonPanel = 0x7f080071
com.example.myapplication:drawable/notify_panel_notification_icon_bg = 0x7f070098
com.example.myapplication:id/activity_chooser_view_content = 0x7f08004a
com.example.myapplication:dimen/def_drawer_elevation = 0x7f06005e
com.example.myapplication:id/reverseSawtooth = 0x7f08016d
com.example.myapplication:color/material_dynamic_neutral80 = 0x7f0501aa
com.example.myapplication:layout/design_layout_tab_text = 0x7f0b0024
com.example.myapplication:id/material_hour_tv = 0x7f08010c
com.example.myapplication:styleable/DrawerArrowToggle = 0x7f120033
com.example.myapplication:dimen/mtrl_progress_circular_track_thickness_extra_small = 0x7f0601fb
com.example.myapplication:style/Widget.Material3.Toolbar.Surface = 0x7f11037f
com.example.myapplication:style/Base.TextAppearance.AppCompat.Body1 = 0x7f110014
com.example.myapplication:id/west = 0x7f080200
com.example.myapplication:style/Theme.MaterialComponents.Dialog.FixedSize.Bridge = 0x7f110225
com.example.myapplication:drawable/notification_bg_low_normal = 0x7f070090
com.example.myapplication:color/m3_sys_color_light_primary = 0x7f05017d
com.example.myapplication:drawable/notification_bg = 0x7f07008e
com.example.myapplication:color/design_dark_default_color_secondary_variant = 0x7f05003c
com.example.myapplication:color/material_dynamic_neutral99 = 0x7f0501ad
com.example.myapplication:attr/tickVisible = 0x7f03041c
com.example.myapplication:drawable/mtrl_tabs_default_indicator = 0x7f07008b
com.example.myapplication:drawable/abc_text_cursor_material = 0x7f07004c
com.example.myapplication:drawable/mtrl_dropdown_arrow = 0x7f070083
com.example.myapplication:drawable/material_ic_keyboard_arrow_previous_black_24dp = 0x7f07007e
com.example.myapplication:color/highlighted_text_material_dark = 0x7f05005d
com.example.myapplication:attr/textAppearanceTitleMedium = 0x7f0303ee
com.example.myapplication:drawable/$avd_show_password__2 = 0x7f070005
com.example.myapplication:attr/keylines = 0x7f030229
com.example.myapplication:drawable/material_ic_keyboard_arrow_next_black_24dp = 0x7f07007d
com.example.myapplication:attr/subheaderColor = 0x7f030397
com.example.myapplication:string/search_menu_title = 0x7f100096
com.example.myapplication:style/Widget.AppCompat.Spinner.DropDown.ActionBar = 0x7f1102e6
com.example.myapplication:string/androidx_startup = 0x7f10001e
com.example.myapplication:style/ThemeOverlay.Material3.TextInputEditText.OutlinedBox.Dense = 0x7f110277
com.example.myapplication:id/info = 0x7f0800ea
com.example.myapplication:style/RtlOverlay.Widget.AppCompat.SearchView.MagIcon = 0x7f110144
com.example.myapplication:id/right = 0x7f08016e
com.example.myapplication:style/Widget.Material3.CollapsingToolbar.Large = 0x7f11032a
com.example.myapplication:id/sharedValueSet = 0x7f08018c
com.example.myapplication:id/showHome = 0x7f080190
com.example.myapplication:dimen/test_dimen = 0x7f060234
com.example.myapplication:id/graph_wrap = 0x7f0800d7
com.example.myapplication:id/month_grid = 0x7f08011b
com.example.myapplication:dimen/m3_bottom_nav_min_height = 0x7f0600ba
com.example.myapplication:style/Widget.AppCompat.Button.Colored = 0x7f1102b2
com.example.myapplication:drawable/m3_tabs_rounded_line_indicator = 0x7f070076
com.example.myapplication:drawable/m3_appbar_background = 0x7f070070
com.example.myapplication:attr/flow_firstHorizontalBias = 0x7f0301b2
com.example.myapplication:attr/motionProgress = 0x7f0302e6
com.example.myapplication:drawable/ic_mtrl_checked_circle = 0x7f07006c
com.example.myapplication:id/animateToEnd = 0x7f080052
com.example.myapplication:attr/placeholderText = 0x7f03031c
com.example.myapplication:attr/queryPatterns = 0x7f030337
com.example.myapplication:id/triangle = 0x7f0801e7
com.example.myapplication:color/m3_ref_palette_dynamic_secondary0 = 0x7f0500b5
com.example.myapplication:drawable/material_cursor_drawable = 0x7f070078
com.example.myapplication:style/RtlUnderlay.Widget.AppCompat.ActionButton = 0x7f110145
com.example.myapplication:id/inward = 0x7f0800ec
com.example.myapplication:attr/floatingActionButtonTertiaryStyle = 0x7f0301b1
com.example.myapplication:attr/titleMarginStart = 0x7f030426
com.example.myapplication:attr/helperTextTextColor = 0x7f0301e0
com.example.myapplication:color/abc_color_highlight_material = 0x7f050004
com.example.myapplication:id/accessibility_custom_action_8 = 0x7f080030
com.example.myapplication:dimen/abc_button_inset_horizontal_material = 0x7f060012
com.example.myapplication:dimen/design_bottom_navigation_item_max_width = 0x7f060067
com.example.myapplication:integer/m3_sys_motion_duration_medium2 = 0x7f090012
com.example.myapplication:attr/boxCornerRadiusTopStart = 0x7f030075
com.example.myapplication:id/dimensions = 0x7f0800a8
com.example.myapplication:drawable/design_snackbar_background = 0x7f070064
com.example.myapplication:attr/itemMaxLines = 0x7f030212
com.example.myapplication:attr/chipStyle = 0x7f0300ba
com.example.myapplication:dimen/mtrl_progress_indicator_full_rounded_corner_radius = 0x7f0601fe
com.example.myapplication:drawable/btn_radio_on_mtrl = 0x7f07005e
com.example.myapplication:drawable/abc_btn_radio_to_on_mtrl_015 = 0x7f070013
com.example.myapplication:style/Widget.MaterialComponents.MaterialCalendar.HeaderLayout = 0x7f1103c1
com.example.myapplication:style/Widget.Material3.MaterialCalendar.YearNavigationButton = 0x7f110357
com.example.myapplication:styleable/FragmentNavigator = 0x7f12003f
com.example.myapplication:drawable/btn_radio_off_to_on_mtrl_animation = 0x7f07005d
com.example.myapplication:style/Widget.MaterialComponents.CircularProgressIndicator.ExtraSmall = 0x7f1103a9
com.example.myapplication:drawable/ic_launcher_background = 0x7f070067
com.example.myapplication:color/m3_sys_color_dynamic_light_tertiary_container = 0x7f05016a
com.example.myapplication:dimen/m3_sys_typescale_title_large_letter_spacing = 0x7f060134
com.example.myapplication:attr/fontProviderFetchTimeout = 0x7f0301ca
com.example.myapplication:color/m3_sys_color_dark_error = 0x7f050126
com.example.myapplication:dimen/mtrl_tooltip_minHeight = 0x7f060220
com.example.myapplication:color/m3_ref_palette_tertiary99 = 0x7f05011c
com.example.myapplication:dimen/mtrl_fab_translation_z_hovered_focused = 0x7f0601d5
com.example.myapplication:drawable/btn_radio_off_mtrl = 0x7f07005c
com.example.myapplication:attr/textInputLayoutFocusedRectEnabled = 0x7f0303fc
com.example.myapplication:style/Widget.Material3.AutoCompleteTextView.FilledBox = 0x7f1102fc
com.example.myapplication:color/m3_sys_color_dark_secondary_container = 0x7f05013a
com.example.myapplication:drawable/btn_checkbox_unchecked_mtrl = 0x7f07005a
com.example.myapplication:style/Base.Widget.AppCompat.RatingBar.Small = 0x7f1100e6
com.example.myapplication:style/Widget.MaterialComponents.MaterialCalendar.HeaderTitle = 0x7f1103c4
com.example.myapplication:layout/test_toolbar_elevation = 0x7f0b0072
com.example.myapplication:drawable/btn_checkbox_checked_to_unchecked_mtrl_animation = 0x7f070059
com.example.myapplication:id/accessibility_custom_action_11 = 0x7f080015
com.example.myapplication:attr/materialCalendarHeaderSelection = 0x7f0302a1
com.example.myapplication:drawable/btn_checkbox_checked_mtrl = 0x7f070058
com.example.myapplication:id/mtrl_picker_fullscreen = 0x7f08012f
com.example.myapplication:drawable/abc_textfield_search_material = 0x7f070054
com.example.myapplication:id/neverCompleteToEnd = 0x7f080149
com.example.myapplication:style/RtlOverlay.Widget.AppCompat.DialogTitle.Icon = 0x7f110138
com.example.myapplication:drawable/abc_textfield_default_mtrl_alpha = 0x7f070051
com.example.myapplication:drawable/abc_text_select_handle_left_mtrl = 0x7f07004d
com.example.myapplication:style/Base.ThemeOverlay.MaterialComponents.Dialog.Alert.Framework = 0x7f110080
com.example.myapplication:style/Base.ThemeOverlay.MaterialComponents.MaterialAlertDialog = 0x7f110082
com.example.myapplication:drawable/abc_tab_indicator_material = 0x7f07004a
com.example.myapplication:attr/subtitleCentered = 0x7f03039d
com.example.myapplication:interpolator/btn_checkbox_unchecked_mtrl_animation_interpolator_1 = 0x7f0a0003
com.example.myapplication:style/TextAppearance.Material3.DisplayLarge = 0x7f1101b7
com.example.myapplication:attr/badgeTextColor = 0x7f030053
com.example.myapplication:integer/mtrl_calendar_year_selector_span = 0x7f090025
com.example.myapplication:drawable/abc_star_black_48dp = 0x7f070046
com.example.myapplication:style/Widget.Material3.MaterialCalendar.Day = 0x7f110343
com.example.myapplication:style/Theme.MaterialComponents.BottomSheetDialog = 0x7f11020d
com.example.myapplication:string/mtrl_picker_text_input_year_abbr = 0x7f100083
com.example.myapplication:style/Theme.MaterialComponents.DayNight = 0x7f110210
com.example.myapplication:id/bottom = 0x7f080063
com.example.myapplication:string/abc_action_bar_home_description = 0x7f100000
com.example.myapplication:attr/destination = 0x7f03014b
com.example.myapplication:color/material_dynamic_primary100 = 0x7f0501bd
com.example.myapplication:id/line3 = 0x7f0800fb
com.example.myapplication:style/Platform.ThemeOverlay.AppCompat.Dark = 0x7f11012f
com.example.myapplication:dimen/m3_divider_heavy_thickness = 0x7f0600e8
com.example.myapplication:style/Widget.MaterialComponents.Snackbar.TextView = 0x7f1103dd
com.example.myapplication:drawable/abc_scrubber_control_to_pressed_mtrl_005 = 0x7f07003e
com.example.myapplication:styleable/CoordinatorLayout_Layout = 0x7f120030
com.example.myapplication:id/accessibility_custom_action_3 = 0x7f080029
com.example.myapplication:styleable/ExtendedFloatingActionButton = 0x7f120035
com.example.myapplication:style/Widget.MaterialComponents.TextInputEditText.OutlinedBox = 0x7f1103e3
com.example.myapplication:drawable/abc_scrubber_control_to_pressed_mtrl_000 = 0x7f07003d
com.example.myapplication:drawable/abc_ratingbar_small_material = 0x7f07003b
com.example.myapplication:drawable/abc_ratingbar_material = 0x7f07003a
com.example.myapplication:style/Base.TextAppearance.AppCompat.SearchResult = 0x7f110025
com.example.myapplication:attr/bottomSheetDialogTheme = 0x7f03006d
com.example.myapplication:dimen/mtrl_calendar_maximum_default_fullscreen_minor_axis = 0x7f0601a3
com.example.myapplication:integer/mtrl_view_visible = 0x7f09002c
com.example.myapplication:styleable/ListPopupWindow = 0x7f120050
com.example.myapplication:drawable/abc_ratingbar_indicator_material = 0x7f070039
com.example.myapplication:drawable/abc_menu_hardkey_panel_mtrl_mult = 0x7f070037
com.example.myapplication:id/chip2 = 0x7f080081
com.example.myapplication:style/Widget.Material3.Light.ActionBar.Solid = 0x7f110340
com.example.myapplication:drawable/abc_list_pressed_holo_dark = 0x7f07002f
com.example.myapplication:style/Widget.AppCompat.ListMenuView = 0x7f1102d3
com.example.myapplication:id/material_clock_period_pm_button = 0x7f080109
com.example.myapplication:style/Base.MaterialAlertDialog.MaterialComponents.Title.Panel = 0x7f110011
com.example.myapplication:attr/thumbStrokeWidth = 0x7f030412
com.example.myapplication:style/Theme.Design.BottomSheetDialog = 0x7f1101ef
com.example.myapplication:dimen/mtrl_progress_circular_track_thickness_medium = 0x7f0601fc
com.example.myapplication:drawable/abc_list_divider_mtrl_alpha = 0x7f07002c
com.example.myapplication:id/recycler_tasks = 0x7f08016c
com.example.myapplication:dimen/m3_sys_typescale_label_medium_letter_spacing = 0x7f060130
com.example.myapplication:style/ThemeOverlay.Material3.FloatingActionButton.Tertiary = 0x7f110267
com.example.myapplication:style/Widget.AppCompat.RatingBar = 0x7f1102dd
com.example.myapplication:drawable/abc_list_divider_material = 0x7f07002b
com.example.myapplication:drawable/abc_item_background_holo_dark = 0x7f070029
com.example.myapplication:attr/hideAnimationBehavior = 0x7f0301e1
com.example.myapplication:attr/autoSizeMaxTextSize = 0x7f03003f
com.example.myapplication:drawable/m3_tabs_line_indicator = 0x7f070075
com.example.myapplication:drawable/abc_ic_menu_selectall_mtrl_alpha = 0x7f070025
com.example.myapplication:style/Widget.AppCompat.DropDownItem.Spinner = 0x7f1102ba
com.example.myapplication:style/Widget.Material3.MaterialCalendar.Day.Today = 0x7f110346
com.example.myapplication:drawable/abc_ic_voice_search_api_material = 0x7f070028
com.example.myapplication:style/Base.ThemeOverlay.Material3.BottomSheetDialog = 0x7f11007b
com.example.myapplication:color/m3_ref_palette_error95 = 0x7f0500da
com.example.myapplication:dimen/mtrl_calendar_header_divider_thickness = 0x7f06019b
com.example.myapplication:drawable/abc_ic_menu_overflow_material = 0x7f070023
com.example.myapplication:id/none = 0x7f08014d
com.example.myapplication:drawable/abc_ic_clear_material = 0x7f07001e
com.example.myapplication:attr/paddingTopNoTitle = 0x7f030309
com.example.myapplication:drawable/abc_ic_arrow_drop_right_black_24dp = 0x7f07001d
com.example.myapplication:styleable/ActivityFilter = 0x7f120006
com.example.myapplication:layout/abc_select_dialog_material = 0x7f0b001a
com.example.myapplication:style/Base.Widget.AppCompat.CompoundButton.CheckBox = 0x7f1100ca
com.example.myapplication:style/Theme.AppCompat.DayNight.NoActionBar = 0x7f1101e0
com.example.myapplication:drawable/abc_btn_switch_to_on_mtrl_00012 = 0x7f070015
com.example.myapplication:drawable/abc_btn_radio_to_on_mtrl_000 = 0x7f070012
com.example.myapplication:style/Widget.Material3.FloatingActionButton.Large.Tertiary = 0x7f11033b
com.example.myapplication:drawable/abc_btn_check_to_on_mtrl_015 = 0x7f07000d
com.example.myapplication:style/Widget.Material3.Button.TextButton.Dialog.Icon = 0x7f110310
com.example.myapplication:attr/itemBackground = 0x7f03020b
com.example.myapplication:id/scrollable = 0x7f08017e
com.example.myapplication:style/Widget.MaterialComponents.MaterialCalendar = 0x7f1103b6
com.example.myapplication:drawable/abc_btn_check_material = 0x7f07000a
com.example.myapplication:style/Widget.AppCompat.Spinner.DropDown = 0x7f1102e5
com.example.myapplication:color/material_dynamic_secondary95 = 0x7f0501d3
com.example.myapplication:dimen/notification_media_narrow_margin = 0x7f06022b
com.example.myapplication:style/Base.TextAppearance.AppCompat.Headline = 0x7f11001c
com.example.myapplication:id/group_divider = 0x7f0800d8
com.example.myapplication:attr/materialCalendarHeaderTitle = 0x7f0302a2
com.example.myapplication:layout/fragment_task_detail = 0x7f0b0030
com.example.myapplication:style/Widget.AppCompat.Spinner.Underlined = 0x7f1102e7
com.example.myapplication:color/secondary_text_default_material_light = 0x7f050242
com.example.myapplication:drawable/$ic_launcher_foreground__0 = 0x7f070006
com.example.myapplication:style/TextAppearance.AppCompat.Light.Widget.PopupMenu.Small = 0x7f110182
com.example.myapplication:id/accessibility_custom_action_23 = 0x7f080022
com.example.myapplication:style/Widget.AppCompat.Light.ActionBar.TabBar.Inverse = 0x7f1102c1
com.example.myapplication:dimen/material_clock_hand_padding = 0x7f060140
com.example.myapplication:drawable/$avd_show_password__0 = 0x7f070003
com.example.myapplication:style/Widget.Material3.MaterialTimePicker = 0x7f11035a
com.example.myapplication:color/m3_sys_color_dark_primary_container = 0x7f050138
com.example.myapplication:drawable/$avd_hide_password__2 = 0x7f070002
com.example.myapplication:dimen/design_navigation_separator_vertical_padding = 0x7f06007f
com.example.myapplication:drawable/$avd_hide_password__1 = 0x7f070001
com.example.myapplication:string/mtrl_picker_invalid_range = 0x7f100075
com.example.myapplication:drawable/$avd_hide_password__0 = 0x7f070000
com.example.myapplication:dimen/tooltip_y_offset_non_touch = 0x7f060247
com.example.myapplication:style/ThemeOverlay.MaterialComponents.Toolbar.Surface = 0x7f1102a1
com.example.myapplication:style/Widget.AppCompat.Light.ActionButton = 0x7f1102c6
com.example.myapplication:dimen/tooltip_horizontal_padding = 0x7f060242
com.example.myapplication:dimen/design_bottom_navigation_active_text_size = 0x7f060063
com.example.myapplication:style/Base.V14.Theme.Material3.Dark = 0x7f110083
com.example.myapplication:dimen/test_navigation_bar_text_size = 0x7f060240
com.example.myapplication:string/path_password_eye = 0x7f100090
com.example.myapplication:style/Widget.MaterialComponents.CheckedTextView = 0x7f1103a2
com.example.myapplication:style/Widget.MaterialComponents.ShapeableImageView = 0x7f1103d9
com.example.myapplication:style/Base.Widget.AppCompat.ListPopupWindow = 0x7f1100db
com.example.myapplication:dimen/test_navigation_bar_item_min_width = 0x7f06023d
com.example.myapplication:style/Base.V21.Theme.MaterialComponents = 0x7f11009b
com.example.myapplication:dimen/test_navigation_bar_elevation = 0x7f060239
com.example.myapplication:style/Base.Widget.AppCompat.Button.ButtonBar.AlertDialog = 0x7f1100c5
com.example.myapplication:dimen/mtrl_slider_halo_radius = 0x7f060203
com.example.myapplication:dimen/notification_right_icon_size = 0x7f06022c
com.example.myapplication:animator/m3_card_elevated_state_list_anim = 0x7f02000f
com.example.myapplication:dimen/notification_main_column_padding_top = 0x7f06022a
com.example.myapplication:dimen/mtrl_textinput_box_stroke_width_default = 0x7f060217
com.example.myapplication:string/mtrl_picker_range_header_unselected = 0x7f10007c
com.example.myapplication:style/ThemeOverlay.Material3.Dialog = 0x7f11025e
com.example.myapplication:dimen/notification_action_text_size = 0x7f060225
com.example.myapplication:style/Base.V14.Theme.Material3.Light = 0x7f110086
com.example.myapplication:dimen/notification_action_icon_size = 0x7f060224
com.example.myapplication:dimen/abc_dialog_min_width_major = 0x7f060022
com.example.myapplication:color/m3_ref_palette_dynamic_primary10 = 0x7f0500a9
com.example.myapplication:dimen/mtrl_tooltip_minWidth = 0x7f060221
com.example.myapplication:dimen/design_bottom_navigation_text_size = 0x7f06006c
com.example.myapplication:color/m3_sys_color_dynamic_dark_on_tertiary_container = 0x7f05014b
com.example.myapplication:dimen/mtrl_textinput_end_icon_margin_start = 0x7f06021a
com.example.myapplication:dimen/design_bottom_navigation_margin = 0x7f06006a
com.example.myapplication:dimen/mtrl_textinput_box_label_cutout_padding = 0x7f060216
com.example.myapplication:dimen/mtrl_textinput_box_corner_radius_small = 0x7f060215
com.example.myapplication:attr/layout_constrainedHeight = 0x7f030239
com.example.myapplication:dimen/mtrl_textinput_box_corner_radius_medium = 0x7f060214
com.example.myapplication:style/Base.Widget.AppCompat.Button = 0x7f1100c2
com.example.myapplication:id/tag_screen_reader_focusable = 0x7f0801bc
com.example.myapplication:dimen/mtrl_slider_track_top = 0x7f06020b
com.example.myapplication:style/Base.TextAppearance.AppCompat.Menu = 0x7f110024
com.example.myapplication:dimen/mtrl_slider_track_side_padding = 0x7f06020a
com.example.myapplication:layout/material_clock_period_toggle = 0x7f0b0038
com.example.myapplication:dimen/m3_chip_corner_size = 0x7f0600e1
com.example.myapplication:color/material_dynamic_primary40 = 0x7f0501c0
com.example.myapplication:dimen/mtrl_progress_circular_size_small = 0x7f0601fa
com.example.myapplication:dimen/mtrl_progress_circular_inset = 0x7f0601f2
com.example.myapplication:style/Theme.AppCompat.Light.Dialog.Alert = 0x7f1101e9
com.example.myapplication:dimen/mtrl_navigation_rail_margin = 0x7f0601ef
com.example.myapplication:dimen/mtrl_navigation_rail_icon_margin = 0x7f0601ed
com.example.myapplication:styleable/ScrollingViewBehavior_Layout = 0x7f120082
com.example.myapplication:dimen/mtrl_navigation_rail_default_width = 0x7f0601eb
com.example.myapplication:color/material_dynamic_neutral30 = 0x7f0501a5
com.example.myapplication:styleable/ViewBackgroundHelper = 0x7f1200a0
com.example.myapplication:attr/splitMinSmallestWidth = 0x7f030379
com.example.myapplication:id/material_hour_text_input = 0x7f08010b
com.example.myapplication:dimen/cardview_default_radius = 0x7f060055
com.example.myapplication:id/navigation_header_container = 0x7f080147
com.example.myapplication:dimen/mtrl_navigation_item_shape_horizontal_margin = 0x7f0601e7
com.example.myapplication:dimen/mtrl_navigation_elevation = 0x7f0601e3
com.example.myapplication:drawable/abc_textfield_search_default_mtrl_alpha = 0x7f070053
com.example.myapplication:dimen/notification_large_icon_width = 0x7f060229
com.example.myapplication:id/mtrl_picker_header = 0x7f080130
com.example.myapplication:dimen/m3_navigation_item_shape_inset_top = 0x7f0600fc
com.example.myapplication:styleable/Spinner = 0x7f120089
com.example.myapplication:style/Widget.MaterialComponents.Light.ActionBar.Solid = 0x7f1103b3
com.example.myapplication:dimen/mtrl_navigation_bar_item_default_margin = 0x7f0601e2
com.example.myapplication:color/m3_sys_color_light_on_primary = 0x7f050174
com.example.myapplication:dimen/mtrl_low_ripple_default_alpha = 0x7f0601dc
com.example.myapplication:color/mtrl_navigation_item_icon_tint = 0x7f05021e
com.example.myapplication:id/scroll = 0x7f08017a
com.example.myapplication:dimen/mtrl_high_ripple_hovered_alpha = 0x7f0601d9
com.example.myapplication:style/Base.V23.Theme.AppCompat = 0x7f1100a4
com.example.myapplication:styleable/ActionMode = 0x7f120004
com.example.myapplication:id/endToStart = 0x7f0800bf
com.example.myapplication:attr/flow_verticalAlign = 0x7f0301c0
com.example.myapplication:dimen/mtrl_high_ripple_default_alpha = 0x7f0601d7
com.example.myapplication:attr/spinnerDropDownItemStyle = 0x7f030376
com.example.myapplication:drawable/ic_m3_chip_check = 0x7f070069
com.example.myapplication:dimen/mtrl_extended_fab_top_padding = 0x7f0601cf
com.example.myapplication:attr/navigationViewStyle = 0x7f0302f3
com.example.myapplication:color/m3_sys_color_dark_on_primary = 0x7f05012e
com.example.myapplication:color/m3_sys_color_dynamic_light_tertiary = 0x7f050169
com.example.myapplication:dimen/mtrl_extended_fab_start_padding = 0x7f0601cd
com.example.myapplication:dimen/mtrl_extended_fab_min_width = 0x7f0601cc
com.example.myapplication:style/Theme.AppCompat.Light.DialogWhenLarge = 0x7f1101eb
com.example.myapplication:attr/autoSizeStepGranularity = 0x7f030042
com.example.myapplication:dimen/test_navigation_bar_active_item_max_width = 0x7f060236
com.example.myapplication:dimen/mtrl_extended_fab_icon_text_spacing = 0x7f0601ca
com.example.myapplication:dimen/mtrl_extended_fab_end_padding_icon = 0x7f0601c8
com.example.myapplication:color/m3_sys_color_dark_on_surface = 0x7f050132
com.example.myapplication:dimen/mtrl_extended_fab_end_padding = 0x7f0601c7
com.example.myapplication:dimen/mtrl_extended_fab_disabled_translation_z = 0x7f0601c5
com.example.myapplication:dimen/mtrl_large_touch_target = 0x7f0601db
com.example.myapplication:dimen/mtrl_extended_fab_corner_radius = 0x7f0601c3
com.example.myapplication:attr/uri = 0x7f03044d
com.example.myapplication:color/design_error = 0x7f05004b
com.example.myapplication:layout/abc_activity_chooser_view_list_item = 0x7f0b0007
com.example.myapplication:id/accessibility_custom_action_21 = 0x7f080020
com.example.myapplication:dimen/mtrl_extended_fab_bottom_padding = 0x7f0601c2
com.example.myapplication:dimen/mtrl_exposed_dropdown_menu_popup_vertical_padding = 0x7f0601c1
com.example.myapplication:dimen/mtrl_exposed_dropdown_menu_popup_vertical_offset = 0x7f0601c0
com.example.myapplication:dimen/mtrl_exposed_dropdown_menu_popup_elevation = 0x7f0601bf
com.example.myapplication:dimen/mtrl_edittext_rectangle_top_offset = 0x7f0601be
com.example.myapplication:integer/mtrl_card_anim_delay_ms = 0x7f090026
com.example.myapplication:dimen/mtrl_chip_pressed_translation_z = 0x7f0601bc
com.example.myapplication:color/m3_ref_palette_neutral_variant0 = 0x7f0500e9
com.example.myapplication:dimen/mtrl_card_spacing = 0x7f0601bb
com.example.myapplication:attr/mock_showDiagonals = 0x7f0302cd
com.example.myapplication:color/mtrl_popupmenu_overlay_color = 0x7f050224
com.example.myapplication:color/m3_ref_palette_neutral95 = 0x7f0500e7
com.example.myapplication:dimen/m3_btn_icon_btn_padding_left = 0x7f0600c6
com.example.myapplication:dimen/mtrl_card_elevation = 0x7f0601ba
com.example.myapplication:attr/textFillColor = 0x7f0303f8
com.example.myapplication:string/m3_sys_typescale_display_small_font = 0x7f100047
com.example.myapplication:color/mtrl_textinput_hovered_box_stroke_color = 0x7f050230
com.example.myapplication:dimen/mtrl_card_corner_radius = 0x7f0601b8
com.example.myapplication:attr/graph = 0x7f0301d7
com.example.myapplication:dimen/mtrl_calendar_year_horizontal_padding = 0x7f0601b3
com.example.myapplication:dimen/mtrl_calendar_year_height = 0x7f0601b2
com.example.myapplication:dimen/mtrl_calendar_title_baseline_to_top_fullscreen = 0x7f0601b0
com.example.myapplication:dimen/test_navigation_bar_shadow_height = 0x7f06023f
com.example.myapplication:dimen/mtrl_calendar_selection_text_baseline_to_bottom = 0x7f0601ab
com.example.myapplication:attr/paddingTopSystemWindowInsets = 0x7f03030a
com.example.myapplication:integer/material_motion_duration_short_1 = 0x7f09001d
com.example.myapplication:dimen/mtrl_calendar_pre_l_text_clip_padding = 0x7f0601a9
com.example.myapplication:dimen/mtrl_calendar_navigation_height = 0x7f0601a7
com.example.myapplication:dimen/mtrl_calendar_month_vertical_padding = 0x7f0601a5
com.example.myapplication:dimen/m3_btn_disabled_translation_z = 0x7f0600c3
com.example.myapplication:dimen/sliding_pane_detail_pane_width = 0x7f060233
com.example.myapplication:attr/counterMaxLength = 0x7f03012c
com.example.myapplication:dimen/mtrl_calendar_header_toggle_margin_bottom = 0x7f0601a0
com.example.myapplication:bool/abc_action_bar_embed_tabs = 0x7f040000
com.example.myapplication:attr/viewTransitionOnCross = 0x7f030455
com.example.myapplication:id/currentState = 0x7f080097
com.example.myapplication:layout/material_clock_display = 0x7f0b0036
com.example.myapplication:style/Widget.Design.FloatingActionButton = 0x7f1102f2
com.example.myapplication:attr/textAppearanceLabelMedium = 0x7f0303df
com.example.myapplication:dimen/mtrl_calendar_header_height_fullscreen = 0x7f06019d
com.example.myapplication:style/Theme.MyApplication = 0x7f11023d
com.example.myapplication:color/m3_popupmenu_overlay_color = 0x7f05008b
com.example.myapplication:style/TextAppearance.MaterialComponents.Chip = 0x7f1101c9
com.example.myapplication:style/Widget.MaterialComponents.ChipGroup = 0x7f1103a7
com.example.myapplication:attr/drawableSize = 0x7f030160
com.example.myapplication:dimen/material_filled_edittext_font_2_0_padding_top = 0x7f060152
com.example.myapplication:dimen/mtrl_calendar_header_height = 0x7f06019c
com.example.myapplication:attr/layout_behavior = 0x7f030236
com.example.myapplication:style/Widget.AppCompat.Light.ListPopupWindow = 0x7f1102cd
com.example.myapplication:id/left = 0x7f0800f7
com.example.myapplication:dimen/mtrl_calendar_dialog_background_inset = 0x7f060198
com.example.myapplication:style/Base.TextAppearance.AppCompat.Widget.ActionMode.Subtitle = 0x7f110034
com.example.myapplication:dimen/mtrl_calendar_days_of_week_height = 0x7f060197
com.example.myapplication:id/spline = 0x7f08019f
com.example.myapplication:color/abc_tint_seek_thumb = 0x7f050016
com.example.myapplication:dimen/mtrl_calendar_day_width = 0x7f060196
com.example.myapplication:layout/material_timepicker = 0x7f0b0040
com.example.myapplication:dimen/mtrl_calendar_day_vertical_padding = 0x7f060195
com.example.myapplication:dimen/mtrl_calendar_day_height = 0x7f060192
com.example.myapplication:style/Theme.Design.NoActionBar = 0x7f1101f3
com.example.myapplication:dimen/mtrl_calendar_bottom_padding = 0x7f06018f
com.example.myapplication:dimen/mtrl_btn_text_size = 0x7f06018a
com.example.myapplication:dimen/mtrl_btn_text_btn_padding_right = 0x7f060189
com.example.myapplication:dimen/mtrl_btn_stroke_size = 0x7f060186
com.example.myapplication:id/clip_vertical = 0x7f080088
com.example.myapplication:attr/materialCalendarHeaderCancelButton = 0x7f03029d
com.example.myapplication:dimen/mtrl_btn_snackbar_margin_horizontal = 0x7f060185
com.example.myapplication:layout/design_layout_snackbar_include = 0x7f0b0022
com.example.myapplication:dimen/mtrl_btn_max_width = 0x7f06017f
com.example.myapplication:dimen/mtrl_btn_hovered_z = 0x7f06017a
com.example.myapplication:string/mtrl_timepicker_confirm = 0x7f100088
com.example.myapplication:layout/design_menu_item_action_area = 0x7f0b0025
com.example.myapplication:dimen/mtrl_btn_disabled_elevation = 0x7f060176
com.example.myapplication:attr/expanded = 0x7f030188
com.example.myapplication:dimen/mtrl_btn_corner_radius = 0x7f060174
com.example.myapplication:style/Widget.Material3.TextInputEditText.FilledBox = 0x7f110371
com.example.myapplication:drawable/abc_btn_borderless_material = 0x7f070009
com.example.myapplication:dimen/mtrl_bottomappbar_fab_cradle_vertical_offset = 0x7f060172
com.example.myapplication:dimen/mtrl_bottomappbar_fab_bottom_margin = 0x7f06016f
com.example.myapplication:dimen/mtrl_badge_toolbar_action_menu_item_vertical_offset = 0x7f06016c
com.example.myapplication:dimen/mtrl_calendar_text_input_padding_top = 0x7f0601ae
com.example.myapplication:dimen/mtrl_badge_horizontal_edge_offset = 0x7f060166
com.example.myapplication:style/TextAppearance.AppCompat.Menu = 0x7f110185
com.example.myapplication:dimen/mtrl_alert_dialog_picker_background_inset = 0x7f060165
com.example.myapplication:id/icon = 0x7f0800e2
com.example.myapplication:dimen/material_timepicker_dialog_buttons_margin_top = 0x7f060160
com.example.myapplication:interpolator/mtrl_linear_out_slow_in = 0x7f0a000a
com.example.myapplication:dimen/material_time_picker_minimum_screen_height = 0x7f06015e
com.example.myapplication:dimen/material_textinput_max_width = 0x7f06015c
com.example.myapplication:style/Widget.AppCompat.RatingBar.Small = 0x7f1102df
com.example.myapplication:color/m3_sys_color_dynamic_dark_secondary = 0x7f05014f
com.example.myapplication:dimen/material_textinput_default_width = 0x7f06015b
com.example.myapplication:dimen/m3_btn_padding_right = 0x7f0600d0
com.example.myapplication:dimen/material_helper_text_font_1_3_padding_horizontal = 0x7f060156
com.example.myapplication:dimen/material_helper_text_default_padding_top = 0x7f060155
com.example.myapplication:style/Base.Theme.AppCompat.Dialog.MinWidth = 0x7f110051
com.example.myapplication:dimen/material_font_2_0_box_collapsed_padding_top = 0x7f060154
com.example.myapplication:dimen/material_font_1_3_box_collapsed_padding_top = 0x7f060153
com.example.myapplication:color/material_dynamic_primary80 = 0x7f0501c4
com.example.myapplication:dimen/material_filled_edittext_font_1_3_padding_top = 0x7f060150
com.example.myapplication:dimen/material_emphasis_disabled = 0x7f06014b
com.example.myapplication:style/Widget.Design.BottomNavigationView = 0x7f1102ef
com.example.myapplication:dimen/m3_sys_typescale_title_medium_letter_spacing = 0x7f060136
com.example.myapplication:style/Widget.MaterialComponents.Chip.Entry = 0x7f1103a5
com.example.myapplication:style/Widget.AppCompat.Button.Borderless.Colored = 0x7f1102b0
com.example.myapplication:dimen/material_clock_size = 0x7f060146
com.example.myapplication:style/Base.TextAppearance.AppCompat.Widget.Button = 0x7f110036
com.example.myapplication:dimen/material_clock_period_toggle_margin_left = 0x7f060144
com.example.myapplication:style/Widget.Material3.AutoCompleteTextView.OutlinedBox = 0x7f1102fe
com.example.myapplication:color/material_dynamic_primary30 = 0x7f0501bf
com.example.myapplication:dimen/material_clock_period_toggle_height = 0x7f060143
com.example.myapplication:attr/flow_lastHorizontalStyle = 0x7f0301bb
com.example.myapplication:dimen/material_clock_hand_stroke_width = 0x7f060141
com.example.myapplication:dimen/material_clock_face_margin_top = 0x7f06013e
com.example.myapplication:dimen/m3_timepicker_window_elevation = 0x7f06013b
com.example.myapplication:dimen/notification_small_icon_background_padding = 0x7f06022e
com.example.myapplication:attr/contentInsetStartWithNavigation = 0x7f030114
com.example.myapplication:dimen/m3_timepicker_display_stroke_width = 0x7f06013a
com.example.myapplication:attr/mock_labelBackgroundColor = 0x7f0302cb
com.example.myapplication:dimen/m3_sys_typescale_title_large_text_size = 0x7f060135
com.example.myapplication:dimen/m3_sys_typescale_label_small_letter_spacing = 0x7f060132
com.example.myapplication:color/m3_ref_palette_primary10 = 0x7f0500f7
com.example.myapplication:dimen/m3_sys_typescale_label_large_text_size = 0x7f06012f
com.example.myapplication:attr/actionBarDivider = 0x7f030003
com.example.myapplication:attr/dividerHorizontal = 0x7f030152
com.example.myapplication:dimen/design_textinput_caption_translate_y = 0x7f06008f
com.example.myapplication:dimen/m3_sys_typescale_label_large_letter_spacing = 0x7f06012e
com.example.myapplication:styleable/Motion = 0x7f120065
com.example.myapplication:style/ShapeAppearanceOverlay.MaterialAlertDialog.Material3 = 0x7f11015c
com.example.myapplication:id/mtrl_calendar_day_selector_frame = 0x7f080123
com.example.myapplication:drawable/notification_template_icon_bg = 0x7f070095
com.example.myapplication:id/match_constraint = 0x7f080103
com.example.myapplication:dimen/m3_sys_typescale_headline_medium_text_size = 0x7f06012b
com.example.myapplication:dimen/m3_sys_typescale_headline_medium_letter_spacing = 0x7f06012a
com.example.myapplication:menu/example_menu = 0x7f0c0000
com.example.myapplication:attr/customStringValue = 0x7f03013d
com.example.myapplication:dimen/m3_sys_typescale_headline_large_text_size = 0x7f060129
com.example.myapplication:attr/argType = 0x7f030039
com.example.myapplication:dimen/m3_sys_typescale_headline_large_letter_spacing = 0x7f060128
com.example.myapplication:dimen/m3_sys_typescale_display_small_text_size = 0x7f060127
com.example.myapplication:drawable/btn_checkbox_unchecked_to_checked_mtrl_animation = 0x7f07005b
com.example.myapplication:dimen/m3_sys_typescale_display_small_letter_spacing = 0x7f060126
com.example.myapplication:attr/textAppearanceBodySmall = 0x7f0303cf
com.example.myapplication:style/Base.TextAppearance.MaterialComponents.Headline6 = 0x7f110047
com.example.myapplication:dimen/m3_sys_typescale_display_medium_letter_spacing = 0x7f060124
com.example.myapplication:attr/motionDurationShort1 = 0x7f0302d4
com.example.myapplication:dimen/m3_sys_typescale_display_large_text_size = 0x7f060123
com.example.myapplication:color/material_dynamic_neutral_variant70 = 0x7f0501b6
com.example.myapplication:attr/layout_collapseMode = 0x7f030237
com.example.myapplication:color/m3_chip_background_color = 0x7f05006d
com.example.myapplication:color/m3_ref_palette_dynamic_neutral80 = 0x7f050097
com.example.myapplication:id/fill_horizontal = 0x7f0800c9
com.example.myapplication:dimen/m3_sys_typescale_body_small_text_size = 0x7f060121
com.example.myapplication:style/Base.Widget.AppCompat.Light.ActionBar.TabText = 0x7f1100d5
com.example.myapplication:id/nav_graph = 0x7f08013c
com.example.myapplication:dimen/m3_sys_typescale_body_small_letter_spacing = 0x7f060120
com.example.myapplication:dimen/m3_sys_typescale_body_medium_letter_spacing = 0x7f06011e
com.example.myapplication:dimen/m3_sys_shape_small_corner_size = 0x7f060117
com.example.myapplication:dimen/m3_sys_shape_medium_corner_size = 0x7f060116
com.example.myapplication:dimen/m3_sys_shape_large_corner_size = 0x7f060115
com.example.myapplication:color/m3_ref_palette_error0 = 0x7f0500cf
com.example.myapplication:dimen/m3_sys_elevation_level1 = 0x7f060110
com.example.myapplication:style/Base.Widget.AppCompat.Spinner.Underlined = 0x7f1100ec
com.example.myapplication:dimen/m3_sys_elevation_level0 = 0x7f06010f
com.example.myapplication:id/bestChoice = 0x7f080061
com.example.myapplication:style/Widget.Material3.Chip.Input.Elevated = 0x7f11031f
com.example.myapplication:dimen/m3_slider_thumb_elevation = 0x7f06010c
com.example.myapplication:dimen/m3_navigation_item_icon_padding = 0x7f0600f8
com.example.myapplication:style/Widget.MaterialComponents.Tooltip = 0x7f1103fc
com.example.myapplication:dimen/m3_ripple_focused_alpha = 0x7f060108
com.example.myapplication:dimen/m3_navigation_rail_item_active_indicator_height = 0x7f060101
com.example.myapplication:layout/fragment_second = 0x7f0b002f
com.example.myapplication:drawable/notification_tile_bg = 0x7f070097
com.example.myapplication:dimen/m3_navigation_menu_headline_horizontal_padding = 0x7f0600ff
com.example.myapplication:bool/m3_sys_typescale_title_medium_text_all_caps = 0x7f04000f
com.example.myapplication:dimen/disabled_alpha_material_dark = 0x7f060090
com.example.myapplication:style/Theme.MaterialComponents.Light.Bridge = 0x7f11022c
com.example.myapplication:attr/indicatorDirectionCircular = 0x7f030201
com.example.myapplication:color/design_dark_default_color_on_background = 0x7f050033
com.example.myapplication:color/m3_ref_palette_error30 = 0x7f0500d3
com.example.myapplication:color/m3_button_ripple_color = 0x7f050065
com.example.myapplication:layout/test_design_radiobutton = 0x7f0b006d
com.example.myapplication:dimen/m3_navigation_item_vertical_padding = 0x7f0600fd
com.example.myapplication:dimen/m3_navigation_item_shape_inset_start = 0x7f0600fb
com.example.myapplication:anim/abc_slide_out_bottom = 0x7f010008
com.example.myapplication:dimen/mtrl_calendar_selection_baseline_to_top_fullscreen = 0x7f0601aa
com.example.myapplication:dimen/m3_navigation_item_shape_inset_bottom = 0x7f0600f9
com.example.myapplication:drawable/mtrl_popupmenu_background = 0x7f070089
com.example.myapplication:attr/flow_horizontalBias = 0x7f0301b7
com.example.myapplication:color/m3_sys_color_dynamic_light_on_tertiary_container = 0x7f050161
com.example.myapplication:dimen/m3_menu_elevation = 0x7f0600f5
com.example.myapplication:style/ThemeOverlay.MaterialComponents.Dark = 0x7f110286
com.example.myapplication:dimen/m3_large_fab_size = 0x7f0600f4
com.example.myapplication:dimen/m3_fab_translation_z_hovered_focused = 0x7f0600f1
com.example.myapplication:attr/contrast = 0x7f03011d
com.example.myapplication:dimen/m3_fab_corner_size = 0x7f0600f0
com.example.myapplication:dimen/m3_extended_fab_top_padding = 0x7f0600ee
com.example.myapplication:dimen/m3_extended_fab_min_height = 0x7f0600ec
com.example.myapplication:style/ShapeAppearance.Material3.Tooltip = 0x7f11014b
com.example.myapplication:dimen/material_filled_edittext_font_2_0_padding_bottom = 0x7f060151
com.example.myapplication:attr/actionOverflowMenuStyle = 0x7f030023
com.example.myapplication:dimen/m3_extended_fab_icon_padding = 0x7f0600eb
com.example.myapplication:dimen/m3_chip_icon_size = 0x7f0600e6
com.example.myapplication:integer/abc_config_activityShortDur = 0x7f090001
com.example.myapplication:attr/layout_constraintRight_toLeftOf = 0x7f030257
com.example.myapplication:dimen/m3_chip_hovered_translation_z = 0x7f0600e5
com.example.myapplication:layout/m3_alert_dialog_actions = 0x7f0b0033
com.example.myapplication:dimen/abc_dialog_padding_top_material = 0x7f060025
com.example.myapplication:attr/reactiveGuide_applyToAllConstraintSets = 0x7f03033e
com.example.myapplication:attr/tabUnboundedRipple = 0x7f0303c4
com.example.myapplication:layout/abc_action_mode_close_item_material = 0x7f0b0005
com.example.myapplication:color/m3_assist_chip_icon_tint_color = 0x7f050060
com.example.myapplication:color/m3_dynamic_default_color_primary_text = 0x7f05007d
com.example.myapplication:color/m3_ref_palette_primary0 = 0x7f0500f6
com.example.myapplication:dimen/m3_chip_dragged_translation_z = 0x7f0600e3
com.example.myapplication:id/action_bar_title = 0x7f08003d
com.example.myapplication:dimen/m3_card_hovered_z = 0x7f0600de
com.example.myapplication:dimen/m3_card_elevated_dragged_z = 0x7f0600da
com.example.myapplication:dimen/m3_card_dragged_z = 0x7f0600d9
com.example.myapplication:dimen/m3_btn_translation_z_hovered = 0x7f0600d8
com.example.myapplication:dimen/abc_text_size_menu_material = 0x7f06004b
com.example.myapplication:dimen/m3_btn_text_btn_padding_right = 0x7f0600d6
com.example.myapplication:dimen/m3_btn_padding_top = 0x7f0600d1
com.example.myapplication:dimen/mtrl_textinput_start_icon_margin_end = 0x7f06021c
com.example.myapplication:dimen/m3_btn_padding_left = 0x7f0600cf
com.example.myapplication:color/material_dynamic_neutral_variant0 = 0x7f0501ae
com.example.myapplication:style/ThemeOverlay.MaterialComponents.AutoCompleteTextView.OutlinedBox.Dense = 0x7f110282
com.example.myapplication:id/view_status_indicator = 0x7f0801f9
com.example.myapplication:dimen/m3_btn_padding_bottom = 0x7f0600ce
com.example.myapplication:dimen/design_bottom_navigation_elevation = 0x7f060064
com.example.myapplication:style/Theme.Material3.DayNight.BottomSheetDialog = 0x7f1101fc
com.example.myapplication:dimen/m3_btn_inset = 0x7f0600cc
com.example.myapplication:style/Widget.Material3.MaterialCalendar.MonthTextView = 0x7f110353
com.example.myapplication:id/action_mode_bar_stub = 0x7f080045
com.example.myapplication:dimen/m3_btn_icon_only_min_width = 0x7f0600cb
com.example.myapplication:attr/flow_horizontalStyle = 0x7f0301b9
com.example.myapplication:dimen/m3_btn_icon_only_icon_padding = 0x7f0600ca
com.example.myapplication:dimen/m3_btn_elevated_btn_elevation = 0x7f0600c4
com.example.myapplication:dimen/m3_btn_dialog_btn_spacing = 0x7f0600c1
com.example.myapplication:drawable/mtrl_ic_cancel = 0x7f070086
com.example.myapplication:id/month_navigation_fragment_toggle = 0x7f08011d
com.example.myapplication:id/dragLeft = 0x7f0800b3
com.example.myapplication:color/design_dark_default_color_primary_variant = 0x7f05003a
com.example.myapplication:color/m3_ref_palette_tertiary60 = 0x7f050117
com.example.myapplication:id/selected = 0x7f08018a
com.example.myapplication:dimen/m3_bottom_sheet_modal_elevation = 0x7f0600bc
com.example.myapplication:style/Animation.AppCompat.DropDownUp = 0x7f110004
com.example.myapplication:dimen/m3_bottom_sheet_elevation = 0x7f0600bb
com.example.myapplication:dimen/m3_bottom_nav_item_padding_top = 0x7f0600b9
com.example.myapplication:dimen/m3_bottom_nav_item_active_indicator_width = 0x7f0600b7
com.example.myapplication:style/Widget.Material3.Chip.Suggestion = 0x7f110322
com.example.myapplication:dimen/m3_bottom_nav_item_active_indicator_height = 0x7f0600b5
com.example.myapplication:attr/coordinatorLayoutStyle = 0x7f03011f
com.example.myapplication:attr/verticalOffsetWithText = 0x7f030452
com.example.myapplication:dimen/m3_badge_vertical_offset = 0x7f0600b1
com.example.myapplication:dimen/m3_badge_radius = 0x7f0600b0
com.example.myapplication:styleable/MotionTelltales = 0x7f12006b
com.example.myapplication:color/mtrl_filled_icon_tint = 0x7f050216
com.example.myapplication:dimen/m3_appbar_size_medium = 0x7f0600ae
com.example.myapplication:dimen/m3_appbar_scrim_height_trigger = 0x7f0600a9
com.example.myapplication:attr/tabIndicator = 0x7f0303ae
com.example.myapplication:id/accessibility_custom_action_28 = 0x7f080027
com.example.myapplication:dimen/compat_button_padding_horizontal_material = 0x7f060059
com.example.myapplication:dimen/m3_appbar_expanded_title_margin_bottom = 0x7f0600a7
com.example.myapplication:color/mtrl_navigation_item_background_color = 0x7f05021d
com.example.myapplication:style/TextAppearance.AppCompat.Widget.ActionBar.Subtitle = 0x7f110190
com.example.myapplication:color/m3_slider_halo_color = 0x7f050120
com.example.myapplication:id/accessibility_custom_action_9 = 0x7f080031
com.example.myapplication:dimen/m3_alert_dialog_action_bottom_padding = 0x7f0600a0
com.example.myapplication:dimen/item_touch_helper_swipe_escape_velocity = 0x7f06009f
com.example.myapplication:styleable/TabItem = 0x7f120093
com.example.myapplication:id/view_transition = 0x7f0801fa
com.example.myapplication:dimen/hint_pressed_alpha_material_light = 0x7f06009c
com.example.myapplication:dimen/hint_alpha_material_light = 0x7f06009a
com.example.myapplication:style/Widget.Material3.MaterialCalendar.Item = 0x7f110351
com.example.myapplication:dimen/m3_navigation_drawer_layout_corner_size = 0x7f0600f6
com.example.myapplication:dimen/highlight_alpha_material_light = 0x7f060098
com.example.myapplication:dimen/highlight_alpha_material_dark = 0x7f060097
com.example.myapplication:color/m3_ref_palette_primary80 = 0x7f0500ff
com.example.myapplication:dimen/fastscroll_margin = 0x7f060094
com.example.myapplication:string/nav_tasks = 0x7f10008d
com.example.myapplication:dimen/fastscroll_default_thickness = 0x7f060093
com.example.myapplication:id/wrap_content = 0x7f080204
com.example.myapplication:dimen/mtrl_btn_z = 0x7f06018b
com.example.myapplication:attr/helperTextEnabled = 0x7f0301de
com.example.myapplication:dimen/fab_margin = 0x7f060092
com.example.myapplication:attr/motionPathRotate = 0x7f0302e5
com.example.myapplication:dimen/m3_sys_state_dragged_state_layer_opacity = 0x7f060118
com.example.myapplication:color/m3_timepicker_display_background_color = 0x7f050193
com.example.myapplication:style/TextAppearance.AppCompat.Headline = 0x7f11017b
com.example.myapplication:dimen/design_tab_text_size_2line = 0x7f06008e
com.example.myapplication:string/abc_menu_shift_shortcut_label = 0x7f10000e
com.example.myapplication:id/easeIn = 0x7f0800b8
com.example.myapplication:dimen/design_tab_max_width = 0x7f06008b
com.example.myapplication:layout/mtrl_calendar_month_navigation = 0x7f0b004f
com.example.myapplication:dimen/design_snackbar_padding_horizontal = 0x7f060087
com.example.myapplication:dimen/design_snackbar_extra_spacing_horizontal = 0x7f060084
com.example.myapplication:layout/mtrl_picker_header_title_text = 0x7f0b005c
com.example.myapplication:color/mtrl_card_view_foreground = 0x7f050208
com.example.myapplication:id/listMode = 0x7f0800fd
com.example.myapplication:dimen/design_snackbar_background_corner_radius = 0x7f060082
com.example.myapplication:color/background_floating_material_dark = 0x7f05001d
com.example.myapplication:attr/thumbTint = 0x7f030414
com.example.myapplication:attr/attributeName = 0x7f03003c
com.example.myapplication:dimen/design_snackbar_action_text_color_alpha = 0x7f060081
com.example.myapplication:attr/labelBehavior = 0x7f03022b
com.example.myapplication:dimen/design_snackbar_action_inline_max_width = 0x7f060080
com.example.myapplication:id/design_menu_item_text = 0x7f0800a5
com.example.myapplication:attr/checkMarkCompat = 0x7f03009b
com.example.myapplication:dimen/design_navigation_padding_bottom = 0x7f06007e
com.example.myapplication:layout/abc_tooltip = 0x7f0b001b
com.example.myapplication:color/m3_ref_palette_neutral_variant99 = 0x7f0500f5
com.example.myapplication:id/nav_profile = 0x7f08013f
com.example.myapplication:string/fab_transformation_scrim_behavior = 0x7f100030
com.example.myapplication:dimen/design_navigation_item_icon_padding = 0x7f06007b
com.example.myapplication:color/m3_sys_color_light_on_secondary = 0x7f050176
com.example.myapplication:dimen/design_navigation_item_horizontal_padding = 0x7f06007a
com.example.myapplication:dimen/design_navigation_icon_size = 0x7f060079
com.example.myapplication:layout/abc_screen_content_include = 0x7f0b0014
com.example.myapplication:dimen/notification_top_pad_large_text = 0x7f060232
com.example.myapplication:dimen/design_navigation_icon_padding = 0x7f060078
com.example.myapplication:style/ThemeOverlay.Material3.DynamicColors.Dark = 0x7f110261
com.example.myapplication:style/Widget.Material3.Button.ElevatedButton = 0x7f110307
com.example.myapplication:dimen/design_fab_size_normal = 0x7f060074
com.example.myapplication:style/Widget.MaterialComponents.CardView = 0x7f1103a1
com.example.myapplication:dimen/mtrl_card_dragged_z = 0x7f0601b9
com.example.myapplication:dimen/material_emphasis_high_type = 0x7f06014d
com.example.myapplication:dimen/design_fab_size_mini = 0x7f060073
com.example.myapplication:integer/m3_card_anim_duration_ms = 0x7f09000d
com.example.myapplication:dimen/design_fab_image_size = 0x7f060072
com.example.myapplication:color/secondary_text_disabled_material_light = 0x7f050244
com.example.myapplication:style/TextAppearance.AppCompat.Widget.Button.Inverse = 0x7f11019b
com.example.myapplication:attr/layout_goneMarginBaseline = 0x7f03026a
com.example.myapplication:style/Widget.MaterialComponents.Toolbar = 0x7f1103f8
com.example.myapplication:dimen/design_fab_border_width = 0x7f060070
com.example.myapplication:dimen/test_navigation_bar_height = 0x7f06023a
com.example.myapplication:dimen/design_bottom_sheet_peek_height_min = 0x7f06006f
com.example.myapplication:id/easeOut = 0x7f0800ba
com.example.myapplication:dimen/design_bottom_sheet_modal_elevation = 0x7f06006e
com.example.myapplication:string/abc_action_mode_done = 0x7f100003
com.example.myapplication:id/accessibility_custom_action_5 = 0x7f08002d
com.example.myapplication:dimen/design_bottom_sheet_elevation = 0x7f06006d
com.example.myapplication:style/RtlOverlay.Widget.AppCompat.Search.DropDown.Text = 0x7f110143
com.example.myapplication:animator/mtrl_extended_fab_show_motion_spec = 0x7f02001a
com.example.myapplication:dimen/design_bottom_navigation_shadow_height = 0x7f06006b
com.example.myapplication:color/m3_sys_color_dark_inverse_primary = 0x7f050129
com.example.myapplication:id/motion_base = 0x7f080121
com.example.myapplication:string/m3_ref_typeface_brand_regular = 0x7f10003a
com.example.myapplication:dimen/design_bottom_navigation_label_padding = 0x7f060069
com.example.myapplication:attr/panelMenuListWidth = 0x7f03030d
com.example.myapplication:dimen/mtrl_calendar_day_horizontal_padding = 0x7f060193
com.example.myapplication:dimen/design_bottom_navigation_item_min_width = 0x7f060068
com.example.myapplication:style/Widget.AppCompat.ListView = 0x7f1102d5
com.example.myapplication:dimen/appcompat_dialog_background_inset = 0x7f060052
com.example.myapplication:attr/clearsTag = 0x7f0300c4
com.example.myapplication:dimen/design_bottom_navigation_icon_size = 0x7f060066
com.example.myapplication:dimen/design_bottom_navigation_active_item_min_width = 0x7f060062
com.example.myapplication:color/material_dynamic_neutral_variant50 = 0x7f0501b4
com.example.myapplication:menu/menu_main = 0x7f0c0002
com.example.myapplication:color/m3_ref_palette_dynamic_primary100 = 0x7f0500aa
com.example.myapplication:dimen/abc_control_inset_material = 0x7f060019
com.example.myapplication:dimen/design_appbar_elevation = 0x7f060060
com.example.myapplication:styleable/BaseProgressIndicator = 0x7f120017
com.example.myapplication:style/Widget.MaterialComponents.ExtendedFloatingActionButton.Icon = 0x7f1103b1
com.example.myapplication:dimen/clock_face_margin_start = 0x7f060056
com.example.myapplication:attr/cardMaxElevation = 0x7f03008c
com.example.myapplication:dimen/cardview_default_elevation = 0x7f060054
com.example.myapplication:dimen/action_bar_size = 0x7f060051
com.example.myapplication:style/Base.Widget.AppCompat.ActionBar.Solid = 0x7f1100b8
com.example.myapplication:anim/abc_popup_enter = 0x7f010003
com.example.myapplication:dimen/abc_text_size_title_material_toolbar = 0x7f060050
com.example.myapplication:dimen/abc_text_size_menu_header_material = 0x7f06004a
com.example.myapplication:style/Widget.MaterialComponents.NavigationRailView.Colored = 0x7f1103cf
com.example.myapplication:dimen/abc_text_size_headline_material = 0x7f060047
com.example.myapplication:id/icon_group = 0x7f0800e3
com.example.myapplication:color/mtrl_textinput_disabled_color = 0x7f05022d
com.example.myapplication:dimen/abc_text_size_display_3_material = 0x7f060045
com.example.myapplication:animator/mtrl_chip_state_list_anim = 0x7f020016
com.example.myapplication:dimen/abc_text_size_display_1_material = 0x7f060043
com.example.myapplication:dimen/abc_text_size_caption_material = 0x7f060042
com.example.myapplication:dimen/abc_text_size_button_material = 0x7f060041
com.example.myapplication:string/password_toggle_content_description = 0x7f10008f
com.example.myapplication:dimen/mtrl_bottomappbar_fab_cradle_margin = 0x7f060170
com.example.myapplication:dimen/abc_switch_padding = 0x7f06003e
com.example.myapplication:style/Widget.Material3.PopupMenu.Overflow = 0x7f110369
com.example.myapplication:attr/toolbarStyle = 0x7f03042f
com.example.myapplication:id/percent = 0x7f080163
com.example.myapplication:id/dragEnd = 0x7f0800b2
com.example.myapplication:string/abc_menu_enter_shortcut_label = 0x7f10000b
com.example.myapplication:dimen/hint_pressed_alpha_material_dark = 0x7f06009b
com.example.myapplication:attr/trackTintMode = 0x7f030441
com.example.myapplication:dimen/abc_star_big = 0x7f06003b
com.example.myapplication:string/add_category = 0x7f10001c
com.example.myapplication:dimen/m3_sys_typescale_title_medium_text_size = 0x7f060137
com.example.myapplication:dimen/abc_select_dialog_padding_start_material = 0x7f06003a
com.example.myapplication:attr/insetForeground = 0x7f030206
com.example.myapplication:styleable/MotionLabel = 0x7f120068
com.example.myapplication:drawable/notification_bg_normal_pressed = 0x7f070093
com.example.myapplication:id/touch_outside = 0x7f0801df
com.example.myapplication:dimen/abc_seekbar_track_background_height_material = 0x7f060038
com.example.myapplication:attr/buttonIconDimen = 0x7f030082
com.example.myapplication:dimen/m3_alert_dialog_action_top_padding = 0x7f0600a1
com.example.myapplication:dimen/abc_list_item_height_small_material = 0x7f060032
com.example.myapplication:style/TextAppearance.AppCompat.Widget.DropDownItem = 0x7f11019c
com.example.myapplication:dimen/abc_edit_text_inset_bottom_material = 0x7f06002c
com.example.myapplication:dimen/abc_disabled_alpha_material_light = 0x7f060028
com.example.myapplication:style/TextAppearance.Design.Counter.Overflow = 0x7f1101a9
com.example.myapplication:dimen/abc_dialog_padding_material = 0x7f060024
com.example.myapplication:color/m3_text_button_background_color_selector = 0x7f050187
com.example.myapplication:drawable/mtrl_ic_error = 0x7f070087
com.example.myapplication:style/TextAppearance.Compat.Notification = 0x7f1101a2
com.example.myapplication:id/tag_accessibility_heading = 0x7f0801b7
com.example.myapplication:dimen/abc_dialog_fixed_width_minor = 0x7f06001f
com.example.myapplication:dimen/mtrl_badge_with_text_radius = 0x7f06016d
com.example.myapplication:style/Widget.AppCompat.ActionBar = 0x7f1102a3
com.example.myapplication:style/Theme.Material3.Dark.Dialog = 0x7f1101f6
com.example.myapplication:attr/appBarLayoutStyle = 0x7f030036
com.example.myapplication:dimen/abc_dialog_fixed_width_major = 0x7f06001e
com.example.myapplication:drawable/abc_ic_menu_copy_mtrl_am_alpha = 0x7f070021
com.example.myapplication:id/accessibility_custom_action_26 = 0x7f080025
com.example.myapplication:attr/subheaderTextAppearance = 0x7f03039a
com.example.myapplication:style/Widget.Material3.PopupMenu.ContextMenu = 0x7f110367
com.example.myapplication:dimen/mtrl_navigation_item_icon_size = 0x7f0601e6
com.example.myapplication:dimen/mtrl_calendar_navigation_bottom_padding = 0x7f0601a6
com.example.myapplication:dimen/abc_dialog_fixed_height_minor = 0x7f06001d
com.example.myapplication:attr/onShow = 0x7f0302fe
com.example.myapplication:id/action_bar_activity_content = 0x7f080038
com.example.myapplication:style/TextAppearance.MaterialComponents.Headline3 = 0x7f1101cc
com.example.myapplication:dimen/abc_dialog_fixed_height_major = 0x7f06001c
com.example.myapplication:anim/abc_fade_out = 0x7f010001
com.example.myapplication:dimen/mtrl_navigation_rail_icon_size = 0x7f0601ee
com.example.myapplication:dimen/abc_control_padding_material = 0x7f06001a
com.example.myapplication:id/fill = 0x7f0800c8
com.example.myapplication:style/Theme.Material3.DynamicColors.Dark = 0x7f110202
com.example.myapplication:color/material_timepicker_modebutton_tint = 0x7f0501fd
com.example.myapplication:attr/colorOnSecondary = 0x7f0300ee
com.example.myapplication:dimen/abc_button_padding_vertical_material = 0x7f060015
com.example.myapplication:dimen/abc_action_button_min_height_material = 0x7f06000d
com.example.myapplication:dimen/abc_action_bar_subtitle_top_margin_material = 0x7f06000c
com.example.myapplication:dimen/abc_action_bar_overflow_padding_start_material = 0x7f060008
com.example.myapplication:dimen/abc_action_bar_overflow_padding_end_material = 0x7f060007
com.example.myapplication:style/Widget.MaterialComponents.TimePicker.Button = 0x7f1103ef
com.example.myapplication:dimen/mtrl_btn_dialog_btn_min_width = 0x7f060175
com.example.myapplication:dimen/test_navigation_bar_item_max_width = 0x7f06023c
com.example.myapplication:dimen/abc_action_bar_icon_vertical_padding_material = 0x7f060006
com.example.myapplication:id/showCustom = 0x7f08018f
com.example.myapplication:dimen/mtrl_calendar_day_today_stroke = 0x7f060194
com.example.myapplication:color/mtrl_calendar_item_stroke_color = 0x7f050206
com.example.myapplication:style/Base.Widget.Material3.CompoundButton.RadioButton = 0x7f1100f8
com.example.myapplication:dimen/abc_action_bar_default_padding_end_material = 0x7f060003
com.example.myapplication:attr/buttonBarNegativeButtonStyle = 0x7f03007c
com.example.myapplication:color/tooltip_background_light = 0x7f050251
com.example.myapplication:color/switch_thumb_normal_material_light = 0x7f05024a
com.example.myapplication:color/switch_thumb_material_light = 0x7f050248
com.example.myapplication:attr/buttonStyle = 0x7f030084
com.example.myapplication:dimen/design_tab_text_size = 0x7f06008d
com.example.myapplication:attr/errorIconDrawable = 0x7f030181
com.example.myapplication:dimen/mtrl_navigation_item_shape_vertical_margin = 0x7f0601e8
com.example.myapplication:color/secondary_text_default_material_dark = 0x7f050241
com.example.myapplication:style/Widget.Material3.ActionMode = 0x7f1102fa
com.example.myapplication:color/ripple_material_light = 0x7f050240
com.example.myapplication:id/constraint = 0x7f08008e
com.example.myapplication:color/purple_700 = 0x7f05023d
com.example.myapplication:animator/linear_indeterminate_line2_tail_interpolator = 0x7f02000c
com.example.myapplication:color/primary_text_default_material_dark = 0x7f050237
com.example.myapplication:style/RtlOverlay.Widget.AppCompat.ActionBar.TitleItem = 0x7f110137
com.example.myapplication:attr/indeterminateProgressStyle = 0x7f0301ff
com.example.myapplication:color/primary_material_dark = 0x7f050235
com.example.myapplication:color/notification_icon_bg_color = 0x7f050232
com.example.myapplication:color/mtrl_textinput_filled_box_default_background_color = 0x7f05022e
com.example.myapplication:color/mtrl_tabs_ripple_color = 0x7f05022a
com.example.myapplication:attr/topInsetScrimEnabled = 0x7f030435
com.example.myapplication:attr/wavePeriod = 0x7f03045d
com.example.myapplication:id/tag_on_receive_content_mime_types = 0x7f0801bb
com.example.myapplication:color/mtrl_scrim_color = 0x7f050225
com.example.myapplication:dimen/m3_bottom_nav_item_active_indicator_margin_horizontal = 0x7f0600b6
com.example.myapplication:color/mtrl_outlined_icon_tint = 0x7f050222
com.example.myapplication:string/m3_sys_motion_easing_standard = 0x7f100041
com.example.myapplication:dimen/abc_action_bar_content_inset_with_nav = 0x7f060001
com.example.myapplication:color/abc_background_cache_hint_selector_material_dark = 0x7f050000
com.example.myapplication:attr/tickColorInactive = 0x7f030418
com.example.myapplication:drawable/abc_item_background_holo_light = 0x7f07002a
com.example.myapplication:color/mtrl_on_surface_ripple_color = 0x7f050221
com.example.myapplication:styleable/CompoundButton = 0x7f120028
com.example.myapplication:attr/actionModeShareDrawable = 0x7f03001d
com.example.myapplication:string/hello_first_fragment = 0x7f100033
com.example.myapplication:drawable/abc_ic_menu_paste_mtrl_am_alpha = 0x7f070024
com.example.myapplication:dimen/mtrl_badge_text_horizontal_edge_offset = 0x7f060169
com.example.myapplication:color/mtrl_on_primary_text_btn_text_color_selector = 0x7f050220
com.example.myapplication:color/m3_ref_palette_dynamic_primary80 = 0x7f0500b1
com.example.myapplication:color/mtrl_navigation_item_text_color = 0x7f05021f
com.example.myapplication:string/abc_menu_space_shortcut_label = 0x7f10000f
com.example.myapplication:color/mtrl_navigation_bar_item_tint = 0x7f05021b
com.example.myapplication:attr/elevationOverlayAccentColor = 0x7f030170
com.example.myapplication:dimen/mtrl_btn_padding_right = 0x7f060182
com.example.myapplication:color/mtrl_navigation_bar_colored_ripple_color = 0x7f05021a
com.example.myapplication:color/mtrl_indicator_text_color = 0x7f050218
com.example.myapplication:attr/behavior_hideable = 0x7f030060
com.example.myapplication:id/accessibility_custom_action_12 = 0x7f080016
com.example.myapplication:color/mtrl_tabs_icon_color_selector_colored = 0x7f050228
com.example.myapplication:color/mtrl_filled_background_color = 0x7f050215
com.example.myapplication:color/mtrl_error = 0x7f050211
com.example.myapplication:attr/iconTint = 0x7f0301f4
com.example.myapplication:id/action_bar_root = 0x7f08003a
com.example.myapplication:color/mtrl_choice_chip_text_color = 0x7f050210
com.example.myapplication:dimen/material_text_view_test_line_height = 0x7f060159
com.example.myapplication:attr/fontStyle = 0x7f0301ce
com.example.myapplication:color/mtrl_chip_surface_color = 0x7f05020c
com.example.myapplication:color/material_dynamic_primary10 = 0x7f0501bc
com.example.myapplication:color/mtrl_chip_close_icon_tint = 0x7f05020b
com.example.myapplication:style/Theme.MaterialComponents.Dialog = 0x7f110220
com.example.myapplication:id/navigation_bar_item_labels_group = 0x7f080144
com.example.myapplication:drawable/notification_bg_low_pressed = 0x7f070091
com.example.myapplication:attr/constraintSet = 0x7f030107
com.example.myapplication:color/mtrl_card_view_ripple = 0x7f050209
com.example.myapplication:color/mtrl_btn_text_color_selector = 0x7f050204
com.example.myapplication:color/mtrl_btn_stroke_color_selector = 0x7f050200
com.example.myapplication:color/mtrl_btn_ripple_color = 0x7f0501ff
com.example.myapplication:attr/singleLine = 0x7f03036d
com.example.myapplication:id/rightToLeft = 0x7f08016f
com.example.myapplication:color/mtrl_btn_bg_color_selector = 0x7f0501fe
com.example.myapplication:style/Base.V14.Theme.MaterialComponents = 0x7f110089
com.example.myapplication:dimen/m3_appbar_scrim_height_trigger_large = 0x7f0600aa
com.example.myapplication:attr/defaultDuration = 0x7f030144
com.example.myapplication:dimen/hint_alpha_material_dark = 0x7f060099
com.example.myapplication:color/material_timepicker_clockface = 0x7f0501fc
com.example.myapplication:style/TextAppearance.AppCompat.Subhead = 0x7f11018a
com.example.myapplication:drawable/notification_template_icon_low_bg = 0x7f070096
com.example.myapplication:attr/buttonPanelSideLayout = 0x7f030083
com.example.myapplication:color/material_timepicker_clock_text_color = 0x7f0501fb
com.example.myapplication:color/material_dynamic_secondary50 = 0x7f0501ce
com.example.myapplication:color/material_timepicker_button_stroke = 0x7f0501fa
com.example.myapplication:color/material_slider_inactive_track_color = 0x7f0501f7
com.example.myapplication:color/material_slider_halo_color = 0x7f0501f5
com.example.myapplication:dimen/m3_navigation_rail_item_padding_bottom = 0x7f060105
com.example.myapplication:string/complete_task = 0x7f10002b
com.example.myapplication:color/m3_sys_color_dynamic_light_secondary_container = 0x7f050166
com.example.myapplication:color/material_slider_active_tick_marks_color = 0x7f0501f3
com.example.myapplication:color/material_on_surface_emphasis_high_type = 0x7f0501f0
com.example.myapplication:color/material_on_primary_emphasis_high_type = 0x7f0501ed
com.example.myapplication:color/material_on_primary_disabled = 0x7f0501ec
com.example.myapplication:layout/text_view_without_line_height = 0x7f0b0078
com.example.myapplication:color/material_grey_900 = 0x7f0501e8
com.example.myapplication:style/Theme.MyApplication.NoActionBar = 0x7f11023f
com.example.myapplication:style/Widget.AppCompat.ButtonBar.AlertDialog = 0x7f1102b5
com.example.myapplication:dimen/m3_sys_typescale_body_large_letter_spacing = 0x7f06011c
com.example.myapplication:color/material_grey_850 = 0x7f0501e7
com.example.myapplication:style/Widget.Material3.Chip.Input.Icon = 0x7f110320
com.example.myapplication:attr/layout_constraintWidth_min = 0x7f030265
com.example.myapplication:attr/fabSize = 0x7f03019f
com.example.myapplication:attr/startIconTintMode = 0x7f03038a
com.example.myapplication:color/material_grey_800 = 0x7f0501e6
com.example.myapplication:id/zero_corner_chip = 0x7f080208
com.example.myapplication:drawable/ic_m3_chip_close = 0x7f07006b
com.example.myapplication:color/material_grey_50 = 0x7f0501e4
com.example.myapplication:color/material_grey_100 = 0x7f0501e2
com.example.myapplication:color/material_dynamic_tertiary95 = 0x7f0501e0
com.example.myapplication:color/material_dynamic_tertiary90 = 0x7f0501df
com.example.myapplication:id/disableIntraAutoTransition = 0x7f0800ab
com.example.myapplication:color/material_dynamic_tertiary70 = 0x7f0501dd
com.example.myapplication:style/Widget.Material3.ExtendedFloatingActionButton.Icon.Primary = 0x7f110330
com.example.myapplication:id/item_touch_helper_previous_elevation = 0x7f0800f2
com.example.myapplication:style/Platform.ThemeOverlay.AppCompat = 0x7f11012e
com.example.myapplication:color/material_dynamic_tertiary30 = 0x7f0501d9
com.example.myapplication:color/m3_ref_palette_error90 = 0x7f0500d9
com.example.myapplication:styleable/LinearLayoutCompat_Layout = 0x7f12004e
com.example.myapplication:attr/minHeight = 0x7f0302c4
com.example.myapplication:drawable/notification_icon_background = 0x7f070094
com.example.myapplication:color/m3_sys_color_dynamic_light_on_surface = 0x7f05015e
com.example.myapplication:color/m3_navigation_item_icon_tint = 0x7f050089
com.example.myapplication:style/TextAppearance.AppCompat.Widget.ActionMode.Title = 0x7f110196
com.example.myapplication:attr/waveDecay = 0x7f03045b
com.example.myapplication:style/Widget.Material3.CompoundButton.RadioButton = 0x7f11032d
com.example.myapplication:color/material_dynamic_tertiary20 = 0x7f0501d8
com.example.myapplication:color/m3_calendar_item_disabled_text = 0x7f050067
com.example.myapplication:dimen/abc_action_bar_default_height_material = 0x7f060002
com.example.myapplication:color/material_dynamic_tertiary100 = 0x7f0501d7
com.example.myapplication:color/material_dynamic_tertiary0 = 0x7f0501d5
com.example.myapplication:style/Widget.AppCompat.Light.ActionBar.TabView = 0x7f1102c4
com.example.myapplication:style/TextAppearance.Widget.AppCompat.Toolbar.Subtitle = 0x7f1101d6
com.example.myapplication:color/mtrl_outlined_stroke_color = 0x7f050223
com.example.myapplication:attr/chipMinHeight = 0x7f0300b1
com.example.myapplication:drawable/abc_seekbar_thumb_material = 0x7f070041
com.example.myapplication:color/material_dynamic_secondary70 = 0x7f0501d0
com.example.myapplication:attr/layout_constraintEnd_toStartOf = 0x7f030247
com.example.myapplication:drawable/abc_textfield_search_activated_mtrl_alpha = 0x7f070052
com.example.myapplication:color/material_dynamic_secondary60 = 0x7f0501cf
com.example.myapplication:attr/textOutlineThickness = 0x7f030403
com.example.myapplication:color/material_dynamic_secondary40 = 0x7f0501cd
com.example.myapplication:attr/badgeGravity = 0x7f030050
com.example.myapplication:animator/mtrl_btn_unelevated_state_list_anim = 0x7f020014
com.example.myapplication:style/Widget.Material3.Chip.Filter.Elevated = 0x7f11031d
com.example.myapplication:color/material_dynamic_secondary99 = 0x7f0501d4
com.example.myapplication:style/ThemeOverlay.MaterialComponents.BottomAppBar.Primary = 0x7f110283
com.example.myapplication:color/material_dynamic_secondary30 = 0x7f0501cc
com.example.myapplication:string/material_motion_easing_linear = 0x7f10005a
com.example.myapplication:color/material_dynamic_secondary20 = 0x7f0501cb
com.example.myapplication:id/design_menu_item_action_area_stub = 0x7f0800a4
com.example.myapplication:color/material_dynamic_secondary100 = 0x7f0501ca
com.example.myapplication:color/material_dynamic_secondary10 = 0x7f0501c9
com.example.myapplication:color/material_dynamic_primary70 = 0x7f0501c3
com.example.myapplication:style/Widget.Material3.TextInputEditText.OutlinedBox = 0x7f110373
com.example.myapplication:color/material_dynamic_primary60 = 0x7f0501c2
com.example.myapplication:style/Widget.Material3.MaterialCalendar.DayTextView = 0x7f110348
com.example.myapplication:string/material_timepicker_text_input_mode_description = 0x7f100064
com.example.myapplication:dimen/m3_navigation_rail_item_active_indicator_margin_horizontal = 0x7f060102
com.example.myapplication:color/material_dynamic_primary20 = 0x7f0501be
com.example.myapplication:styleable/NavigationRailView = 0x7f120075
com.example.myapplication:id/material_timepicker_container = 0x7f080112
com.example.myapplication:color/material_dynamic_primary0 = 0x7f0501bb
com.example.myapplication:attr/layoutDuringTransition = 0x7f030232
com.example.myapplication:attr/textAppearanceLineHeightEnabled = 0x7f0303e2
com.example.myapplication:dimen/mtrl_calendar_year_corner = 0x7f0601b1
com.example.myapplication:attr/layout_editor_absoluteX = 0x7f030268
com.example.myapplication:color/material_dynamic_neutral_variant95 = 0x7f0501b9
com.example.myapplication:attr/windowMinWidthMajor = 0x7f030468
com.example.myapplication:color/material_dynamic_neutral_variant90 = 0x7f0501b8
com.example.myapplication:color/material_dynamic_neutral_variant80 = 0x7f0501b7
com.example.myapplication:color/material_dynamic_neutral_variant30 = 0x7f0501b2
com.example.myapplication:color/material_dynamic_neutral_variant20 = 0x7f0501b1
com.example.myapplication:color/material_dynamic_neutral_variant100 = 0x7f0501b0
com.example.myapplication:color/notification_action_color_filter = 0x7f050231
com.example.myapplication:color/material_dynamic_neutral40 = 0x7f0501a6
com.example.myapplication:attr/carousel_touchUp_dampeningFactor = 0x7f030098
com.example.myapplication:color/material_deep_teal_200 = 0x7f05019e
com.example.myapplication:color/material_blue_grey_950 = 0x7f05019c
com.example.myapplication:id/tag_window_insets_animation_callback = 0x7f0801c1
com.example.myapplication:dimen/mtrl_badge_toolbar_action_menu_item_horizontal_offset = 0x7f06016b
com.example.myapplication:color/m3_timepicker_secondary_text_button_text_color = 0x7f050198
com.example.myapplication:id/dropdown_menu = 0x7f0800b7
com.example.myapplication:color/m3_timepicker_secondary_text_button_ripple_color = 0x7f050197
com.example.myapplication:dimen/abc_dropdownitem_icon_width = 0x7f060029
com.example.myapplication:color/error_color_material_light = 0x7f05005a
com.example.myapplication:color/design_default_color_background = 0x7f05003e
com.example.myapplication:color/abc_primary_text_material_light = 0x7f05000c
com.example.myapplication:color/m3_timepicker_display_text_color = 0x7f050196
com.example.myapplication:attr/customBoolean = 0x7f030134
com.example.myapplication:layout/test_toolbar_surface = 0x7f0b0073
com.example.myapplication:color/design_icon_tint = 0x7f050053
com.example.myapplication:color/design_bottom_navigation_shadow_color = 0x7f05002f
com.example.myapplication:color/m3_timepicker_display_stroke_color = 0x7f050195
com.example.myapplication:string/next = 0x7f10008e
com.example.myapplication:drawable/abc_cab_background_top_mtrl_alpha = 0x7f070018
com.example.myapplication:attr/carousel_forwardTransition = 0x7f030093
com.example.myapplication:attr/contentPaddingBottom = 0x7f030116
com.example.myapplication:id/action_SecondFragment_to_FirstFragment = 0x7f080036
com.example.myapplication:color/m3_timepicker_button_ripple_color = 0x7f050190
com.example.myapplication:color/m3_text_button_ripple_color_selector = 0x7f050189
com.example.myapplication:dimen/abc_text_size_body_2_material = 0x7f060040
com.example.myapplication:style/Theme.Material3.DayNight.DialogWhenLarge = 0x7f110200
com.example.myapplication:color/m3_tabs_ripple_color = 0x7f050186
com.example.myapplication:attr/forceApplySystemWindowInsetTop = 0x7f0301d1
com.example.myapplication:color/m3_button_foreground_color_selector = 0x7f050063
com.example.myapplication:color/m3_sys_color_light_tertiary = 0x7f050183
com.example.myapplication:color/design_dark_default_color_primary = 0x7f050038
com.example.myapplication:styleable/TextInputLayout = 0x7f120098
com.example.myapplication:color/m3_sys_color_light_surface_variant = 0x7f050182
com.example.myapplication:styleable/ActionMenuItemView = 0x7f120002
com.example.myapplication:color/m3_sys_color_light_surface = 0x7f050181
com.example.myapplication:color/m3_ref_palette_dynamic_secondary40 = 0x7f0500ba
com.example.myapplication:color/m3_sys_color_dynamic_light_primary_container = 0x7f050164
com.example.myapplication:styleable/MaterialCheckBox = 0x7f120059
com.example.myapplication:color/m3_sys_color_light_secondary_container = 0x7f050180
com.example.myapplication:id/action_image = 0x7f080041
com.example.myapplication:color/m3_sys_color_light_outline = 0x7f05017c
com.example.myapplication:color/checkbox_themeable_attribute_color = 0x7f05002e
com.example.myapplication:style/Base.Widget.AppCompat.Toolbar = 0x7f1100ef
com.example.myapplication:color/m3_sys_color_light_on_tertiary_container = 0x7f05017b
com.example.myapplication:layout/material_clockface_textview = 0x7f0b003a
com.example.myapplication:attr/backgroundTint = 0x7f03004e
com.example.myapplication:attr/motionEffect_alpha = 0x7f0302db
com.example.myapplication:style/Widget.Material3.MaterialTimePicker.Display.Divider = 0x7f11035e
com.example.myapplication:color/m3_sys_color_light_on_tertiary = 0x7f05017a
com.example.myapplication:id/guideline = 0x7f0800db
com.example.myapplication:color/m3_sys_color_light_on_surface_variant = 0x7f050179
com.example.myapplication:attr/layout_constraintTop_toTopOf = 0x7f03025e
com.example.myapplication:dimen/mtrl_calendar_header_content_padding_fullscreen = 0x7f06019a
com.example.myapplication:style/Base.Widget.Material3.ActionMode = 0x7f1100f3
com.example.myapplication:style/ThemeOverlay.Material3.TextInputEditText.FilledBox.Dense = 0x7f110275
com.example.myapplication:color/m3_dynamic_highlighted_text = 0x7f05007f
com.example.myapplication:color/foreground_material_dark = 0x7f05005b
com.example.myapplication:color/m3_ref_palette_dynamic_primary99 = 0x7f0500b4
com.example.myapplication:color/m3_sys_color_light_on_secondary_container = 0x7f050177
com.example.myapplication:attr/verticalOffset = 0x7f030451
com.example.myapplication:id/vertical_only = 0x7f0801f6
com.example.myapplication:color/m3_sys_color_light_on_error = 0x7f050172
com.example.myapplication:style/Base.TextAppearance.Widget.AppCompat.Toolbar.Title = 0x7f11004b
com.example.myapplication:attr/colorControlNormal = 0x7f0300e4
com.example.myapplication:color/m3_sys_color_light_error_container = 0x7f05016d
com.example.myapplication:color/m3_sys_color_dynamic_light_secondary = 0x7f050165
com.example.myapplication:drawable/abc_vector_test = 0x7f070055
com.example.myapplication:dimen/tooltip_precise_anchor_extra_offset = 0x7f060244
com.example.myapplication:attr/textBackgroundRotate = 0x7f0303f3
com.example.myapplication:color/m3_dynamic_hint_foreground = 0x7f050080
com.example.myapplication:id/tv_month_year = 0x7f080214
com.example.myapplication:color/mtrl_tabs_icon_color_selector = 0x7f050227
com.example.myapplication:color/mtrl_fab_icon_text_color_selector = 0x7f050213
com.example.myapplication:attr/titleMarginTop = 0x7f030427
com.example.myapplication:attr/layout_constraintVertical_weight = 0x7f030261
com.example.myapplication:dimen/abc_disabled_alpha_material_dark = 0x7f060027
com.example.myapplication:attr/circularflow_viewCenter = 0x7f0300c2
com.example.myapplication:attr/textAppearanceTitleSmall = 0x7f0303ef
com.example.myapplication:color/m3_sys_color_dynamic_light_primary = 0x7f050163
com.example.myapplication:attr/showAnimationBehavior = 0x7f030363
com.example.myapplication:dimen/mtrl_calendar_action_padding = 0x7f06018e
com.example.myapplication:attr/quantizeMotionInterpolator = 0x7f030332
com.example.myapplication:attr/state_collapsible = 0x7f03038d
com.example.myapplication:color/m3_sys_color_dynamic_light_on_secondary_container = 0x7f05015d
com.example.myapplication:color/m3_sys_color_dynamic_light_inverse_surface = 0x7f050158
com.example.myapplication:attr/tabPaddingEnd = 0x7f0303bb
com.example.myapplication:id/view_detail_status = 0x7f0801f7
com.example.myapplication:dimen/design_snackbar_max_width = 0x7f060085
com.example.myapplication:attr/scrimAnimationDuration = 0x7f03034f
com.example.myapplication:color/m3_sys_color_dynamic_light_background = 0x7f050155
com.example.myapplication:attr/layout_constraintLeft_creator = 0x7f030253
com.example.myapplication:id/nav_controller_view_tag = 0x7f08013b
com.example.myapplication:attr/cardUseCompatPadding = 0x7f03008e
com.example.myapplication:color/m3_sys_color_dynamic_dark_tertiary_container = 0x7f050154
com.example.myapplication:layout/design_text_input_start_icon = 0x7f0b002d
com.example.myapplication:layout/mtrl_picker_header_toggle = 0x7f0b005d
com.example.myapplication:drawable/test_level_drawable = 0x7f07009a
com.example.myapplication:id/gone = 0x7f0800d5
com.example.myapplication:color/m3_sys_color_dynamic_dark_secondary_container = 0x7f050150
com.example.myapplication:string/abc_menu_alt_shortcut_label = 0x7f100008
com.example.myapplication:color/m3_sys_color_dynamic_dark_primary_container = 0x7f05014e
com.example.myapplication:color/m3_sys_color_dynamic_dark_on_tertiary = 0x7f05014a
com.example.myapplication:dimen/notification_small_icon_size_as_large = 0x7f06022f
com.example.myapplication:attr/thumbColor = 0x7f03040e
com.example.myapplication:color/m3_sys_color_dynamic_dark_on_secondary = 0x7f050146
com.example.myapplication:dimen/abc_text_size_display_4_material = 0x7f060046
com.example.myapplication:dimen/m3_btn_text_btn_padding_left = 0x7f0600d5
com.example.myapplication:style/Widget.Material3.MaterialCalendar.Year.Selected = 0x7f110355
com.example.myapplication:attr/colorSecondary = 0x7f0300fc
com.example.myapplication:attr/mock_label = 0x7f0302ca
com.example.myapplication:attr/useCompatPadding = 0x7f03044e
com.example.myapplication:attr/colorOutline = 0x7f0300f5
com.example.myapplication:color/m3_sys_color_dynamic_dark_inverse_surface = 0x7f050142
com.example.myapplication:dimen/notification_large_icon_height = 0x7f060228
com.example.myapplication:color/m3_sys_color_dynamic_dark_inverse_on_surface = 0x7f050140
com.example.myapplication:attr/actionProviderClass = 0x7f030024
com.example.myapplication:styleable/ActionMenuView = 0x7f120003
com.example.myapplication:color/m3_sys_color_dark_tertiary = 0x7f05013d
com.example.myapplication:style/Widget.AppCompat.Light.SearchView = 0x7f1102d1
com.example.myapplication:id/tv_detail_category = 0x7f0801e8
com.example.myapplication:color/m3_sys_color_dark_surface = 0x7f05013b
com.example.myapplication:attr/floatingActionButtonStyle = 0x7f0301af
com.example.myapplication:id/password_toggle = 0x7f08015f
com.example.myapplication:style/TextAppearance.AppCompat.Widget.ActionBar.Menu = 0x7f11018f
com.example.myapplication:drawable/navigation_empty_icon = 0x7f07008c
com.example.myapplication:color/m3_calendar_item_stroke_color = 0x7f050068
com.example.myapplication:attr/strokeColor = 0x7f030394
com.example.myapplication:id/scrollIndicatorUp = 0x7f08017c
com.example.myapplication:dimen/m3_sys_typescale_label_small_text_size = 0x7f060133
com.example.myapplication:color/m3_sys_color_dark_secondary = 0x7f050139
com.example.myapplication:id/autoComplete = 0x7f08005a
com.example.myapplication:id/accelerate = 0x7f080010
com.example.myapplication:color/m3_sys_color_dynamic_light_on_secondary = 0x7f05015c
com.example.myapplication:attr/tabMode = 0x7f0303b8
com.example.myapplication:style/ThemeOverlay.MaterialComponents.MaterialAlertDialog.Centered = 0x7f11028f
com.example.myapplication:color/m3_sys_color_dark_primary = 0x7f050137
com.example.myapplication:attr/navigationContentDescription = 0x7f0302ee
com.example.myapplication:drawable/ic_mtrl_chip_checked_circle = 0x7f07006e
com.example.myapplication:color/m3_sys_color_dark_on_tertiary = 0x7f050134
com.example.myapplication:styleable/KeyFrame = 0x7f120046
com.example.myapplication:color/m3_sys_color_dark_on_secondary_container = 0x7f050131
com.example.myapplication:string/material_timepicker_hour = 0x7f100060
com.example.myapplication:color/m3_switch_track_tint = 0x7f050124
com.example.myapplication:attr/itemVerticalPadding = 0x7f030226
com.example.myapplication:color/design_dark_default_color_secondary = 0x7f05003b
com.example.myapplication:style/Base.ThemeOverlay.AppCompat.Dark = 0x7f110075
com.example.myapplication:attr/textAppearancePopupMenuHeader = 0x7f0303e7
com.example.myapplication:string/material_motion_easing_decelerated = 0x7f100058
com.example.myapplication:color/m3_ref_palette_tertiary90 = 0x7f05011a
com.example.myapplication:attr/limitBoundsTo = 0x7f03027b
com.example.myapplication:anim/btn_checkbox_to_unchecked_icon_null_animation = 0x7f010011
com.example.myapplication:dimen/mtrl_navigation_item_icon_padding = 0x7f0601e5
com.example.myapplication:color/m3_ref_palette_tertiary40 = 0x7f050115
com.example.myapplication:id/src_over = 0x7f0801a7
com.example.myapplication:color/m3_ref_palette_tertiary30 = 0x7f050114
com.example.myapplication:color/m3_ref_palette_dynamic_neutral_variant80 = 0x7f0500a4
com.example.myapplication:style/Base.TextAppearance.AppCompat.Tooltip = 0x7f11002e
com.example.myapplication:color/m3_ref_palette_tertiary100 = 0x7f050112
com.example.myapplication:style/Widget.AppCompat.ActionBar.TabText = 0x7f1102a6
com.example.myapplication:color/m3_ref_palette_tertiary0 = 0x7f050110
com.example.myapplication:style/Widget.Material3.CircularProgressIndicator.Small = 0x7f110328
com.example.myapplication:id/textTop = 0x7f0801cc
com.example.myapplication:color/m3_ref_palette_secondary95 = 0x7f05010e
com.example.myapplication:string/status_completed = 0x7f100099
com.example.myapplication:animator/design_fab_hide_motion_spec = 0x7f020001
com.example.myapplication:color/m3_ref_palette_secondary30 = 0x7f050107
com.example.myapplication:id/accessibility_custom_action_19 = 0x7f08001d
com.example.myapplication:dimen/design_snackbar_padding_vertical = 0x7f060088
com.example.myapplication:color/material_dynamic_secondary90 = 0x7f0501d2
com.example.myapplication:color/m3_ref_palette_neutral70 = 0x7f0500e4
com.example.myapplication:color/m3_ref_palette_secondary100 = 0x7f050105
com.example.myapplication:attr/contentInsetRight = 0x7f030112
com.example.myapplication:color/m3_ref_palette_dynamic_tertiary0 = 0x7f0500c2
com.example.myapplication:attr/colorTertiaryContainer = 0x7f030104
com.example.myapplication:dimen/m3_alert_dialog_icon_margin = 0x7f0600a4
com.example.myapplication:style/Widget.Material3.Button.UnelevatedButton = 0x7f110315
com.example.myapplication:color/m3_ref_palette_primary99 = 0x7f050102
com.example.myapplication:animator/fragment_fade_enter = 0x7f020005
com.example.myapplication:color/m3_ref_palette_primary60 = 0x7f0500fd
com.example.myapplication:attr/tooltipFrameBackground = 0x7f030432
com.example.myapplication:attr/materialCalendarHeaderLayout = 0x7f0302a0
com.example.myapplication:attr/contentDescription = 0x7f03010e
com.example.myapplication:color/m3_ref_palette_primary50 = 0x7f0500fc
com.example.myapplication:attr/layout_constraintGuide_begin = 0x7f030248
com.example.myapplication:color/m3_ref_palette_primary20 = 0x7f0500f9
com.example.myapplication:color/m3_ref_palette_neutral_variant95 = 0x7f0500f4
com.example.myapplication:color/abc_btn_colored_borderless_text_material = 0x7f050002
com.example.myapplication:attr/carousel_touchUp_velocityThreshold = 0x7f030099
com.example.myapplication:color/m3_ref_palette_neutral_variant70 = 0x7f0500f1
com.example.myapplication:dimen/abc_action_bar_subtitle_bottom_margin_material = 0x7f06000b
com.example.myapplication:id/action_context_bar = 0x7f08003f
com.example.myapplication:style/Base.Theme.MaterialComponents.CompactMenu = 0x7f110062
com.example.myapplication:color/m3_ref_palette_neutral_variant60 = 0x7f0500f0
com.example.myapplication:color/m3_ref_palette_neutral_variant50 = 0x7f0500ef
com.example.myapplication:color/m3_ref_palette_neutral_variant40 = 0x7f0500ee
com.example.myapplication:color/m3_ref_palette_neutral99 = 0x7f0500e8
com.example.myapplication:layout/design_navigation_item_separator = 0x7f0b0028
com.example.myapplication:color/m3_ref_palette_neutral60 = 0x7f0500e3
com.example.myapplication:color/m3_ref_palette_neutral40 = 0x7f0500e1
com.example.myapplication:attr/alphabeticModifiers = 0x7f030030
com.example.myapplication:attr/itemShapeFillColor = 0x7f03021a
com.example.myapplication:attr/recyclerViewStyle = 0x7f030341
com.example.myapplication:color/m3_ref_palette_error60 = 0x7f0500d6
com.example.myapplication:attr/motionEffect_translationX = 0x7f0302e0
com.example.myapplication:id/accessibility_custom_action_18 = 0x7f08001c
com.example.myapplication:color/m3_timepicker_button_text_color = 0x7f050191
com.example.myapplication:attr/textAppearanceLargePopupMenu = 0x7f0303e1
com.example.myapplication:color/m3_ref_palette_error50 = 0x7f0500d5
com.example.myapplication:style/Base.Widget.AppCompat.Toolbar.Button.Navigation = 0x7f1100f0
com.example.myapplication:dimen/m3_appbar_scrim_height_trigger_medium = 0x7f0600ab
com.example.myapplication:style/Base.Widget.Material3.TabLayout.Secondary = 0x7f110103
com.example.myapplication:id/tv_today = 0x7f080215
com.example.myapplication:color/m3_ref_palette_error40 = 0x7f0500d4
com.example.myapplication:color/m3_ref_palette_dynamic_tertiary90 = 0x7f0500cc
com.example.myapplication:attr/textAppearanceLabelLarge = 0x7f0303de
com.example.myapplication:attr/autoCompleteMode = 0x7f03003d
com.example.myapplication:color/m3_ref_palette_dynamic_tertiary80 = 0x7f0500cb
com.example.myapplication:color/m3_ref_palette_dynamic_tertiary60 = 0x7f0500c9
com.example.myapplication:id/design_menu_item_action_area = 0x7f0800a3
com.example.myapplication:color/material_dynamic_neutral50 = 0x7f0501a7
com.example.myapplication:string/mtrl_picker_text_input_day_abbr = 0x7f100081
com.example.myapplication:dimen/m3_btn_stroke_size = 0x7f0600d2
com.example.myapplication:attr/gapBetweenBars = 0x7f0301d4
com.example.myapplication:attr/restoreState = 0x7f030346
com.example.myapplication:color/m3_ref_palette_dynamic_tertiary50 = 0x7f0500c8
com.example.myapplication:id/sliding_pane_layout = 0x7f080197
com.example.myapplication:style/ThemeOverlay.MaterialComponents.MaterialAlertDialog.Picker.Date.Spinner = 0x7f110294
com.example.myapplication:animator/fragment_open_enter = 0x7f020007
com.example.myapplication:dimen/mtrl_min_touch_target_size = 0x7f0601e0
com.example.myapplication:style/Widget.Material3.ExtendedFloatingActionButton.Tertiary = 0x7f110337
com.example.myapplication:attr/windowActionModeOverlay = 0x7f030463
com.example.myapplication:style/MaterialAlertDialog.Material3 = 0x7f110115
com.example.myapplication:color/m3_button_background_color_selector = 0x7f050062
com.example.myapplication:attr/dividerVertical = 0x7f030157
com.example.myapplication:color/m3_ref_palette_dynamic_secondary90 = 0x7f0500bf
com.example.myapplication:attr/textInputFilledExposedDropdownMenuStyle = 0x7f0303fa
com.example.myapplication:color/accent_material_dark = 0x7f050019
com.example.myapplication:layout/mtrl_picker_header_selection_text = 0x7f0b005b
com.example.myapplication:id/accessibility_custom_action_6 = 0x7f08002e
com.example.myapplication:dimen/m3_bottomappbar_fab_cradle_margin = 0x7f0600bd
com.example.myapplication:integer/status_bar_notification_info_maxnum = 0x7f09002e
com.example.myapplication:color/m3_ref_palette_dynamic_secondary70 = 0x7f0500bd
com.example.myapplication:dimen/abc_action_bar_content_inset_material = 0x7f060000
com.example.myapplication:color/black = 0x7f050021
com.example.myapplication:attr/actionModeTheme = 0x7f030020
com.example.myapplication:color/m3_ref_palette_dynamic_secondary50 = 0x7f0500bb
com.example.myapplication:style/Theme.AppCompat.Dialog = 0x7f1101e1
com.example.myapplication:id/ignoreRequest = 0x7f0800e6
com.example.myapplication:anim/abc_fade_in = 0x7f010000
com.example.myapplication:color/m3_ref_palette_dynamic_secondary100 = 0x7f0500b7
com.example.myapplication:style/TextAppearance.AppCompat.Light.SearchResult.Title = 0x7f110180
com.example.myapplication:color/m3_ref_palette_dynamic_primary95 = 0x7f0500b3
com.example.myapplication:layout/mtrl_picker_dialog = 0x7f0b0057
com.example.myapplication:style/TextAppearance.AppCompat.Widget.ActionBar.Title.Inverse = 0x7f110193
com.example.myapplication:dimen/mtrl_calendar_selection_text_baseline_to_bottom_fullscreen = 0x7f0601ac
com.example.myapplication:color/m3_ref_palette_dynamic_primary40 = 0x7f0500ad
com.example.myapplication:attr/waveVariesBy = 0x7f030460
com.example.myapplication:style/ShapeAppearanceOverlay.Material3.Chip = 0x7f110158
com.example.myapplication:color/m3_slider_thumb_color = 0x7f050122
com.example.myapplication:styleable/NavArgument = 0x7f12006d
com.example.myapplication:color/m3_ref_palette_dynamic_neutral_variant60 = 0x7f0500a2
com.example.myapplication:color/m3_ref_palette_dynamic_neutral_variant50 = 0x7f0500a1
com.example.myapplication:color/foreground_material_light = 0x7f05005c
com.example.myapplication:color/m3_ref_palette_dynamic_neutral_variant40 = 0x7f0500a0
com.example.myapplication:dimen/abc_edit_text_inset_top_material = 0x7f06002e
com.example.myapplication:style/TextAppearance.AppCompat.Widget.ActionMode.Title.Inverse = 0x7f110197
com.example.myapplication:color/m3_sys_color_dark_background = 0x7f050125
com.example.myapplication:color/m3_ref_palette_dynamic_neutral_variant100 = 0x7f05009d
com.example.myapplication:color/m3_ref_palette_dynamic_neutral_variant10 = 0x7f05009c
com.example.myapplication:attr/itemMinHeight = 0x7f030213
com.example.myapplication:attr/arcMode = 0x7f030038
com.example.myapplication:color/m3_ref_palette_dynamic_neutral_variant0 = 0x7f05009b
com.example.myapplication:style/Theme.Material3.DayNight.Dialog = 0x7f1101fd
com.example.myapplication:layout/test_navigation_bar_item_layout = 0x7f0b006e
com.example.myapplication:color/m3_ref_palette_dynamic_primary30 = 0x7f0500ac
com.example.myapplication:style/Widget.Material3.AppBarLayout = 0x7f1102fb
com.example.myapplication:attr/behavior_halfExpandedRatio = 0x7f03005f
com.example.myapplication:color/m3_ref_palette_dynamic_neutral40 = 0x7f050093
com.example.myapplication:string/m3_sys_typescale_title_small_font = 0x7f100050
com.example.myapplication:attr/labelStyle = 0x7f03022c
com.example.myapplication:style/Widget.Material3.Snackbar = 0x7f11036b
com.example.myapplication:color/m3_ref_palette_dynamic_neutral0 = 0x7f05008e
com.example.myapplication:styleable/AppCompatImageView = 0x7f120011
com.example.myapplication:attr/listChoiceBackgroundIndicator = 0x7f03027f
com.example.myapplication:attr/actionModeSplitBackground = 0x7f03001e
com.example.myapplication:color/m3_navigation_item_text_color = 0x7f05008a
com.example.myapplication:color/m3_navigation_bar_item_with_indicator_icon_tint = 0x7f050085
com.example.myapplication:attr/maxButtonHeight = 0x7f0302b8
com.example.myapplication:attr/textInputOutlinedExposedDropdownMenuStyle = 0x7f0303fe
com.example.myapplication:color/m3_highlighted_text = 0x7f050083
com.example.myapplication:attr/actionBarTabStyle = 0x7f03000a
com.example.myapplication:attr/ensureMinTouchTargetSize = 0x7f03017d
com.example.myapplication:color/m3_dynamic_default_color_secondary_text = 0x7f05007e
com.example.myapplication:attr/flow_lastVerticalStyle = 0x7f0301bd
com.example.myapplication:color/m3_dynamic_dark_highlighted_text = 0x7f05007a
com.example.myapplication:color/m3_dark_hint_foreground = 0x7f050074
com.example.myapplication:color/m3_sys_color_dark_on_background = 0x7f05012b
com.example.myapplication:color/button_material_light = 0x7f050029
com.example.myapplication:color/m3_ref_palette_dynamic_secondary30 = 0x7f0500b9
com.example.myapplication:attr/listPreferredItemPaddingRight = 0x7f03028c
com.example.myapplication:id/search_button = 0x7f080181
com.example.myapplication:dimen/m3_btn_icon_btn_padding_right = 0x7f0600c7
com.example.myapplication:attr/colorSecondaryVariant = 0x7f0300fe
com.example.myapplication:color/m3_switch_thumb_tint = 0x7f050123
com.example.myapplication:color/m3_sys_color_dynamic_dark_on_surface = 0x7f050148
com.example.myapplication:color/m3_dark_default_color_secondary_text = 0x7f050072
com.example.myapplication:layout/select_dialog_singlechoice_material = 0x7f0b0068
com.example.myapplication:style/Widget.MaterialComponents.Toolbar.Primary = 0x7f1103f9
com.example.myapplication:color/m3_card_ripple_color = 0x7f05006a
com.example.myapplication:color/m3_ref_palette_primary30 = 0x7f0500fa
com.example.myapplication:style/Base.Theme.MaterialComponents.DialogWhenLarge = 0x7f110068
com.example.myapplication:color/m3_card_stroke_color = 0x7f05006b
com.example.myapplication:id/progress_horizontal = 0x7f080168
com.example.myapplication:style/TestStyleWithThemeLineHeightAttribute = 0x7f11016e
com.example.myapplication:attr/secondaryActivityName = 0x7f030356
com.example.myapplication:color/m3_ref_palette_dynamic_secondary10 = 0x7f0500b6
com.example.myapplication:id/select_dialog_listview = 0x7f080189
com.example.myapplication:id/packed = 0x7f080159
com.example.myapplication:style/Widget.MaterialComponents.TimePicker.Display.Divider = 0x7f1103f2
com.example.myapplication:color/m3_appbar_overlay_color = 0x7f05005f
com.example.myapplication:dimen/material_divider_thickness = 0x7f06014a
com.example.myapplication:color/m3_ref_palette_primary90 = 0x7f050100
com.example.myapplication:attr/motionEasingLinear = 0x7f0302d9
com.example.myapplication:color/m3_ref_palette_tertiary95 = 0x7f05011b
com.example.myapplication:attr/tooltipForegroundColor = 0x7f030431
com.example.myapplication:color/error_color_material_dark = 0x7f050059
com.example.myapplication:attr/carousel_nextState = 0x7f030095
com.example.myapplication:color/dim_foreground_disabled_material_dark = 0x7f050055
com.example.myapplication:style/Base.TextAppearance.AppCompat.Light.Widget.PopupMenu.Large = 0x7f110020
com.example.myapplication:dimen/m3_btn_disabled_elevation = 0x7f0600c2
com.example.myapplication:attr/itemRippleColor = 0x7f030217
com.example.myapplication:color/m3_ref_palette_neutral_variant30 = 0x7f0500ed
com.example.myapplication:id/action_menu_presenter = 0x7f080043
com.example.myapplication:color/design_fab_stroke_end_inner_color = 0x7f05004f
com.example.myapplication:color/design_fab_shadow_mid_color = 0x7f05004d
com.example.myapplication:attr/triggerReceiver = 0x7f030449
com.example.myapplication:color/design_default_color_surface = 0x7f05004a
com.example.myapplication:color/design_default_color_secondary = 0x7f050048
com.example.myapplication:dimen/mtrl_progress_circular_size_extra_small = 0x7f0601f8
com.example.myapplication:color/design_default_color_primary_variant = 0x7f050047
com.example.myapplication:styleable/FragmentContainerView = 0x7f12003e
com.example.myapplication:attr/materialCalendarDayOfWeekLabel = 0x7f03029b
com.example.myapplication:color/design_default_color_primary_dark = 0x7f050046
com.example.myapplication:attr/materialDisplayDividerStyle = 0x7f0302af
com.example.myapplication:dimen/m3_btn_max_width = 0x7f0600cd
com.example.myapplication:id/notification_main_column_container = 0x7f080152
com.example.myapplication:color/design_default_color_on_secondary = 0x7f050043
com.example.myapplication:attr/prefixTextAppearance = 0x7f03032b
com.example.myapplication:attr/checkedButton = 0x7f03009f
com.example.myapplication:color/design_default_color_on_background = 0x7f050040
com.example.myapplication:id/content = 0x7f080090
com.example.myapplication:color/design_dark_default_color_primary_dark = 0x7f050039
com.example.myapplication:attr/expandedTitleGravity = 0x7f03018a
com.example.myapplication:style/Widget.Material3.ExtendedFloatingActionButton.Secondary = 0x7f110335
com.example.myapplication:id/off = 0x7f080153
com.example.myapplication:attr/colorSurfaceVariant = 0x7f030101
com.example.myapplication:color/m3_ref_palette_dynamic_primary90 = 0x7f0500b2
com.example.myapplication:color/design_dark_default_color_on_secondary = 0x7f050036
com.example.myapplication:layout/design_layout_tab_icon = 0x7f0b0023
com.example.myapplication:color/design_dark_default_color_background = 0x7f050031
com.example.myapplication:attr/onHide = 0x7f0302fb
com.example.myapplication:style/ThemeOverlay.Material3.Button = 0x7f110254
com.example.myapplication:dimen/design_fab_translation_z_hovered_focused = 0x7f060075
com.example.myapplication:dimen/test_navigation_bar_icon_size = 0x7f06023b
com.example.myapplication:color/test_mtrl_calendar_day = 0x7f05024e
com.example.myapplication:styleable/ConstraintLayout_Layout = 0x7f12002a
com.example.myapplication:attr/snackbarTextViewStyle = 0x7f030373
com.example.myapplication:color/cardview_shadow_end_color = 0x7f05002c
com.example.myapplication:attr/color = 0x7f0300dd
com.example.myapplication:color/cardview_light_background = 0x7f05002b
com.example.myapplication:attr/finishSecondaryWithPrimary = 0x7f0301a6
com.example.myapplication:color/bright_foreground_inverse_material_dark = 0x7f050024
com.example.myapplication:attr/itemPaddingTop = 0x7f030216
com.example.myapplication:color/background_material_light = 0x7f050020
com.example.myapplication:color/background_material_dark = 0x7f05001f
com.example.myapplication:color/androidx_core_secondary_text_default_material_light = 0x7f05001c
com.example.myapplication:dimen/mtrl_high_ripple_pressed_alpha = 0x7f0601da
com.example.myapplication:id/honorRequest = 0x7f0800e0
com.example.myapplication:id/easeInOut = 0x7f0800b9
com.example.myapplication:attr/startDestination = 0x7f030385
com.example.myapplication:color/abc_tint_switch_track = 0x7f050018
com.example.myapplication:id/accessibility_custom_action_0 = 0x7f080012
com.example.myapplication:id/material_clock_display = 0x7f080105
com.example.myapplication:color/abc_tint_btn_checkable = 0x7f050013
com.example.myapplication:attr/defaultQueryHint = 0x7f030146
com.example.myapplication:color/abc_search_url_text = 0x7f05000d
com.example.myapplication:color/m3_ref_palette_neutral10 = 0x7f0500dd
com.example.myapplication:anim/nav_default_exit_anim = 0x7f010021
com.example.myapplication:integer/design_snackbar_text_max_lines = 0x7f090007
com.example.myapplication:style/Widget.Support.CoordinatorLayout = 0x7f1103fd
com.example.myapplication:color/m3_sys_color_light_inverse_on_surface = 0x7f05016e
com.example.myapplication:attr/colorOnContainer = 0x7f0300e8
com.example.myapplication:attr/materialButtonStyle = 0x7f030298
com.example.myapplication:attr/floatingActionButtonLargePrimaryStyle = 0x7f0301a8
com.example.myapplication:color/abc_hint_foreground_material_light = 0x7f050008
com.example.myapplication:string/material_slider_range_start = 0x7f10005d
com.example.myapplication:attr/touchAnchorId = 0x7f030436
com.example.myapplication:color/m3_ref_palette_dynamic_tertiary30 = 0x7f0500c6
com.example.myapplication:style/TextAppearance.Material3.BodySmall = 0x7f1101b6
com.example.myapplication:color/m3_timepicker_clock_text_color = 0x7f050192
com.example.myapplication:color/abc_hint_foreground_material_dark = 0x7f050007
com.example.myapplication:style/Widget.MaterialComponents.TimePicker.ImageButton = 0x7f1103f6
com.example.myapplication:bool/mtrl_btn_textappearance_all_caps = 0x7f040011
com.example.myapplication:color/m3_sys_color_dark_outline = 0x7f050136
com.example.myapplication:drawable/abc_dialog_material_background = 0x7f07001a
com.example.myapplication:attr/triggerSlack = 0x7f03044a
com.example.myapplication:dimen/abc_text_size_subhead_material = 0x7f06004d
com.example.myapplication:bool/m3_sys_typescale_label_large_text_all_caps = 0x7f04000b
com.example.myapplication:attr/navigationIconTint = 0x7f0302f0
com.example.myapplication:color/m3_default_color_secondary_text = 0x7f050077
com.example.myapplication:drawable/design_ic_visibility = 0x7f070061
com.example.myapplication:attr/boxCollapsedPaddingTop = 0x7f030071
com.example.myapplication:drawable/abc_list_pressed_holo_light = 0x7f070030
com.example.myapplication:id/frost = 0x7f0800d2
com.example.myapplication:dimen/mtrl_extended_fab_min_height = 0x7f0601cb
com.example.myapplication:bool/m3_sys_typescale_headline_small_text_all_caps = 0x7f04000a
com.example.myapplication:attr/colorSwitchThumbNormal = 0x7f030102
com.example.myapplication:id/NO_DEBUG = 0x7f080007
com.example.myapplication:bool/m3_sys_typescale_headline_medium_text_all_caps = 0x7f040009
com.example.myapplication:style/Base.DialogWindowTitle.AppCompat = 0x7f11000e
com.example.myapplication:styleable/MotionHelper = 0x7f120067
com.example.myapplication:attr/motionEffect_move = 0x7f0302dd
com.example.myapplication:id/aligned = 0x7f08004d
com.example.myapplication:style/Base.TextAppearance.AppCompat.Subhead = 0x7f11002a
com.example.myapplication:bool/m3_sys_typescale_headline_large_text_all_caps = 0x7f040008
com.example.myapplication:id/deltaRelative = 0x7f0800a0
com.example.myapplication:bool/m3_sys_typescale_display_small_text_all_caps = 0x7f040007
com.example.myapplication:string/m3_sys_typescale_label_large_font = 0x7f10004b
com.example.myapplication:attr/indicatorColor = 0x7f030200
com.example.myapplication:dimen/material_bottom_sheet_max_width = 0x7f06013c
com.example.myapplication:attr/behavior_overlapTop = 0x7f030061
com.example.myapplication:bool/m3_sys_typescale_body_medium_text_all_caps = 0x7f040003
com.example.myapplication:attr/windowFixedWidthMajor = 0x7f030466
com.example.myapplication:style/ThemeOverlay.Material3.Dialog.Alert.Framework = 0x7f110260
com.example.myapplication:id/src_atop = 0x7f0801a5
com.example.myapplication:style/Base.Widget.AppCompat.SearchView.ActionBar = 0x7f1100e8
com.example.myapplication:style/Base.TextAppearance.AppCompat.Medium = 0x7f110022
com.example.myapplication:attr/paddingEnd = 0x7f030305
com.example.myapplication:layout/abc_popup_menu_item_layout = 0x7f0b0013
com.example.myapplication:id/textview_second = 0x7f0801d6
com.example.myapplication:attr/controlBackground = 0x7f03011e
com.example.myapplication:dimen/mtrl_alert_dialog_background_inset_end = 0x7f060162
com.example.myapplication:attr/actionModeBackground = 0x7f030013
com.example.myapplication:color/mtrl_chip_background_color = 0x7f05020a
com.example.myapplication:attr/textureWidth = 0x7f03040a
com.example.myapplication:attr/voiceIcon = 0x7f030459
com.example.myapplication:attr/arrowHeadLength = 0x7f03003a
com.example.myapplication:styleable/ConstraintOverride = 0x7f12002d
com.example.myapplication:id/special_effects_controller_view_tag = 0x7f08019e
com.example.myapplication:color/m3_ref_palette_secondary20 = 0x7f050106
com.example.myapplication:attr/actionOverflowButtonStyle = 0x7f030022
com.example.myapplication:attr/paddingRightSystemWindowInsets = 0x7f030307
com.example.myapplication:dimen/mtrl_shape_corner_size_small_component = 0x7f060202
com.example.myapplication:color/m3_sys_color_dynamic_dark_tertiary = 0x7f050153
com.example.myapplication:id/animateToStart = 0x7f080053
com.example.myapplication:attr/viewTransitionMode = 0x7f030454
com.example.myapplication:styleable/MaterialAutoCompleteTextView = 0x7f120053
com.example.myapplication:style/ThemeOverlay.MaterialComponents.MaterialAlertDialog.Picker.Date.Calendar = 0x7f110291
com.example.myapplication:attr/ttcIndex = 0x7f03044b
com.example.myapplication:attr/windowFixedHeightMajor = 0x7f030464
com.example.myapplication:attr/constraintRotate = 0x7f030106
com.example.myapplication:attr/route = 0x7f03034c
com.example.myapplication:color/mtrl_calendar_selected_range = 0x7f050207
com.example.myapplication:dimen/design_tab_scrollable_min_width = 0x7f06008c
com.example.myapplication:attr/triggerId = 0x7f030448
com.example.myapplication:animator/mtrl_fab_show_motion_spec = 0x7f02001d
com.example.myapplication:attr/transformPivotTarget = 0x7f030442
com.example.myapplication:attr/trackColorActive = 0x7f03043b
com.example.myapplication:dimen/m3_chip_checked_hovered_translation_z = 0x7f0600e0
com.example.myapplication:attr/cornerFamilyBottomLeft = 0x7f030121
com.example.myapplication:attr/track = 0x7f030439
com.example.myapplication:style/ShapeAppearance.MaterialComponents.MediumComponent = 0x7f11014e
com.example.myapplication:style/Widget.Design.ScrimInsetsFrameLayout = 0x7f1102f4
com.example.myapplication:id/fixed = 0x7f0800cd
com.example.myapplication:color/m3_navigation_bar_item_with_indicator_label_tint = 0x7f050086
com.example.myapplication:attr/touchRegionId = 0x7f030438
com.example.myapplication:attr/tooltipText = 0x7f030434
com.example.myapplication:attr/behavior_expandedOffset = 0x7f03005d
com.example.myapplication:dimen/mtrl_navigation_rail_compact_width = 0x7f0601ea
com.example.myapplication:attr/toolbarSurfaceStyle = 0x7f030430
com.example.myapplication:attr/customReference = 0x7f03013c
com.example.myapplication:style/Widget.AppCompat.SearchView.ActionBar = 0x7f1102e1
com.example.myapplication:id/path = 0x7f080160
com.example.myapplication:id/tv_task_title = 0x7f0801f0
com.example.myapplication:style/Platform.AppCompat = 0x7f110128
com.example.myapplication:attr/textAppearanceHeadline3 = 0x7f0303d7
com.example.myapplication:attr/toolbarNavigationButtonStyle = 0x7f03042e
com.example.myapplication:string/character_counter_overflowed_content_description = 0x7f100027
com.example.myapplication:style/Base.TextAppearance.MaterialComponents.Button = 0x7f110046
com.example.myapplication:dimen/abc_search_view_preferred_width = 0x7f060037
com.example.myapplication:attr/windowNoTitle = 0x7f03046a
com.example.myapplication:attr/toolbarId = 0x7f03042d
com.example.myapplication:attr/fontWeight = 0x7f0301d0
com.example.myapplication:attr/titleMargins = 0x7f030428
com.example.myapplication:layout/mtrl_calendar_year = 0x7f0b0052
com.example.myapplication:color/abc_secondary_text_material_dark = 0x7f050011
com.example.myapplication:style/Base.V14.Theme.MaterialComponents.Light = 0x7f11008d
com.example.myapplication:attr/titleMargin = 0x7f030423
com.example.myapplication:dimen/abc_alert_dialog_button_dimen = 0x7f060011
com.example.myapplication:attr/title = 0x7f03041f
com.example.myapplication:style/Base.TextAppearance.AppCompat.SearchResult.Title = 0x7f110027
com.example.myapplication:dimen/mtrl_btn_inset = 0x7f06017d
com.example.myapplication:attr/tickMark = 0x7f030419
com.example.myapplication:color/mtrl_btn_text_color_disabled = 0x7f050203
com.example.myapplication:color/material_dynamic_neutral90 = 0x7f0501ab
com.example.myapplication:attr/tickColor = 0x7f030416
com.example.myapplication:style/Widget.Material3.LinearProgressIndicator = 0x7f110341
com.example.myapplication:attr/thumbTintMode = 0x7f030415
com.example.myapplication:attr/thumbTextPadding = 0x7f030413
com.example.myapplication:style/Widget.MaterialComponents.TabLayout = 0x7f1103de
com.example.myapplication:drawable/abc_btn_default_mtrl_shape = 0x7f07000f
com.example.myapplication:attr/thumbRadius = 0x7f030410
com.example.myapplication:style/Base.TextAppearance.AppCompat.Widget.ActionBar.Subtitle = 0x7f110030
com.example.myapplication:id/radio = 0x7f080169
com.example.myapplication:animator/mtrl_btn_state_list_anim = 0x7f020013
com.example.myapplication:color/m3_ref_palette_dynamic_tertiary70 = 0x7f0500ca
com.example.myapplication:attr/theme = 0x7f03040b
com.example.myapplication:attr/textPanX = 0x7f030404
com.example.myapplication:attr/layout_dodgeInsetEdges = 0x7f030267
com.example.myapplication:attr/tabPadding = 0x7f0303b9
com.example.myapplication:attr/yearSelectedStyle = 0x7f03046b
com.example.myapplication:style/Widget.Material3.FloatingActionButton.Large.Secondary = 0x7f110339
com.example.myapplication:style/Widget.Material3.Chip.Filter = 0x7f11031c
com.example.myapplication:attr/errorIconTint = 0x7f030182
com.example.myapplication:id/spacer = 0x7f08019d
com.example.myapplication:id/btn_add_category = 0x7f080068
com.example.myapplication:attr/textLocale = 0x7f030401
com.example.myapplication:attr/actionModeCloseContentDescription = 0x7f030015
com.example.myapplication:color/m3_ref_palette_tertiary80 = 0x7f050119
com.example.myapplication:attr/itemTextAppearanceActive = 0x7f030223
com.example.myapplication:attr/textInputFilledStyle = 0x7f0303fb
com.example.myapplication:style/Widget.AppCompat.Button.ButtonBar.AlertDialog = 0x7f1102b1
com.example.myapplication:id/mtrl_picker_header_selection_text = 0x7f080131
com.example.myapplication:bool/m3_sys_typescale_title_large_text_all_caps = 0x7f04000e
com.example.myapplication:attr/textInputFilledDenseStyle = 0x7f0303f9
com.example.myapplication:id/uniform = 0x7f0801f2
com.example.myapplication:attr/textBackgroundZoom = 0x7f0303f4
com.example.myapplication:style/Widget.MaterialComponents.ActionBar.PrimarySurface = 0x7f110382
com.example.myapplication:attr/layout_editor_absoluteY = 0x7f030269
com.example.myapplication:id/action_FirstFragment_to_SecondFragment = 0x7f080035
com.example.myapplication:attr/textBackgroundPanY = 0x7f0303f2
com.example.myapplication:attr/imageZoom = 0x7f0301fd
com.example.myapplication:dimen/notification_top_pad = 0x7f060231
com.example.myapplication:string/mtrl_picker_out_of_range = 0x7f100077
com.example.myapplication:style/ShapeAppearanceOverlay.MaterialComponents.MaterialCalendar.Window.Fullscreen = 0x7f110162
com.example.myapplication:attr/bottomSheetStyle = 0x7f03006e
com.example.myapplication:attr/wavePhase = 0x7f03045e
com.example.myapplication:attr/textAppearanceSubtitle2 = 0x7f0303ec
com.example.myapplication:attr/textAppearanceSubtitle1 = 0x7f0303eb
com.example.myapplication:attr/fontProviderPackage = 0x7f0301cb
com.example.myapplication:style/Widget.AppCompat.Light.ActionBar.Solid = 0x7f1102be
com.example.myapplication:attr/textAppearanceSmallPopupMenu = 0x7f0303ea
com.example.myapplication:id/compress = 0x7f08008c
com.example.myapplication:attr/customDimension = 0x7f030137
com.example.myapplication:attr/textAppearanceSearchResultTitle = 0x7f0303e9
com.example.myapplication:styleable/TabLayout = 0x7f120094
com.example.myapplication:style/RtlOverlay.Widget.AppCompat.PopupMenuItem.Text = 0x7f11013d
com.example.myapplication:dimen/abc_list_item_padding_horizontal_material = 0x7f060033
com.example.myapplication:color/m3_dark_default_color_primary_text = 0x7f050071
com.example.myapplication:attr/textAppearanceListItemSecondary = 0x7f0303e4
com.example.myapplication:style/Widget.Material3.MaterialTimePicker.Clock = 0x7f11035c
com.example.myapplication:attr/textAppearanceHeadline5 = 0x7f0303d9
com.example.myapplication:style/ThemeOverlay.Material3.Button.ElevatedButton = 0x7f110255
com.example.myapplication:attr/textAppearanceHeadline2 = 0x7f0303d6
com.example.myapplication:attr/textAppearanceDisplaySmall = 0x7f0303d4
com.example.myapplication:attr/titleMarginBottom = 0x7f030424
com.example.myapplication:color/m3_chip_assist_text_color = 0x7f05006c
com.example.myapplication:attr/searchViewStyle = 0x7f030354
com.example.myapplication:attr/expandedTitleMarginTop = 0x7f03018f
com.example.myapplication:style/ThemeOverlay.Material3.DynamicColors.DayNight = 0x7f110262
com.example.myapplication:attr/panelBackground = 0x7f03030b
com.example.myapplication:attr/textAppearanceDisplayLarge = 0x7f0303d2
com.example.myapplication:dimen/mtrl_low_ripple_hovered_alpha = 0x7f0601de
com.example.myapplication:styleable/RecyclerView = 0x7f120080
com.example.myapplication:dimen/test_navigation_bar_active_item_min_width = 0x7f060237
com.example.myapplication:attr/textAppearanceButton = 0x7f0303d0
com.example.myapplication:attr/textAppearanceBody2 = 0x7f0303cc
com.example.myapplication:dimen/mtrl_tooltip_padding = 0x7f060222
com.example.myapplication:color/m3_sys_color_dynamic_light_on_primary = 0x7f05015a
com.example.myapplication:dimen/m3_btn_dialog_btn_min_width = 0x7f0600c0
com.example.myapplication:attr/telltales_tailScale = 0x7f0303c8
com.example.myapplication:drawable/abc_btn_check_to_on_mtrl_000 = 0x7f07000c
com.example.myapplication:attr/telltales_tailColor = 0x7f0303c7
com.example.myapplication:drawable/mtrl_popupmenu_background_overlay = 0x7f07008a
com.example.myapplication:layout/material_time_input = 0x7f0b003f
com.example.myapplication:attr/tabIndicatorAnimationDuration = 0x7f0303af
com.example.myapplication:attr/onStateTransition = 0x7f0302ff
com.example.myapplication:attr/targetPackage = 0x7f0303c6
com.example.myapplication:attr/searchHintIcon = 0x7f030352
com.example.myapplication:color/m3_sys_color_dark_on_primary_container = 0x7f05012f
com.example.myapplication:style/Base.TextAppearance.MaterialComponents.Badge = 0x7f110045
com.example.myapplication:color/abc_decor_view_status_guard_light = 0x7f050006
com.example.myapplication:color/bright_foreground_material_dark = 0x7f050026
com.example.myapplication:attr/targetId = 0x7f0303c5
com.example.myapplication:style/TextAppearance.AppCompat.Widget.Button.Colored = 0x7f11019a
com.example.myapplication:attr/passwordToggleTint = 0x7f030311
com.example.myapplication:style/Widget.MaterialComponents.MaterialCalendar.HeaderDivider = 0x7f1103c0
com.example.myapplication:attr/tabTextColor = 0x7f0303c3
com.example.myapplication:style/Widget.MaterialComponents.MaterialCalendar.HeaderCancelButton = 0x7f1103be
com.example.myapplication:attr/tabTextAppearance = 0x7f0303c2
com.example.myapplication:dimen/mtrl_calendar_day_corner = 0x7f060191
com.example.myapplication:attr/tabSecondaryStyle = 0x7f0303bf
com.example.myapplication:dimen/mtrl_calendar_action_confirm_button_min_width = 0x7f06018c
com.example.myapplication:dimen/material_text_view_test_line_height_override = 0x7f06015a
com.example.myapplication:attr/tabPaddingBottom = 0x7f0303ba
com.example.myapplication:style/Widget.Material3.ActionBar.Solid = 0x7f1102f9
com.example.myapplication:id/textinput_placeholder = 0x7f0801d3
com.example.myapplication:dimen/m3_sys_elevation_level3 = 0x7f060112
com.example.myapplication:attr/tabMinWidth = 0x7f0303b7
com.example.myapplication:color/mtrl_navigation_bar_ripple_color = 0x7f05021c
com.example.myapplication:attr/endIconDrawable = 0x7f030177
com.example.myapplication:attr/tooltipStyle = 0x7f030433
com.example.myapplication:drawable/abc_seekbar_track_material = 0x7f070043
com.example.myapplication:attr/tabIndicatorGravity = 0x7f0303b3
com.example.myapplication:color/m3_ref_palette_dynamic_neutral10 = 0x7f05008f
com.example.myapplication:attr/flow_firstVerticalStyle = 0x7f0301b5
com.example.myapplication:anim/abc_slide_out_top = 0x7f010009
com.example.myapplication:attr/tabIndicatorColor = 0x7f0303b1
com.example.myapplication:attr/materialCalendarYearNavigationButton = 0x7f0302a8
com.example.myapplication:style/Base.Theme.AppCompat = 0x7f11004c
com.example.myapplication:attr/textInputOutlinedStyle = 0x7f0303ff
com.example.myapplication:attr/enforceTextAppearance = 0x7f03017c
com.example.myapplication:dimen/mtrl_calendar_action_height = 0x7f06018d
com.example.myapplication:attr/layout_constraintWidth_percent = 0x7f030266
com.example.myapplication:attr/floatingActionButtonSurfaceStyle = 0x7f0301b0
com.example.myapplication:drawable/abc_ic_menu_share_mtrl_alpha = 0x7f070026
com.example.myapplication:attr/tabBackground = 0x7f0303a9
com.example.myapplication:attr/region_widthMoreThan = 0x7f030345
com.example.myapplication:attr/switchTextAppearance = 0x7f0303a8
com.example.myapplication:color/m3_dynamic_dark_primary_text_disable_only = 0x7f05007c
com.example.myapplication:attr/drawableLeftCompat = 0x7f03015e
com.example.myapplication:attr/switchPadding = 0x7f0303a6
com.example.myapplication:attr/searchIcon = 0x7f030353
com.example.myapplication:color/m3_ref_palette_dynamic_neutral50 = 0x7f050094
com.example.myapplication:id/nav_tasks = 0x7f080140
com.example.myapplication:attr/textAppearanceHeadline4 = 0x7f0303d8
com.example.myapplication:attr/cornerSizeBottomRight = 0x7f030128
com.example.myapplication:attr/sizePercent = 0x7f03036f
com.example.myapplication:style/TextAppearance.Material3.HeadlineLarge = 0x7f1101ba
com.example.myapplication:color/abc_btn_colored_text_material = 0x7f050003
com.example.myapplication:attr/tabSelectedTextColor = 0x7f0303c0
com.example.myapplication:style/Widget.MaterialComponents.ActionBar.Surface = 0x7f110384
com.example.myapplication:attr/suffixText = 0x7f0303a1
com.example.myapplication:color/m3_ref_palette_tertiary10 = 0x7f050111
com.example.myapplication:attr/actionBarStyle = 0x7f030008
com.example.myapplication:attr/subheaderInsetStart = 0x7f030399
com.example.myapplication:attr/subheaderInsetEnd = 0x7f030398
com.example.myapplication:navigation/nav_graph = 0x7f0e0000
com.example.myapplication:id/bounceStart = 0x7f080067
com.example.myapplication:dimen/mtrl_btn_elevation = 0x7f060178
com.example.myapplication:color/m3_ref_palette_dynamic_neutral100 = 0x7f050090
com.example.myapplication:style/Widget.Material3.BottomNavigationView.ActiveIndicator = 0x7f110303
com.example.myapplication:attr/primaryActivityName = 0x7f03032f
com.example.myapplication:attr/startIconTint = 0x7f030389
com.example.myapplication:dimen/mtrl_calendar_navigation_top_padding = 0x7f0601a8
com.example.myapplication:attr/subMenuArrow = 0x7f030396
com.example.myapplication:attr/maxLines = 0x7f0302bc
com.example.myapplication:attr/strokeWidth = 0x7f030395
com.example.myapplication:attr/statusBarScrim = 0x7f030393
com.example.myapplication:attr/statusBarForeground = 0x7f030392
com.example.myapplication:layout/abc_action_bar_up_container = 0x7f0b0001
com.example.myapplication:integer/cancel_button_image_alpha = 0x7f090004
com.example.myapplication:attr/state_dragged = 0x7f03038e
com.example.myapplication:style/Base.Widget.AppCompat.Light.PopupMenu.Overflow = 0x7f1100d9
com.example.myapplication:attr/chipStandaloneStyle = 0x7f0300b6
com.example.myapplication:attr/arrowShaftLength = 0x7f03003b
com.example.myapplication:attr/state_above_anchor = 0x7f03038b
com.example.myapplication:style/MaterialAlertDialog.MaterialComponents.Title.Text = 0x7f110126
com.example.myapplication:attr/splitMinWidth = 0x7f03037a
com.example.myapplication:animator/mtrl_extended_fab_hide_motion_spec = 0x7f020019
com.example.myapplication:dimen/mtrl_snackbar_background_corner_radius = 0x7f06020e
com.example.myapplication:attr/spinnerStyle = 0x7f030377
com.example.myapplication:dimen/mtrl_alert_dialog_background_inset_bottom = 0x7f060161
com.example.myapplication:attr/spinBars = 0x7f030375
com.example.myapplication:style/ThemeOverlay.Material3.MaterialTimePicker = 0x7f11026f
com.example.myapplication:id/SYM = 0x7f08000c
com.example.myapplication:attr/snackbarStyle = 0x7f030372
com.example.myapplication:attr/snackbarButtonStyle = 0x7f030371
com.example.myapplication:styleable/SearchView = 0x7f120083
com.example.myapplication:dimen/m3_sys_elevation_level4 = 0x7f060113
com.example.myapplication:layout/material_chip_input_combo = 0x7f0b0035
com.example.myapplication:dimen/abc_action_bar_stacked_max_height = 0x7f060009
com.example.myapplication:attr/singleChoiceItemLayout = 0x7f03036c
com.example.myapplication:attr/endIconTintMode = 0x7f03017a
com.example.myapplication:attr/shrinkMotionSpec = 0x7f03036b
com.example.myapplication:style/Theme.MaterialComponents.Light.Dialog.Alert = 0x7f110230
com.example.myapplication:id/decelerate = 0x7f08009c
com.example.myapplication:dimen/disabled_alpha_material_light = 0x7f060091
com.example.myapplication:attr/flow_horizontalGap = 0x7f0301b8
com.example.myapplication:attr/showTitle = 0x7f03036a
com.example.myapplication:color/design_box_stroke_color = 0x7f050030
com.example.myapplication:layout/text_view_with_theme_line_height = 0x7f0b0077
com.example.myapplication:attr/showPaths = 0x7f030368
com.example.myapplication:attr/showMotionSpec = 0x7f030367
com.example.myapplication:attr/actionBarItemBackground = 0x7f030004
com.example.myapplication:attr/boxCornerRadiusTopEnd = 0x7f030074
com.example.myapplication:attr/showDividers = 0x7f030366
com.example.myapplication:dimen/abc_text_size_small_material = 0x7f06004c
com.example.myapplication:attr/showAsAction = 0x7f030364
com.example.myapplication:attr/shapeAppearanceSmallComponent = 0x7f030361
com.example.myapplication:dimen/tooltip_margin = 0x7f060243
com.example.myapplication:color/m3_elevated_chip_background_color = 0x7f050082
com.example.myapplication:xml/standalone_badge_gravity_bottom_end = 0x7f130003
com.example.myapplication:attr/startIconDrawable = 0x7f030388
com.example.myapplication:attr/flow_firstHorizontalStyle = 0x7f0301b3
com.example.myapplication:color/material_dynamic_neutral_variant99 = 0x7f0501ba
com.example.myapplication:bool/m3_sys_typescale_display_medium_text_all_caps = 0x7f040006
com.example.myapplication:attr/exitAnim = 0x7f030186
com.example.myapplication:color/m3_sys_color_light_tertiary_container = 0x7f050184
com.example.myapplication:attr/shapeAppearanceMediumComponent = 0x7f03035f
com.example.myapplication:color/m3_sys_color_light_on_primary_container = 0x7f050175
com.example.myapplication:attr/shapeAppearanceLargeComponent = 0x7f03035e
com.example.myapplication:attr/drawPath = 0x7f03015b
com.example.myapplication:attr/blendSrc = 0x7f030065
com.example.myapplication:attr/selectorSize = 0x7f03035b
com.example.myapplication:dimen/abc_text_size_body_1_material = 0x7f06003f
com.example.myapplication:id/useLogo = 0x7f0801f5
com.example.myapplication:id/SHOW_PROGRESS = 0x7f08000b
com.example.myapplication:style/Widget.Design.Snackbar = 0x7f1102f5
com.example.myapplication:attr/selectionRequired = 0x7f03035a
com.example.myapplication:attr/deltaPolarAngle = 0x7f030148
com.example.myapplication:color/mtrl_fab_bg_color_selector = 0x7f050212
com.example.myapplication:attr/marginHorizontal = 0x7f030290
com.example.myapplication:style/Theme.AppCompat = 0x7f1101d8
com.example.myapplication:color/m3_ref_palette_dynamic_neutral30 = 0x7f050092
com.example.myapplication:attr/selectableItemBackgroundBorderless = 0x7f030359
com.example.myapplication:dimen/material_cursor_inset_bottom = 0x7f060147
com.example.myapplication:styleable/MaterialButton = 0x7f120054
com.example.myapplication:attr/selectableItemBackground = 0x7f030358
com.example.myapplication:id/tv_detail_title = 0x7f0801ec
com.example.myapplication:attr/editTextBackground = 0x7f03016c
com.example.myapplication:color/m3_dynamic_dark_default_color_primary_text = 0x7f050078
com.example.myapplication:drawable/ic_mtrl_chip_close_circle = 0x7f07006f
com.example.myapplication:attr/scrimBackground = 0x7f030350
com.example.myapplication:attr/saturation = 0x7f03034d
com.example.myapplication:drawable/abc_ic_search_api_material = 0x7f070027
com.example.myapplication:attr/ifTagNotSet = 0x7f0301f7
com.example.myapplication:color/teal_700 = 0x7f05024c
com.example.myapplication:attr/setsTag = 0x7f03035c
com.example.myapplication:style/Widget.Material3.Slider = 0x7f11036a
com.example.myapplication:attr/windowActionBar = 0x7f030461
com.example.myapplication:integer/m3_chip_anim_duration = 0x7f09000e
com.example.myapplication:id/search_go_btn = 0x7f080184
com.example.myapplication:attr/itemStrokeWidth = 0x7f030221
com.example.myapplication:dimen/mtrl_navigation_rail_active_text_size = 0x7f0601e9
com.example.myapplication:attr/rippleColor = 0x7f030348
com.example.myapplication:attr/reverseLayout = 0x7f030347
com.example.myapplication:style/TextAppearance.AppCompat.Caption = 0x7f110176
com.example.myapplication:attr/region_heightMoreThan = 0x7f030343
com.example.myapplication:color/m3_ref_palette_dynamic_neutral_variant20 = 0x7f05009e
com.example.myapplication:attr/region_heightLessThan = 0x7f030342
com.example.myapplication:id/visible_removing_fragment_view_tag = 0x7f0801ff
com.example.myapplication:attr/textAppearanceBody1 = 0x7f0303cb
com.example.myapplication:color/design_dark_default_color_on_error = 0x7f050034
com.example.myapplication:id/x_right = 0x7f080207
com.example.myapplication:style/Base.V21.Theme.AppCompat = 0x7f110097
com.example.myapplication:id/mtrl_calendar_frame = 0x7f080125
com.example.myapplication:string/nav_profile = 0x7f10008c
com.example.myapplication:color/abc_primary_text_disable_only_material_light = 0x7f05000a
com.example.myapplication:attr/reactiveGuide_applyToConstraintSet = 0x7f03033f
com.example.myapplication:attr/tabIndicatorAnimationMode = 0x7f0303b0
com.example.myapplication:attr/reactiveGuide_animateChange = 0x7f03033d
com.example.myapplication:attr/drawableTint = 0x7f030162
com.example.myapplication:attr/ratingBarStyleSmall = 0x7f03033c
com.example.myapplication:styleable/StateListDrawable = 0x7f12008e
com.example.myapplication:style/Base.Widget.Material3.CollapsingToolbar = 0x7f1100f6
com.example.myapplication:attr/rangeFillColor = 0x7f030339
com.example.myapplication:attr/expandedTitleMarginEnd = 0x7f03018d
com.example.myapplication:style/Widget.Material3.MaterialCalendar.Fullscreen = 0x7f110349
com.example.myapplication:attr/navigationRailStyle = 0x7f0302f2
com.example.myapplication:attr/radioButtonStyle = 0x7f030338
com.example.myapplication:color/m3_ref_palette_dynamic_primary0 = 0x7f0500a8
com.example.myapplication:attr/textColorAlertDialogListItem = 0x7f0303f5
com.example.myapplication:attr/queryBackground = 0x7f030335
com.example.myapplication:id/nav_host_fragment_content_main = 0x7f08013e
com.example.myapplication:attr/quantizeMotionSteps = 0x7f030334
com.example.myapplication:color/mtrl_btn_text_btn_ripple_color = 0x7f050202
com.example.myapplication:color/abc_tint_default = 0x7f050014
com.example.myapplication:attr/progressBarStyle = 0x7f030331
com.example.myapplication:style/Widget.MaterialComponents.MaterialCalendar.Day.Invalid = 0x7f1103b8
com.example.myapplication:attr/progressBarPadding = 0x7f030330
com.example.myapplication:color/m3_ref_palette_neutral_variant10 = 0x7f0500ea
com.example.myapplication:attr/preserveIconSpacing = 0x7f03032d
com.example.myapplication:style/TextAppearance.Material3.LabelLarge = 0x7f1101bd
com.example.myapplication:id/spring = 0x7f0801a3
com.example.myapplication:attr/errorContentDescription = 0x7f03017f
com.example.myapplication:attr/popupWindowStyle = 0x7f030329
com.example.myapplication:color/switch_thumb_normal_material_dark = 0x7f050249
com.example.myapplication:attr/popupMenuStyle = 0x7f030327
com.example.myapplication:dimen/compat_notification_large_icon_max_height = 0x7f06005c
com.example.myapplication:attr/cornerFamilyBottomRight = 0x7f030122
com.example.myapplication:animator/m3_card_state_list_anim = 0x7f020010
com.example.myapplication:attr/popupMenuBackground = 0x7f030326
com.example.myapplication:attr/layout_constraintDimensionRatio = 0x7f030245
com.example.myapplication:dimen/mtrl_badge_long_text_horizontal_padding = 0x7f060167
com.example.myapplication:attr/popUpToInclusive = 0x7f030324
com.example.myapplication:style/Widget.AppCompat.Light.PopupMenu = 0x7f1102cf
com.example.myapplication:attr/mimeType = 0x7f0302c3
com.example.myapplication:id/nav_calendar = 0x7f08013a
com.example.myapplication:attr/tabRippleColor = 0x7f0303be
com.example.myapplication:style/ThemeOverlay.MaterialComponents.TimePicker = 0x7f11029c
com.example.myapplication:attr/placeholderTextColor = 0x7f03031e
com.example.myapplication:attr/actionBarTheme = 0x7f03000c
com.example.myapplication:style/Widget.MaterialComponents.MaterialCalendar.MonthTextView = 0x7f1103c8
com.example.myapplication:attr/liftOnScrollTargetViewId = 0x7f03027a
com.example.myapplication:attr/polarRelativeTo = 0x7f030320
com.example.myapplication:style/Widget.MaterialComponents.TimePicker.Clock = 0x7f1103f0
com.example.myapplication:drawable/abc_control_background_material = 0x7f070019
com.example.myapplication:styleable/MockView = 0x7f120064
com.example.myapplication:attr/colorOnBackground = 0x7f0300e7
com.example.myapplication:anim/abc_popup_exit = 0x7f010004
com.example.myapplication:attr/placeholderTextAppearance = 0x7f03031d
com.example.myapplication:attr/ratingBarStyle = 0x7f03033a
com.example.myapplication:style/ThemeOverlay.Material3.FloatingActionButton.Surface = 0x7f110266
com.example.myapplication:attr/layout_constraintHeight_max = 0x7f03024d
com.example.myapplication:attr/percentWidth = 0x7f030316
com.example.myapplication:drawable/abc_text_select_handle_middle_mtrl = 0x7f07004e
com.example.myapplication:attr/passwordToggleTintMode = 0x7f030312
com.example.myapplication:attr/passwordToggleDrawable = 0x7f03030f
com.example.myapplication:color/m3_ref_palette_dynamic_primary60 = 0x7f0500af
com.example.myapplication:string/abc_toolbar_collapse_description = 0x7f10001a
com.example.myapplication:attr/percentX = 0x7f030317
com.example.myapplication:color/design_dark_default_color_error = 0x7f050032
com.example.myapplication:attr/panelMenuListTheme = 0x7f03030c
com.example.myapplication:dimen/abc_list_item_height_large_material = 0x7f060030
com.example.myapplication:color/m3_selection_control_ripple_color_selector = 0x7f05011e
com.example.myapplication:attr/yearStyle = 0x7f03046c
com.example.myapplication:attr/layout_constraintGuide_end = 0x7f030249
com.example.myapplication:attr/layout_constraintVertical_bias = 0x7f03025f
com.example.myapplication:attr/textPanY = 0x7f030405
com.example.myapplication:drawable/mtrl_ic_arrow_drop_up = 0x7f070085
com.example.myapplication:color/m3_sys_color_dark_on_surface_variant = 0x7f050133
com.example.myapplication:dimen/m3_extended_fab_end_padding = 0x7f0600ea
com.example.myapplication:attr/textOutlineColor = 0x7f030402
com.example.myapplication:drawable/abc_spinner_textfield_background_material = 0x7f070045
com.example.myapplication:dimen/mtrl_alert_dialog_background_inset_top = 0x7f060164
com.example.myapplication:color/dim_foreground_disabled_material_light = 0x7f050056
com.example.myapplication:styleable/MotionEffect = 0x7f120066
com.example.myapplication:attr/popUpToSaveState = 0x7f030325
com.example.myapplication:dimen/notification_right_side_padding_top = 0x7f06022d
com.example.myapplication:attr/paddingBottomNoButtons = 0x7f030303
com.example.myapplication:color/m3_ref_palette_dynamic_tertiary40 = 0x7f0500c7
com.example.myapplication:attr/overlay = 0x7f030302
com.example.myapplication:styleable/Navigator = 0x7f120077
com.example.myapplication:style/TextAppearance.Material3.TitleSmall = 0x7f1101c3
com.example.myapplication:attr/overlapAnchor = 0x7f030301
com.example.myapplication:dimen/m3_sys_typescale_title_small_letter_spacing = 0x7f060138
com.example.myapplication:color/m3_ref_palette_tertiary20 = 0x7f050113
com.example.myapplication:drawable/abc_ic_commit_search_api_mtrl_alpha = 0x7f07001f
com.example.myapplication:color/teal_200 = 0x7f05024b
com.example.myapplication:attr/transitionEasing = 0x7f030444
com.example.myapplication:style/Widget.MaterialComponents.CompoundButton.CheckBox = 0x7f1103ad
com.example.myapplication:string/abc_action_menu_overflow_description = 0x7f100002
com.example.myapplication:attr/extendedFloatingActionButtonSurfaceStyle = 0x7f030196
com.example.myapplication:attr/autoCompleteTextViewStyle = 0x7f03003e
com.example.myapplication:attr/layout_constraintHorizontal_bias = 0x7f030250
com.example.myapplication:attr/flow_lastVerticalBias = 0x7f0301bc
com.example.myapplication:attr/onPositiveCross = 0x7f0302fd
com.example.myapplication:attr/telltales_velocityMode = 0x7f0303c9
com.example.myapplication:style/Widget.MaterialComponents.AutoCompleteTextView.OutlinedBox.Dense = 0x7f11038b
com.example.myapplication:attr/onNegativeCross = 0x7f0302fc
com.example.myapplication:id/north = 0x7f08014f
com.example.myapplication:color/primary_text_disabled_material_dark = 0x7f050239
com.example.myapplication:attr/hoveredFocusedTranslationZ = 0x7f0301ed
com.example.myapplication:attr/cornerSizeBottomLeft = 0x7f030127
com.example.myapplication:color/abc_search_url_text_selected = 0x7f050010
com.example.myapplication:attr/nestedScrollViewStyle = 0x7f0302f5
com.example.myapplication:id/snackbar_text = 0x7f080199
com.example.myapplication:attr/nestedScrollFlags = 0x7f0302f4
com.example.myapplication:dimen/mtrl_calendar_content_padding = 0x7f060190
com.example.myapplication:attr/navigationIcon = 0x7f0302ef
com.example.myapplication:attr/iconEndPadding = 0x7f0301ef
com.example.myapplication:attr/imageButtonStyle = 0x7f0301f9
com.example.myapplication:anim/btn_radio_to_on_mtrl_dot_group_animation = 0x7f010015
com.example.myapplication:attr/navGraph = 0x7f0302ed
com.example.myapplication:color/m3_ref_palette_dynamic_neutral95 = 0x7f050099
com.example.myapplication:id/middle = 0x7f080119
com.example.myapplication:id/dragRight = 0x7f0800b4
com.example.myapplication:attr/chipIconVisible = 0x7f0300b0
com.example.myapplication:attr/multiChoiceItemLayout = 0x7f0302ec
com.example.myapplication:attr/moveWhenScrollAtTop = 0x7f0302eb
com.example.myapplication:attr/tickMarkTintMode = 0x7f03041b
com.example.myapplication:attr/motion_triggerOnCollision = 0x7f0302ea
com.example.myapplication:style/Base.Theme.MaterialComponents.Light.Bridge = 0x7f11006a
com.example.myapplication:attr/layout_insetEdge = 0x7f030271
com.example.myapplication:dimen/abc_floating_window_z = 0x7f06002f
com.example.myapplication:attr/motion_postLayoutCollision = 0x7f0302e9
com.example.myapplication:attr/motionTarget = 0x7f0302e8
com.example.myapplication:attr/motionStagger = 0x7f0302e7
com.example.myapplication:attr/layout_goneMarginLeft = 0x7f03026d
com.example.myapplication:dimen/m3_bottom_nav_item_padding_bottom = 0x7f0600b8
com.example.myapplication:attr/endIconCheckable = 0x7f030175
com.example.myapplication:attr/motionPath = 0x7f0302e4
com.example.myapplication:style/Base.TextAppearance.AppCompat.Widget.TextView.SpinnerItem = 0x7f11003f
com.example.myapplication:attr/motionInterpolator = 0x7f0302e3
com.example.myapplication:color/m3_ref_palette_error20 = 0x7f0500d2
com.example.myapplication:dimen/mtrl_extended_fab_start_padding_icon = 0x7f0601ce
com.example.myapplication:attr/motionEffect_viewTransition = 0x7f0302e2
com.example.myapplication:dimen/mtrl_calendar_header_content_padding = 0x7f060199
com.example.myapplication:attr/motionEffect_translationY = 0x7f0302e1
com.example.myapplication:attr/motionEffect_strict = 0x7f0302df
com.example.myapplication:attr/itemIconTint = 0x7f030211
com.example.myapplication:drawable/abc_text_select_handle_right_mtrl = 0x7f07004f
com.example.myapplication:color/m3_ref_palette_secondary80 = 0x7f05010c
com.example.myapplication:color/m3_ref_palette_primary40 = 0x7f0500fb
com.example.myapplication:id/save_overlay_view = 0x7f080176
com.example.myapplication:string/m3_sys_motion_easing_accelerated = 0x7f10003d
com.example.myapplication:attr/chipIconEnabled = 0x7f0300ad
com.example.myapplication:attr/motionEffect_end = 0x7f0302dc
com.example.myapplication:attr/errorEnabled = 0x7f030180
com.example.myapplication:attr/motionEasingStandard = 0x7f0302da
com.example.myapplication:id/edit_query = 0x7f0800bc
com.example.myapplication:color/m3_selection_control_button_tint = 0x7f05011d
com.example.myapplication:attr/titleCollapseMode = 0x7f030421
com.example.myapplication:string/material_minute_suffix = 0x7f100056
com.example.myapplication:dimen/mtrl_snackbar_padding_horizontal = 0x7f060212
com.example.myapplication:integer/app_bar_elevation_anim_duration = 0x7f090002
com.example.myapplication:dimen/design_fab_elevation = 0x7f060071
com.example.myapplication:attr/motionEasingEmphasized = 0x7f0302d8
com.example.myapplication:attr/dropdownListPreferredItemHeight = 0x7f030169
com.example.myapplication:color/m3_sys_color_light_on_error_container = 0x7f050173
com.example.myapplication:attr/motionEasingDecelerated = 0x7f0302d7
com.example.myapplication:integer/material_motion_duration_long_2 = 0x7f09001a
com.example.myapplication:attr/circleRadius = 0x7f0300bc
com.example.myapplication:attr/motionDurationShort2 = 0x7f0302d5
com.example.myapplication:anim/abc_shrink_fade_out_from_bottom = 0x7f010005
com.example.myapplication:dimen/compat_control_corner_material = 0x7f06005b
com.example.myapplication:color/switch_thumb_material_dark = 0x7f050247
com.example.myapplication:attr/itemShapeAppearance = 0x7f030218
com.example.myapplication:style/Base.Animation.AppCompat.DropDownUp = 0x7f11000b
com.example.myapplication:attr/motionDebug = 0x7f0302cf
com.example.myapplication:dimen/m3_sys_state_focus_state_layer_opacity = 0x7f060119
com.example.myapplication:color/m3_sys_color_light_inverse_surface = 0x7f050170
com.example.myapplication:id/ltr = 0x7f080101
com.example.myapplication:attr/actionBarPopupTheme = 0x7f030005
com.example.myapplication:attr/mock_showLabel = 0x7f0302ce
com.example.myapplication:id/text_input_error_icon = 0x7f0801ce
com.example.myapplication:attr/mock_labelColor = 0x7f0302cc
com.example.myapplication:layout/design_bottom_sheet_dialog = 0x7f0b0020
com.example.myapplication:dimen/m3_datepicker_elevation = 0x7f0600e7
com.example.myapplication:attr/textAppearanceBodyMedium = 0x7f0303ce
com.example.myapplication:attr/layout_goneMarginStart = 0x7f03026f
com.example.myapplication:layout/abc_popup_menu_header_item_layout = 0x7f0b0012
com.example.myapplication:attr/hintAnimationEnabled = 0x7f0301e5
com.example.myapplication:attr/firstBaselineToTopHeight = 0x7f0301a7
com.example.myapplication:color/material_on_surface_stroke = 0x7f0501f2
com.example.myapplication:attr/measureWithLargestChild = 0x7f0302bf
com.example.myapplication:style/ShapeAppearance.Material3.LargeComponent = 0x7f110147
com.example.myapplication:color/material_on_background_emphasis_medium = 0x7f0501eb
com.example.myapplication:color/m3_sys_color_dynamic_dark_on_primary_container = 0x7f050145
com.example.myapplication:attr/counterTextColor = 0x7f030130
com.example.myapplication:color/m3_ref_palette_secondary90 = 0x7f05010d
com.example.myapplication:attr/shortcutMatchRequired = 0x7f030362
com.example.myapplication:attr/materialTimePickerTitleStyle = 0x7f0302b5
com.example.myapplication:styleable/AppBarLayoutStates = 0x7f12000e
com.example.myapplication:style/Base.Widget.AppCompat.ActivityChooserView = 0x7f1100c0
com.example.myapplication:id/all = 0x7f08004e
com.example.myapplication:dimen/mtrl_snackbar_action_text_color_alpha = 0x7f06020d
com.example.myapplication:style/Base.V21.ThemeOverlay.Material3.BottomSheetDialog = 0x7f1100a0
com.example.myapplication:attr/materialTimePickerTheme = 0x7f0302b4
com.example.myapplication:style/Theme.MaterialComponents.DayNight.DarkActionBar.Bridge = 0x7f110214
com.example.myapplication:dimen/mtrl_btn_text_btn_icon_padding = 0x7f060187
com.example.myapplication:attr/boxStrokeWidthFocused = 0x7f030079
com.example.myapplication:attr/duration = 0x7f03016a
com.example.myapplication:attr/horizontalOffsetWithText = 0x7f0301ec
com.example.myapplication:attr/badgeWithTextRadius = 0x7f030055
com.example.myapplication:attr/closeIconEndPadding = 0x7f0300cc
com.example.myapplication:attr/materialThemeOverlay = 0x7f0302b2
com.example.myapplication:id/titleDividerNoCustom = 0x7f0801d9
com.example.myapplication:attr/checkboxStyle = 0x7f03009e
com.example.myapplication:dimen/mtrl_extended_fab_translation_z_base = 0x7f0601d0
com.example.myapplication:color/m3_ref_palette_dynamic_tertiary20 = 0x7f0500c5
com.example.myapplication:styleable/Insets = 0x7f120043
com.example.myapplication:id/accessibility_custom_action_14 = 0x7f080018
com.example.myapplication:attr/materialCircleRadius = 0x7f0302ad
com.example.myapplication:id/accessibility_custom_action_15 = 0x7f080019
com.example.myapplication:string/m3_sys_typescale_headline_medium_font = 0x7f100049
com.example.myapplication:color/m3_ref_palette_dynamic_secondary60 = 0x7f0500bc
com.example.myapplication:id/wrap_content_constrained = 0x7f080205
com.example.myapplication:attr/materialCardViewOutlinedStyle = 0x7f0302ab
com.example.myapplication:attr/startIconCheckable = 0x7f030386
com.example.myapplication:id/textinput_error = 0x7f0801d1
com.example.myapplication:attr/materialCalendarStyle = 0x7f0302a6
com.example.myapplication:attr/materialCalendarTheme = 0x7f0302a7
com.example.myapplication:color/m3_sys_color_dynamic_dark_on_primary = 0x7f050144
com.example.myapplication:integer/mtrl_btn_anim_duration_ms = 0x7f090022
com.example.myapplication:style/Widget.Material3.BottomNavigationView = 0x7f110302
com.example.myapplication:attr/textAppearanceSearchResultSubtitle = 0x7f0303e8
com.example.myapplication:dimen/abc_text_size_title_material = 0x7f06004f
com.example.myapplication:attr/layout_goneMarginBottom = 0x7f03026b
com.example.myapplication:attr/materialCalendarFullscreenTheme = 0x7f03029c
com.example.myapplication:color/m3_ref_palette_dynamic_neutral_variant99 = 0x7f0500a7
com.example.myapplication:attr/materialAlertDialogTitleTextStyle = 0x7f030296
com.example.myapplication:color/m3_chip_stroke_color = 0x7f05006f
com.example.myapplication:color/primary_dark_material_dark = 0x7f050233
com.example.myapplication:attr/boxStrokeColor = 0x7f030076
com.example.myapplication:color/mtrl_filled_stroke_color = 0x7f050217
com.example.myapplication:attr/content = 0x7f03010d
com.example.myapplication:color/abc_primary_text_material_dark = 0x7f05000b
com.example.myapplication:attr/launchSingleTop = 0x7f03022f
com.example.myapplication:attr/materialAlertDialogTitlePanelStyle = 0x7f030295
com.example.myapplication:dimen/m3_large_fab_max_image_size = 0x7f0600f3
com.example.myapplication:bool/m3_sys_typescale_body_large_text_all_caps = 0x7f040002
com.example.myapplication:attr/collapsedSize = 0x7f0300d4
com.example.myapplication:style/ThemeOverlay.MaterialComponents.AutoCompleteTextView.OutlinedBox = 0x7f110281
com.example.myapplication:attr/materialAlertDialogTheme = 0x7f030293
com.example.myapplication:attr/layout_constraintTag = 0x7f03025b
com.example.myapplication:attr/floatingActionButtonPrimaryStyle = 0x7f0301ad
com.example.myapplication:attr/logo = 0x7f03028e
com.example.myapplication:dimen/abc_progress_bar_height_material = 0x7f060035
com.example.myapplication:color/m3_ref_palette_neutral80 = 0x7f0500e5
com.example.myapplication:style/Widget.MaterialComponents.MaterialCalendar.Fullscreen = 0x7f1103bd
com.example.myapplication:attr/layout_constraintCircleAngle = 0x7f030243
com.example.myapplication:attr/listPreferredItemPaddingStart = 0x7f03028d
com.example.myapplication:dimen/mtrl_extended_fab_translation_z_hovered_focused = 0x7f0601d1
com.example.myapplication:attr/listPreferredItemPaddingLeft = 0x7f03028b
com.example.myapplication:attr/autoSizeMinTextSize = 0x7f030040
com.example.myapplication:color/mtrl_text_btn_text_color_selector = 0x7f05022b
com.example.myapplication:id/material_clock_period_am_button = 0x7f080108
com.example.myapplication:attr/bottomInsetScrimEnabled = 0x7f03006b
com.example.myapplication:attr/viewTransitionOnNegativeCross = 0x7f030456
com.example.myapplication:attr/listPreferredItemHeightSmall = 0x7f030289
com.example.myapplication:color/material_dynamic_tertiary80 = 0x7f0501de
com.example.myapplication:attr/number = 0x7f0302f8
com.example.myapplication:attr/listLayout = 0x7f030284
com.example.myapplication:id/blocking = 0x7f080062
com.example.myapplication:style/Base.Widget.AppCompat.PopupMenu = 0x7f1100df
com.example.myapplication:dimen/mtrl_calendar_year_width = 0x7f0601b5
com.example.myapplication:color/m3_ref_palette_dynamic_secondary99 = 0x7f0500c1
com.example.myapplication:style/Theme.MaterialComponents.CompactMenu = 0x7f11020f
com.example.myapplication:attr/listChoiceIndicatorSingleAnimated = 0x7f030281
com.example.myapplication:dimen/m3_sys_typescale_title_small_text_size = 0x7f060139
com.example.myapplication:dimen/abc_alert_dialog_button_bar_height = 0x7f060010
com.example.myapplication:attr/trackColorInactive = 0x7f03043c
com.example.myapplication:dimen/tooltip_precise_anchor_threshold = 0x7f060245
com.example.myapplication:attr/lineHeight = 0x7f03027c
com.example.myapplication:drawable/abc_scrubber_track_mtrl_alpha = 0x7f070040
com.example.myapplication:string/mtrl_picker_range_header_selected = 0x7f10007a
com.example.myapplication:color/m3_dynamic_dark_hint_foreground = 0x7f05007b
com.example.myapplication:attr/icon = 0x7f0301ee
com.example.myapplication:attr/layout_wrapBehaviorInParent = 0x7f030278
com.example.myapplication:attr/layout_scrollEffect = 0x7f030275
com.example.myapplication:attr/layout_constraintHorizontal_chainStyle = 0x7f030251
com.example.myapplication:color/m3_button_ripple_color_selector = 0x7f050066
com.example.myapplication:style/Test.Theme.MaterialComponents.MaterialCalendar = 0x7f110168
com.example.myapplication:attr/layout_goneMarginTop = 0x7f030270
com.example.myapplication:attr/contentPaddingEnd = 0x7f030117
com.example.myapplication:attr/cornerSizeTopRight = 0x7f03012a
com.example.myapplication:string/material_minute_selection = 0x7f100055
com.example.myapplication:dimen/m3_alert_dialog_title_bottom_margin = 0x7f0600a6
com.example.myapplication:attr/minWidth = 0x7f0302c8
com.example.myapplication:attr/colorOnSecondaryContainer = 0x7f0300ef
com.example.myapplication:attr/layout_goneMarginRight = 0x7f03026e
com.example.myapplication:id/parentRelative = 0x7f08015d
com.example.myapplication:style/TextAppearance.Design.Suffix = 0x7f1101b0
com.example.myapplication:attr/layout_constraintTop_creator = 0x7f03025c
com.example.myapplication:color/m3_sys_color_dark_tertiary_container = 0x7f05013e
com.example.myapplication:attr/layout_constraintWidth_max = 0x7f030264
com.example.myapplication:style/Widget.MaterialComponents.TextView = 0x7f1103ed
com.example.myapplication:attr/iconifiedByDefault = 0x7f0301f6
com.example.myapplication:attr/drawableTopCompat = 0x7f030164
com.example.myapplication:attr/showDelay = 0x7f030365
com.example.myapplication:attr/buttonBarNeutralButtonStyle = 0x7f03007d
com.example.myapplication:attr/carousel_firstView = 0x7f030092
com.example.myapplication:attr/layout_goneMarginEnd = 0x7f03026c
com.example.myapplication:attr/listMenuViewStyle = 0x7f030285
com.example.myapplication:id/floating = 0x7f0800cf
com.example.myapplication:dimen/abc_dialog_min_width_minor = 0x7f060023
com.example.myapplication:attr/layout_constraintVertical_chainStyle = 0x7f030260
com.example.myapplication:dimen/m3_chip_disabled_translation_z = 0x7f0600e2
com.example.myapplication:animator/linear_indeterminate_line1_tail_interpolator = 0x7f02000a
com.example.myapplication:attr/closeIconVisible = 0x7f0300d0
com.example.myapplication:attr/splitRatio = 0x7f03037b
com.example.myapplication:style/ThemeOverlay.Material3.AutoCompleteTextView.OutlinedBox.Dense = 0x7f110251
com.example.myapplication:attr/lastBaselineToBottomHeight = 0x7f03022e
com.example.myapplication:styleable/AnimatedStateListDrawableItem = 0x7f12000b
com.example.myapplication:attr/layout_constraintStart_toStartOf = 0x7f03025a
com.example.myapplication:drawable/abc_ic_go_search_api_material = 0x7f070020
com.example.myapplication:styleable/MaterialCalendarItem = 0x7f120057
com.example.myapplication:anim/design_bottom_sheet_slide_out = 0x7f010019
com.example.myapplication:attr/layout_constraintHeight_default = 0x7f03024c
com.example.myapplication:style/Theme.MaterialComponents.Light.LargeTouch = 0x7f110238
com.example.myapplication:anim/btn_checkbox_to_checked_box_outer_merged_animation = 0x7f01000d
com.example.myapplication:integer/material_motion_duration_medium_2 = 0x7f09001c
com.example.myapplication:style/TextAppearance.Material3.DisplaySmall = 0x7f1101b9
com.example.myapplication:attr/layout_constraintRight_creator = 0x7f030256
com.example.myapplication:attr/layout_constraintHorizontal_weight = 0x7f030252
com.example.myapplication:dimen/mtrl_slider_label_padding = 0x7f060204
com.example.myapplication:attr/behavior_saveFlags = 0x7f030063
com.example.myapplication:attr/layout_constraintHeight_percent = 0x7f03024f
com.example.myapplication:style/Theme.AppCompat.NoActionBar = 0x7f1101ed
com.example.myapplication:attr/listPopupWindowStyle = 0x7f030286
com.example.myapplication:style/Base.V23.Theme.AppCompat.Light = 0x7f1100a5
com.example.myapplication:dimen/m3_extended_fab_start_padding = 0x7f0600ed
com.example.myapplication:bool/abc_config_actionMenuItemAllCaps = 0x7f040001
com.example.myapplication:color/material_blue_grey_900 = 0x7f05019b
com.example.myapplication:attr/fontFamily = 0x7f0301c6
com.example.myapplication:layout/text_view_with_line_height_from_style = 0x7f0b0076
com.example.myapplication:dimen/material_emphasis_disabled_background = 0x7f06014c
com.example.myapplication:attr/layout_constraintRight_toRightOf = 0x7f030258
com.example.myapplication:id/test_radiobutton_android_button_tint = 0x7f0801c4
com.example.myapplication:string/material_motion_easing_standard = 0x7f10005b
com.example.myapplication:attr/action = 0x7f030002
com.example.myapplication:attr/carousel_backwardTransition = 0x7f030090
com.example.myapplication:style/Widget.Design.BottomSheet.Modal = 0x7f1102f0
com.example.myapplication:layout/abc_list_menu_item_icon = 0x7f0b000f
com.example.myapplication:attr/itemHorizontalPadding = 0x7f03020d
com.example.myapplication:id/tv_detail_status = 0x7f0801eb
com.example.myapplication:color/m3_ref_palette_neutral100 = 0x7f0500de
com.example.myapplication:color/mtrl_choice_chip_background_color = 0x7f05020e
com.example.myapplication:attr/layout_constraintHeight = 0x7f03024b
com.example.myapplication:attr/barLength = 0x7f030056
com.example.myapplication:attr/textStartPadding = 0x7f030406
com.example.myapplication:dimen/abc_dialog_list_padding_top_no_title = 0x7f060021
com.example.myapplication:attr/expandedTitleTextAppearance = 0x7f030190
com.example.myapplication:attr/drawableStartCompat = 0x7f030161
com.example.myapplication:attr/layout_constraintCircleRadius = 0x7f030244
com.example.myapplication:style/Widget.MaterialComponents.TimePicker.Display.HelperText = 0x7f1103f3
com.example.myapplication:anim/btn_checkbox_to_unchecked_box_inner_merged_animation = 0x7f01000f
com.example.myapplication:dimen/mtrl_snackbar_message_margin_horizontal = 0x7f060211
com.example.myapplication:attr/layout_constraintBottom_toBottomOf = 0x7f030240
com.example.myapplication:attr/actionMenuTextAppearance = 0x7f030011
com.example.myapplication:integer/m3_sys_shape_large_corner_family = 0x7f090016
com.example.myapplication:attr/imagePanX = 0x7f0301fa
com.example.myapplication:attr/layout_constraintBaseline_toTopOf = 0x7f03023e
com.example.myapplication:attr/cardForegroundColor = 0x7f03008b
com.example.myapplication:attr/layout_constraintBaseline_toBottomOf = 0x7f03023d
com.example.myapplication:attr/bottomAppBarStyle = 0x7f03006a
com.example.myapplication:attr/useMaterialThemeColors = 0x7f03044f
com.example.myapplication:attr/layout_constraintBaseline_toBaselineOf = 0x7f03023c
com.example.myapplication:dimen/m3_navigation_item_shape_inset_end = 0x7f0600fa
com.example.myapplication:style/RtlOverlay.Widget.AppCompat.PopupMenuItem.InternalGroup = 0x7f11013a
com.example.myapplication:attr/listPreferredItemHeightLarge = 0x7f030288
com.example.myapplication:animator/linear_indeterminate_line1_head_interpolator = 0x7f020009
com.example.myapplication:style/Widget.Material3.Chip.Assist = 0x7f11031a
com.example.myapplication:dimen/mtrl_snackbar_background_overlay_color_alpha = 0x7f06020f
com.example.myapplication:attr/layout_anchorGravity = 0x7f030235
com.example.myapplication:drawable/abc_popup_background_mtrl_mult = 0x7f070038
com.example.myapplication:attr/shapeAppearance = 0x7f03035d
com.example.myapplication:style/Widget.AppCompat.ProgressBar = 0x7f1102db
com.example.myapplication:style/Widget.AppCompat.ImageButton = 0x7f1102bc
com.example.myapplication:attr/layout_anchor = 0x7f030234
com.example.myapplication:style/Widget.AppCompat.ActionBar.Solid = 0x7f1102a4
com.example.myapplication:attr/layoutManager = 0x7f030233
com.example.myapplication:anim/design_snackbar_in = 0x7f01001a
com.example.myapplication:id/mini = 0x7f08011a
com.example.myapplication:integer/abc_config_activityDefaultDur = 0x7f090000
com.example.myapplication:attr/layout = 0x7f030230
com.example.myapplication:color/material_divider_color = 0x7f0501a0
com.example.myapplication:dimen/mtrl_high_ripple_focused_alpha = 0x7f0601d8
com.example.myapplication:attr/listChoiceIndicatorMultipleAnimated = 0x7f030280
com.example.myapplication:dimen/material_clock_hand_center_dot_radius = 0x7f06013f
com.example.myapplication:color/switch_thumb_disabled_material_light = 0x7f050246
com.example.myapplication:style/Widget.MaterialComponents.Button.TextButton.Dialog.Flush = 0x7f11039b
com.example.myapplication:style/TextAppearance.Material3.DisplayMedium = 0x7f1101b8
com.example.myapplication:attr/cornerRadius = 0x7f030125
com.example.myapplication:attr/labelVisibilityMode = 0x7f03022d
com.example.myapplication:animator/mtrl_extended_fab_state_list_animator = 0x7f02001b
com.example.myapplication:color/m3_textfield_input_text_color = 0x7f05018c
com.example.myapplication:dimen/mtrl_navigation_rail_text_size = 0x7f0601f1
com.example.myapplication:attr/colorSecondaryContainer = 0x7f0300fd
com.example.myapplication:style/TextAppearance.MaterialComponents.Headline2 = 0x7f1101cb
com.example.myapplication:color/m3_sys_color_dark_inverse_surface = 0x7f05012a
com.example.myapplication:attr/divider = 0x7f030150
com.example.myapplication:dimen/compat_button_inset_vertical_material = 0x7f060058
com.example.myapplication:style/Widget.AppCompat.PopupWindow = 0x7f1102da
com.example.myapplication:attr/lineSpacing = 0x7f03027d
com.example.myapplication:attr/lStar = 0x7f03022a
com.example.myapplication:id/ratio = 0x7f08016a
com.example.myapplication:attr/layout_constraintStart_toEndOf = 0x7f030259
com.example.myapplication:color/m3_sys_color_dark_error_container = 0x7f050127
com.example.myapplication:dimen/mtrl_calendar_header_toggle_margin_top = 0x7f0601a1
com.example.myapplication:attr/data = 0x7f03013e
com.example.myapplication:color/m3_dynamic_dark_default_color_secondary_text = 0x7f050079
com.example.myapplication:attr/buttonCompat = 0x7f030080
com.example.myapplication:color/m3_ref_palette_tertiary70 = 0x7f050118
com.example.myapplication:attr/keyboardIcon = 0x7f030228
com.example.myapplication:attr/itemTextColor = 0x7f030225
com.example.myapplication:attr/itemTextAppearance = 0x7f030222
com.example.myapplication:id/chip3 = 0x7f080082
com.example.myapplication:style/Base.Widget.AppCompat.ActionBar.TabBar = 0x7f1100b9
com.example.myapplication:anim/btn_radio_to_off_mtrl_dot_group_animation = 0x7f010012
com.example.myapplication:attr/cornerSize = 0x7f030126
com.example.myapplication:attr/itemStrokeColor = 0x7f030220
com.example.myapplication:id/labeled = 0x7f0800f5
com.example.myapplication:attr/itemShapeInsetTop = 0x7f03021e
com.example.myapplication:attr/customColorDrawableValue = 0x7f030135
com.example.myapplication:attr/elevation = 0x7f03016f
com.example.myapplication:attr/textBackgroundPanX = 0x7f0303f1
com.example.myapplication:style/Widget.MaterialComponents.Button.TextButton = 0x7f110399
com.example.myapplication:drawable/abc_list_selector_background_transition_holo_light = 0x7f070032
com.example.myapplication:styleable/AnimatedStateListDrawableCompat = 0x7f12000a
com.example.myapplication:attr/materialCalendarMonthNavigationButton = 0x7f0302a5
com.example.myapplication:id/action_mode_bar = 0x7f080044
com.example.myapplication:attr/itemShapeInsetBottom = 0x7f03021b
com.example.myapplication:animator/mtrl_fab_transformation_sheet_expand_spec = 0x7f02001f
com.example.myapplication:style/TestStyleWithLineHeightAppearance = 0x7f11016d
com.example.myapplication:color/m3_sys_color_dark_on_tertiary_container = 0x7f050135
com.example.myapplication:attr/springStiffness = 0x7f030380
com.example.myapplication:animator/mtrl_extended_fab_change_size_expand_motion_spec = 0x7f020018
com.example.myapplication:dimen/m3_ripple_default_alpha = 0x7f060107
com.example.myapplication:dimen/m3_appbar_size_large = 0x7f0600ad
com.example.myapplication:attr/borderRound = 0x7f030066
com.example.myapplication:attr/mock_diagonalsColor = 0x7f0302c9
com.example.myapplication:attr/textAppearanceOverline = 0x7f0303e6
com.example.myapplication:attr/floatingActionButtonSecondaryStyle = 0x7f0301ae
com.example.myapplication:attr/borderlessButtonStyle = 0x7f030069
com.example.myapplication:dimen/mtrl_calendar_selection_text_baseline_to_top = 0x7f0601ad
com.example.myapplication:attr/itemIconSize = 0x7f030210
com.example.myapplication:attr/buttonBarPositiveButtonStyle = 0x7f03007e
com.example.myapplication:attr/closeIcon = 0x7f0300ca
com.example.myapplication:drawable/abc_spinner_mtrl_am_alpha = 0x7f070044
com.example.myapplication:style/ThemeOverlay.Material3.Button.TextButton.Snackbar = 0x7f110257
com.example.myapplication:id/auto = 0x7f080059
com.example.myapplication:attr/itemIconPadding = 0x7f03020f
com.example.myapplication:attr/maxCharacterCount = 0x7f0302b9
com.example.myapplication:attr/itemHorizontalTranslationEnabled = 0x7f03020e
com.example.myapplication:color/material_timepicker_button_background = 0x7f0501f9
com.example.myapplication:style/Theme.AppCompat.DayNight.Dialog.Alert = 0x7f1101dd
com.example.myapplication:animator/design_appbar_state_list_animator = 0x7f020000
com.example.myapplication:animator/fragment_open_exit = 0x7f020008
com.example.myapplication:anim/nav_default_enter_anim = 0x7f010020
com.example.myapplication:attr/collapsingToolbarLayoutLargeStyle = 0x7f0300d9
com.example.myapplication:attr/layout_marginBaseline = 0x7f030273
com.example.myapplication:attr/foregroundInsidePadding = 0x7f0301d2
com.example.myapplication:attr/itemActiveIndicatorStyle = 0x7f03020a
com.example.myapplication:style/Theme.AppCompat.DayNight.DialogWhenLarge = 0x7f1101df
com.example.myapplication:attr/itemPaddingBottom = 0x7f030215
com.example.myapplication:attr/initialActivityCount = 0x7f030205
com.example.myapplication:color/m3_sys_color_dynamic_dark_surface_variant = 0x7f050152
com.example.myapplication:color/m3_sys_color_dynamic_dark_on_background = 0x7f050143
com.example.myapplication:attr/trackColor = 0x7f03043a
com.example.myapplication:dimen/mtrl_extended_fab_translation_z_pressed = 0x7f0601d2
com.example.myapplication:attr/cardElevation = 0x7f03008a
com.example.myapplication:attr/indeterminateAnimationType = 0x7f0301fe
com.example.myapplication:attr/imagePanY = 0x7f0301fb
com.example.myapplication:attr/layout_constraintWidth = 0x7f030262
com.example.myapplication:drawable/abc_cab_background_internal_bg = 0x7f070016
com.example.myapplication:attr/flow_wrapMode = 0x7f0301c4
com.example.myapplication:style/ShapeAppearance.MaterialComponents.Test = 0x7f110150
com.example.myapplication:dimen/m3_navigation_menu_divider_horizontal_padding = 0x7f0600fe
com.example.myapplication:style/Base.V14.Theme.MaterialComponents.Light.Bridge = 0x7f11008e
com.example.myapplication:attr/ifTagSet = 0x7f0301f8
com.example.myapplication:color/m3_ref_palette_error99 = 0x7f0500db
com.example.myapplication:attr/textBackground = 0x7f0303f0
com.example.myapplication:attr/colorBackgroundFloating = 0x7f0300df
com.example.myapplication:attr/iconSize = 0x7f0301f2
com.example.myapplication:color/abc_secondary_text_material_light = 0x7f050012
com.example.myapplication:attr/iconGravity = 0x7f0301f0
com.example.myapplication:dimen/default_dimension = 0x7f06005f
com.example.myapplication:dimen/abc_action_button_min_width_overflow_material = 0x7f06000f
com.example.myapplication:attr/layout_keyline = 0x7f030272
com.example.myapplication:attr/dragThreshold = 0x7f03015a
com.example.myapplication:style/Widget.MaterialComponents.BottomAppBar = 0x7f11038d
com.example.myapplication:attr/listItemLayout = 0x7f030283
com.example.myapplication:dimen/m3_sys_typescale_display_medium_text_size = 0x7f060125
com.example.myapplication:attr/tabPaddingTop = 0x7f0303bd
com.example.myapplication:attr/fabAnimationMode = 0x7f03019a
com.example.myapplication:dimen/design_bottom_navigation_active_item_max_width = 0x7f060061
com.example.myapplication:attr/layout_scrollInterpolator = 0x7f030277
com.example.myapplication:style/Theme.MaterialComponents.Dialog.FixedSize = 0x7f110224
com.example.myapplication:attr/homeLayout = 0x7f0301ea
com.example.myapplication:layout/abc_cascading_menu_item_layout = 0x7f0b000b
com.example.myapplication:color/material_slider_inactive_tick_marks_color = 0x7f0501f6
com.example.myapplication:dimen/m3_badge_horizontal_offset = 0x7f0600af
com.example.myapplication:attr/hintTextAppearance = 0x7f0301e7
com.example.myapplication:style/Widget.MaterialComponents.MaterialCalendar.HeaderToggleButton = 0x7f1103c5
com.example.myapplication:attr/hintEnabled = 0x7f0301e6
com.example.myapplication:string/m3_sys_typescale_display_large_font = 0x7f100045
com.example.myapplication:id/startHorizontal = 0x7f0801aa
com.example.myapplication:drawable/abc_cab_background_top_material = 0x7f070017
com.example.myapplication:color/white = 0x7f050252
com.example.myapplication:style/Base.V14.ThemeOverlay.Material3.BottomSheetDialog = 0x7f110092
com.example.myapplication:layout/design_navigation_item_header = 0x7f0b0027
com.example.myapplication:attr/layout_constraintHeight_min = 0x7f03024e
com.example.myapplication:color/blue_500 = 0x7f050254
com.example.myapplication:attr/textAppearanceListItemSmall = 0x7f0303e5
com.example.myapplication:style/TextAppearance.AppCompat.Title = 0x7f11018c
com.example.myapplication:attr/hideOnContentScroll = 0x7f0301e3
com.example.myapplication:color/m3_ref_palette_dynamic_primary50 = 0x7f0500ae
com.example.myapplication:styleable/AppBarLayout_Layout = 0x7f12000f
com.example.myapplication:style/Test.Widget.MaterialComponents.MaterialCalendar.Day.Selected = 0x7f11016b
com.example.myapplication:color/material_dynamic_neutral60 = 0x7f0501a8
com.example.myapplication:dimen/m3_navigation_item_horizontal_padding = 0x7f0600f7
com.example.myapplication:style/Widget.AppCompat.DrawerArrowToggle = 0x7f1102b9
com.example.myapplication:color/design_default_color_primary = 0x7f050045
com.example.myapplication:id/action_bar_spinner = 0x7f08003b
com.example.myapplication:style/Widget.AppCompat.Button = 0x7f1102ae
com.example.myapplication:layout/design_bottom_navigation_item = 0x7f0b001f
com.example.myapplication:dimen/mtrl_calendar_title_baseline_to_top = 0x7f0601af
com.example.myapplication:animator/mtrl_card_state_list_anim = 0x7f020015
com.example.myapplication:attr/placeholderActivityName = 0x7f03031b
com.example.myapplication:attr/height = 0x7f0301dc
com.example.myapplication:attr/backgroundOverlayColorAlpha = 0x7f03004b
com.example.myapplication:attr/haloRadius = 0x7f0301da
com.example.myapplication:style/MaterialAlertDialog.Material3.Body.Text.CenterStacked = 0x7f110117
com.example.myapplication:animator/m3_elevated_chip_state_list_anim = 0x7f020012
com.example.myapplication:attr/haloColor = 0x7f0301d9
com.example.myapplication:attr/liftOnScroll = 0x7f030279
com.example.myapplication:id/chains = 0x7f08007c
com.example.myapplication:style/TextAppearance.AppCompat.Widget.ActionBar.Subtitle.Inverse = 0x7f110191
com.example.myapplication:attr/goIcon = 0x7f0301d6
com.example.myapplication:anim/design_bottom_sheet_slide_in = 0x7f010018
com.example.myapplication:id/material_timepicker_cancel_button = 0x7f080111
com.example.myapplication:attr/gestureInsetBottomIgnored = 0x7f0301d5
com.example.myapplication:drawable/abc_ic_menu_cut_mtrl_alpha = 0x7f070022
com.example.myapplication:style/Widget.Material3.Toolbar = 0x7f11037d
com.example.myapplication:attr/framePosition = 0x7f0301d3
com.example.myapplication:integer/m3_btn_anim_duration_ms = 0x7f09000b
com.example.myapplication:id/navigation_bar_item_active_indicator_view = 0x7f080141
com.example.myapplication:attr/textEndPadding = 0x7f0303f7
com.example.myapplication:attr/backgroundInsetStart = 0x7f030049
com.example.myapplication:attr/titleTextStyle = 0x7f03042c
com.example.myapplication:color/m3_ref_palette_primary70 = 0x7f0500fe
com.example.myapplication:attr/titlePositionInterpolator = 0x7f030429
com.example.myapplication:dimen/material_helper_text_font_1_3_padding_top = 0x7f060157
com.example.myapplication:dimen/m3_ripple_hovered_alpha = 0x7f060109
com.example.myapplication:color/material_slider_active_track_color = 0x7f0501f4
com.example.myapplication:style/MaterialAlertDialog.Material3.Title.Text.CenterStacked = 0x7f11011d
com.example.myapplication:attr/hideMotionSpec = 0x7f0301e2
com.example.myapplication:attr/dividerThickness = 0x7f030156
com.example.myapplication:id/hideable = 0x7f0800dd
com.example.myapplication:attr/dialogCornerRadius = 0x7f03014c
com.example.myapplication:style/Widget.AppCompat.Light.ActionBar.TabBar = 0x7f1102c0
com.example.myapplication:dimen/mtrl_slider_label_square_side = 0x7f060206
com.example.myapplication:attr/chipBackgroundColor = 0x7f0300a8
com.example.myapplication:attr/fontProviderSystemFontFamily = 0x7f0301cd
com.example.myapplication:attr/colorAccent = 0x7f0300de
com.example.myapplication:style/TextAppearance.MaterialComponents.Tooltip = 0x7f1101d4
com.example.myapplication:style/RtlOverlay.DialogWindowTitle.AppCompat = 0x7f110136
com.example.myapplication:attr/fontProviderFetchStrategy = 0x7f0301c9
com.example.myapplication:attr/fontProviderAuthority = 0x7f0301c7
com.example.myapplication:dimen/mtrl_calendar_year_vertical_padding = 0x7f0601b4
com.example.myapplication:attr/constraintSetStart = 0x7f030109
com.example.myapplication:style/ThemeOverlay.Material3.Light = 0x7f110268
com.example.myapplication:style/Theme.MyApplication.PopupOverlay = 0x7f110240
com.example.myapplication:layout/abc_screen_simple_overlay_action_mode = 0x7f0b0016
com.example.myapplication:attr/actionModeCloseButtonStyle = 0x7f030014
com.example.myapplication:dimen/mtrl_progress_circular_size = 0x7f0601f7
com.example.myapplication:dimen/abc_edit_text_inset_horizontal_material = 0x7f06002d
com.example.myapplication:attr/font = 0x7f0301c5
com.example.myapplication:attr/maxWidth = 0x7f0302be
com.example.myapplication:attr/actionTextColorAlpha = 0x7f030025
com.example.myapplication:dimen/m3_btn_icon_only_default_padding = 0x7f0600c8
com.example.myapplication:attr/colorPrimarySurface = 0x7f0300fa
com.example.myapplication:style/AlertDialog.AppCompat.Light = 0x7f110001
com.example.myapplication:attr/linearProgressIndicatorStyle = 0x7f03027e
com.example.myapplication:attr/tabMaxWidth = 0x7f0303b6
com.example.myapplication:attr/flow_padding = 0x7f0301bf
com.example.myapplication:attr/flow_maxElementsWrap = 0x7f0301be
com.example.myapplication:dimen/design_navigation_elevation = 0x7f060077
com.example.myapplication:color/mtrl_btn_transparent_bg_color = 0x7f050205
com.example.myapplication:drawable/mtrl_ic_arrow_drop_down = 0x7f070084
com.example.myapplication:attr/floatingActionButtonLargeSurfaceStyle = 0x7f0301ab
com.example.myapplication:style/TextAppearance.MaterialComponents.TimePicker.Title = 0x7f1101d3
com.example.myapplication:attr/thickness = 0x7f03040d
com.example.myapplication:attr/round = 0x7f03034a
com.example.myapplication:id/horizontal_only = 0x7f0800e1
com.example.myapplication:color/m3_navigation_item_background_color = 0x7f050088
com.example.myapplication:attr/perpendicularPath_percent = 0x7f030319
com.example.myapplication:color/m3_ref_palette_neutral_variant20 = 0x7f0500ec
com.example.myapplication:attr/dayStyle = 0x7f030142
com.example.myapplication:attr/prefixTextColor = 0x7f03032c
com.example.myapplication:styleable/NavHostFragment = 0x7f120071
com.example.myapplication:attr/titleTextColor = 0x7f03042b
com.example.myapplication:color/m3_ref_palette_neutral_variant100 = 0x7f0500eb
com.example.myapplication:styleable/RadialViewGroup = 0x7f12007d
com.example.myapplication:style/Widget.MaterialComponents.PopupMenu = 0x7f1103d4
com.example.myapplication:color/m3_ref_palette_dynamic_tertiary99 = 0x7f0500ce
com.example.myapplication:color/m3_text_button_foreground_color_selector = 0x7f050188
com.example.myapplication:attr/circularflow_defaultRadius = 0x7f0300c0
com.example.myapplication:attr/floatingActionButtonLargeTertiaryStyle = 0x7f0301ac
com.example.myapplication:attr/fontProviderCerts = 0x7f0301c8
com.example.myapplication:color/design_fab_shadow_end_color = 0x7f05004c
com.example.myapplication:attr/finishPrimaryWithSecondary = 0x7f0301a5
com.example.myapplication:attr/fastScrollHorizontalTrackDrawable = 0x7f0301a2
com.example.myapplication:dimen/design_navigation_item_vertical_padding = 0x7f06007c
com.example.myapplication:attr/fastScrollHorizontalThumbDrawable = 0x7f0301a1
com.example.myapplication:color/radiobutton_themeable_attribute_color = 0x7f05023e
com.example.myapplication:styleable/KeyTimeCycle = 0x7f12004a
com.example.myapplication:attr/fabCustomSize = 0x7f03019e
com.example.myapplication:string/m3_sys_typescale_body_small_font = 0x7f100044
com.example.myapplication:color/dim_foreground_material_dark = 0x7f050057
com.example.myapplication:style/Base.Animation.AppCompat.Tooltip = 0x7f11000c
com.example.myapplication:attr/fabCradleVerticalOffset = 0x7f03019d
com.example.myapplication:id/action_bar_subtitle = 0x7f08003c
com.example.myapplication:attr/fabCradleRoundedCornerRadius = 0x7f03019c
com.example.myapplication:attr/layout_constraintEnd_toEndOf = 0x7f030246
com.example.myapplication:attr/popEnterAnim = 0x7f030321
com.example.myapplication:attr/fastScrollVerticalThumbDrawable = 0x7f0301a3
com.example.myapplication:color/m3_ref_palette_error70 = 0x7f0500d7
com.example.myapplication:layout/abc_alert_dialog_material = 0x7f0b0009
com.example.myapplication:dimen/abc_panel_menu_list_width = 0x7f060034
com.example.myapplication:color/material_dynamic_neutral_variant10 = 0x7f0501af
com.example.myapplication:attr/layout_constraintWidth_default = 0x7f030263
com.example.myapplication:layout/mtrl_alert_select_dialog_singlechoice = 0x7f0b0048
com.example.myapplication:color/m3_assist_chip_stroke_color = 0x7f050061
com.example.myapplication:attr/fabCradleMargin = 0x7f03019b
com.example.myapplication:dimen/m3_sys_elevation_level5 = 0x7f060114
com.example.myapplication:color/material_dynamic_primary90 = 0x7f0501c5
com.example.myapplication:bool/m3_sys_typescale_label_small_text_all_caps = 0x7f04000d
com.example.myapplication:dimen/material_input_text_to_prefix_suffix_padding = 0x7f060158
com.example.myapplication:dimen/m3_snackbar_margin = 0x7f06010e
com.example.myapplication:attr/homeAsUpIndicator = 0x7f0301e9
com.example.myapplication:color/m3_sys_color_light_background = 0x7f05016b
com.example.myapplication:id/text = 0x7f0801c6
com.example.myapplication:style/Theme.Material3.DayNight.NoActionBar = 0x7f110201
com.example.myapplication:attr/extendedFloatingActionButtonSecondaryStyle = 0x7f030194
com.example.myapplication:attr/textAppearanceHeadlineSmall = 0x7f0303dd
com.example.myapplication:attr/extendedFloatingActionButtonPrimaryStyle = 0x7f030193
com.example.myapplication:color/abc_primary_text_disable_only_material_dark = 0x7f050009
com.example.myapplication:dimen/m3_sys_state_pressed_state_layer_opacity = 0x7f06011b
com.example.myapplication:attr/passwordToggleContentDescription = 0x7f03030e
com.example.myapplication:attr/checkedTextViewStyle = 0x7f0300a7
com.example.myapplication:color/mtrl_textinput_default_box_stroke_color = 0x7f05022c
com.example.myapplication:style/Base.Widget.AppCompat.Light.ActionBar = 0x7f1100d2
com.example.myapplication:color/mtrl_navigation_bar_colored_item_tint = 0x7f050219
com.example.myapplication:attr/nullable = 0x7f0302f7
com.example.myapplication:attr/expandedTitleTextColor = 0x7f030191
com.example.myapplication:attr/fabAlignmentMode = 0x7f030199
com.example.myapplication:style/Widget.MaterialComponents.MaterialCalendar.Day.Today = 0x7f1103ba
com.example.myapplication:attr/expandedTitleMarginStart = 0x7f03018e
com.example.myapplication:color/m3_ref_palette_dynamic_neutral_variant95 = 0x7f0500a6
com.example.myapplication:id/counterclockwise = 0x7f080096
com.example.myapplication:dimen/m3_btn_text_btn_icon_padding_left = 0x7f0600d3
com.example.myapplication:attr/clockFaceBackgroundColor = 0x7f0300c6
com.example.myapplication:attr/expandActivityOverflowButtonDrawable = 0x7f030187
com.example.myapplication:styleable/ExtendedFloatingActionButton_Behavior_Layout = 0x7f120036
com.example.myapplication:style/TextAppearance.AppCompat.Light.Widget.PopupMenu.Large = 0x7f110181
com.example.myapplication:style/Animation.AppCompat.Dialog = 0x7f110003
com.example.myapplication:attr/errorTextColor = 0x7f030185
com.example.myapplication:attr/errorTextAppearance = 0x7f030184
com.example.myapplication:styleable/BottomNavigationView = 0x7f120019
com.example.myapplication:attr/errorIconTintMode = 0x7f030183
com.example.myapplication:attr/materialCalendarHeaderToggleButton = 0x7f0302a3
com.example.myapplication:style/ShapeAppearanceOverlay.MaterialComponents.ExtendedFloatingActionButton = 0x7f11015f
com.example.myapplication:attr/itemPadding = 0x7f030214
com.example.myapplication:style/Base.V24.Theme.Material3.Dark.Dialog = 0x7f1100a7
com.example.myapplication:attr/windowMinWidthMinor = 0x7f030469
com.example.myapplication:color/material_dynamic_tertiary50 = 0x7f0501db
com.example.myapplication:attr/materialCardViewStyle = 0x7f0302ac
com.example.myapplication:attr/enterAnim = 0x7f03017e
com.example.myapplication:style/Base.TextAppearance.Widget.AppCompat.ExpandedMenu.Item = 0x7f110049
com.example.myapplication:attr/secondaryActivityAction = 0x7f030355
com.example.myapplication:attr/enforceMaterialTheme = 0x7f03017b
com.example.myapplication:color/tooltip_background_dark = 0x7f050250
com.example.myapplication:attr/colorControlHighlight = 0x7f0300e3
com.example.myapplication:attr/showText = 0x7f030369
com.example.myapplication:attr/SharedValue = 0x7f030000
com.example.myapplication:attr/buttonBarButtonStyle = 0x7f03007b
com.example.myapplication:attr/buttonTint = 0x7f030086
com.example.myapplication:style/Base.Theme.MaterialComponents.Light.Dialog = 0x7f11006d
com.example.myapplication:attr/horizontalOffset = 0x7f0301eb
com.example.myapplication:attr/behavior_draggable = 0x7f03005c
com.example.myapplication:style/TextAppearance.Compat.Notification.Info = 0x7f1101a3
com.example.myapplication:id/match_parent = 0x7f080104
com.example.myapplication:attr/currentState = 0x7f030132
com.example.myapplication:animator/nav_default_pop_enter_anim = 0x7f020022
com.example.myapplication:attr/emojiCompatEnabled = 0x7f030173
com.example.myapplication:attr/tintMode = 0x7f03041e
com.example.myapplication:attr/elevationOverlayEnabled = 0x7f030172
com.example.myapplication:attr/elevationOverlayColor = 0x7f030171
com.example.myapplication:attr/textAppearanceHeadlineLarge = 0x7f0303db
com.example.myapplication:attr/materialAlertDialogBodyTextStyle = 0x7f030291
com.example.myapplication:attr/editTextStyle = 0x7f03016e
com.example.myapplication:id/legacy = 0x7f0800f9
com.example.myapplication:styleable/OnSwipe = 0x7f120079
com.example.myapplication:dimen/design_snackbar_text_size = 0x7f06008a
com.example.myapplication:attr/checkedIconSize = 0x7f0300a4
com.example.myapplication:attr/circularflow_defaultAngle = 0x7f0300bf
com.example.myapplication:attr/quantizeMotionPhase = 0x7f030333
com.example.myapplication:attr/tabStyle = 0x7f0303c1
com.example.myapplication:attr/deriveConstraintsFrom = 0x7f03014a
com.example.myapplication:attr/floatingActionButtonLargeStyle = 0x7f0301aa
com.example.myapplication:layout/mtrl_picker_fullscreen = 0x7f0b0058
com.example.myapplication:id/actions = 0x7f080049
com.example.myapplication:dimen/mtrl_slider_label_radius = 0x7f060205
com.example.myapplication:attr/drawerLayoutCornerSize = 0x7f030166
com.example.myapplication:id/showTitle = 0x7f080191
com.example.myapplication:style/Theme.Material3.Dark.BottomSheetDialog = 0x7f1101f5
com.example.myapplication:id/actionUp = 0x7f080034
com.example.myapplication:dimen/mtrl_navigation_rail_text_bottom_margin = 0x7f0601f0
com.example.myapplication:attr/textInputStyle = 0x7f030400
com.example.myapplication:attr/drawerArrowStyle = 0x7f030165
com.example.myapplication:attr/motionDurationLong1 = 0x7f0302d0
com.example.myapplication:color/m3_sys_color_dark_inverse_on_surface = 0x7f050128
com.example.myapplication:attr/drawableTintMode = 0x7f030163
com.example.myapplication:id/month_navigation_bar = 0x7f08011c
com.example.myapplication:attr/colorOnTertiary = 0x7f0300f3
com.example.myapplication:styleable/ClockFaceView = 0x7f120023
com.example.myapplication:attr/suggestionRowLayout = 0x7f0303a4
com.example.myapplication:attr/hideOnScroll = 0x7f0301e4
com.example.myapplication:color/m3_ref_palette_dynamic_neutral70 = 0x7f050096
com.example.myapplication:id/tag_unhandled_key_event_manager = 0x7f0801bf
com.example.myapplication:attr/drawableEndCompat = 0x7f03015d
com.example.myapplication:animator/m3_btn_elevated_btn_state_list_anim = 0x7f02000d
com.example.myapplication:attr/boxStrokeErrorColor = 0x7f030077
com.example.myapplication:attr/dragScale = 0x7f030159
com.example.myapplication:attr/textureEffect = 0x7f030408
com.example.myapplication:attr/itemShapeInsetEnd = 0x7f03021c
com.example.myapplication:color/material_grey_600 = 0x7f0501e5
com.example.myapplication:attr/layout_constraintCircle = 0x7f030242
com.example.myapplication:attr/dividerPadding = 0x7f030155
com.example.myapplication:animator/nav_default_exit_anim = 0x7f020021
com.example.myapplication:attr/daySelectedStyle = 0x7f030141
com.example.myapplication:attr/dividerInsetStart = 0x7f030154
com.example.myapplication:style/Base.ThemeOverlay.MaterialComponents.Dialog = 0x7f11007e
com.example.myapplication:attr/colorPrimary = 0x7f0300f6
com.example.myapplication:attr/dividerInsetEnd = 0x7f030153
com.example.myapplication:attr/contentScrim = 0x7f03011c
com.example.myapplication:attr/titleMarginEnd = 0x7f030425
com.example.myapplication:string/abc_capital_on = 0x7f100007
com.example.myapplication:attr/extraMultilineHeightEnabled = 0x7f030198
com.example.myapplication:interpolator/btn_checkbox_checked_mtrl_animation_interpolator_1 = 0x7f0a0001
com.example.myapplication:dimen/m3_navigation_rail_item_min_height = 0x7f060104
com.example.myapplication:color/design_fab_stroke_top_inner_color = 0x7f050051
com.example.myapplication:dimen/mtrl_extended_fab_disabled_elevation = 0x7f0601c4
com.example.myapplication:id/callMeasure = 0x7f080074
com.example.myapplication:color/m3_sys_color_dynamic_light_on_background = 0x7f050159
com.example.myapplication:attr/path_percent = 0x7f030314
com.example.myapplication:attr/minHideDelay = 0x7f0302c5
com.example.myapplication:attr/displayOptions = 0x7f03014f
com.example.myapplication:styleable/ViewTransition = 0x7f1200a3
com.example.myapplication:attr/imageRotate = 0x7f0301fc
com.example.myapplication:attr/popExitAnim = 0x7f030322
com.example.myapplication:attr/actionViewClass = 0x7f030026
com.example.myapplication:id/mtrl_internal_children_alpha_tag = 0x7f08012d
com.example.myapplication:styleable/NavigationBarView = 0x7f120074
com.example.myapplication:attr/dialogPreferredPadding = 0x7f03014d
com.example.myapplication:id/checked = 0x7f08007e
com.example.myapplication:color/material_dynamic_primary50 = 0x7f0501c1
com.example.myapplication:color/design_fab_shadow_start_color = 0x7f05004e
com.example.myapplication:attr/chipStartPadding = 0x7f0300b7
com.example.myapplication:attr/dayInvalidStyle = 0x7f030140
com.example.myapplication:style/Widget.MaterialComponents.Button.TextButton.Dialog = 0x7f11039a
com.example.myapplication:attr/visibilityMode = 0x7f030458
com.example.myapplication:attr/dataPattern = 0x7f03013f
com.example.myapplication:dimen/material_cursor_inset_top = 0x7f060148
com.example.myapplication:attr/layout_constraintTop_toBottomOf = 0x7f03025d
com.example.myapplication:attr/titleCentered = 0x7f030420
com.example.myapplication:color/m3_ref_palette_dynamic_primary70 = 0x7f0500b0
com.example.myapplication:dimen/mtrl_btn_padding_left = 0x7f060181
com.example.myapplication:color/design_default_color_error = 0x7f05003f
com.example.myapplication:style/Widget.MaterialComponents.TextInputEditText.FilledBox = 0x7f1103e1
com.example.myapplication:color/m3_ref_palette_dynamic_neutral_variant30 = 0x7f05009f
com.example.myapplication:attr/collapseIcon = 0x7f0300d3
com.example.myapplication:attr/statusBarBackground = 0x7f030391
com.example.myapplication:attr/customFloatValue = 0x7f030138
com.example.myapplication:attr/counterTextAppearance = 0x7f03012f
com.example.myapplication:style/Widget.AppCompat.SeekBar = 0x7f1102e2
com.example.myapplication:attr/tickMarkTint = 0x7f03041a
com.example.myapplication:id/included = 0x7f0800e9
com.example.myapplication:attr/scaleFromTextSize = 0x7f03034e
com.example.myapplication:layout/test_design_checkbox = 0x7f0b006c
com.example.myapplication:dimen/m3_btn_translation_z_base = 0x7f0600d7
com.example.myapplication:attr/actionModeStyle = 0x7f03001f
com.example.myapplication:attr/counterOverflowTextAppearance = 0x7f03012d
com.example.myapplication:attr/endIconMode = 0x7f030178
com.example.myapplication:style/RtlOverlay.Widget.AppCompat.PopupMenuItem.Title = 0x7f11013e
com.example.myapplication:color/m3_navigation_bar_ripple_color_selector = 0x7f050087
com.example.myapplication:attr/cornerSizeTopLeft = 0x7f030129
com.example.myapplication:color/m3_sys_color_light_secondary = 0x7f05017f
com.example.myapplication:styleable/MaterialShape = 0x7f12005c
com.example.myapplication:anim/abc_tooltip_enter = 0x7f01000a
com.example.myapplication:attr/chipStrokeWidth = 0x7f0300b9
com.example.myapplication:attr/cornerFamilyTopRight = 0x7f030124
com.example.myapplication:attr/contentPaddingTop = 0x7f03011b
com.example.myapplication:attr/contentPaddingStart = 0x7f03011a
com.example.myapplication:dimen/m3_bottomappbar_fab_cradle_vertical_offset = 0x7f0600bf
com.example.myapplication:id/nav_calendar_active = 0x7f08020d
com.example.myapplication:attr/barrierMargin = 0x7f030059
com.example.myapplication:color/material_on_primary_emphasis_medium = 0x7f0501ee
com.example.myapplication:attr/springDamping = 0x7f03037e
com.example.myapplication:styleable/CircularProgressIndicator = 0x7f120022
com.example.myapplication:attr/indicatorDirectionLinear = 0x7f030202
com.example.myapplication:color/mtrl_textinput_focused_box_stroke_color = 0x7f05022f
com.example.myapplication:id/autoCompleteToStart = 0x7f08005c
com.example.myapplication:string/mtrl_picker_date_header_selected = 0x7f10006e
com.example.myapplication:attr/contentPaddingLeft = 0x7f030118
com.example.myapplication:dimen/m3_card_stroke_width = 0x7f0600df
com.example.myapplication:style/Platform.V21.AppCompat = 0x7f110131
com.example.myapplication:attr/tint = 0x7f03041d
com.example.myapplication:attr/fastScrollEnabled = 0x7f0301a0
com.example.myapplication:dimen/m3_btn_text_btn_icon_padding_right = 0x7f0600d4
com.example.myapplication:string/icon_content_description = 0x7f100036
com.example.myapplication:attr/colorPrimaryContainer = 0x7f0300f7
com.example.myapplication:id/homeAsUp = 0x7f0800df
com.example.myapplication:attr/contentInsetStart = 0x7f030113
com.example.myapplication:attr/textAppearanceTitleLarge = 0x7f0303ed
com.example.myapplication:color/material_dynamic_secondary0 = 0x7f0501c8
com.example.myapplication:attr/contentInsetLeft = 0x7f030111
com.example.myapplication:drawable/avd_show_password = 0x7f070057
com.example.myapplication:attr/colorButtonNormal = 0x7f0300e0
com.example.myapplication:color/m3_ref_palette_secondary10 = 0x7f050104
com.example.myapplication:style/Base.Theme.Material3.Light.Dialog = 0x7f11005f
com.example.myapplication:drawable/m3_selection_control_ripple = 0x7f070073
com.example.myapplication:attr/contentInsetEndWithActions = 0x7f030110
com.example.myapplication:attr/textAppearanceHeadlineMedium = 0x7f0303dc
com.example.myapplication:attr/carousel_touchUpMode = 0x7f030097
com.example.myapplication:attr/constraintSetEnd = 0x7f030108
com.example.myapplication:color/button_material_dark = 0x7f050028
com.example.myapplication:attr/actionModeCopyDrawable = 0x7f030017
com.example.myapplication:id/alertTitle = 0x7f08004c
com.example.myapplication:anim/abc_slide_in_top = 0x7f010007
com.example.myapplication:attr/motionDurationLong2 = 0x7f0302d1
com.example.myapplication:attr/commitIcon = 0x7f030105
com.example.myapplication:dimen/m3_alert_dialog_elevation = 0x7f0600a3
com.example.myapplication:string/mtrl_picker_invalid_format_example = 0x7f100073
com.example.myapplication:attr/textAppearanceHeadline1 = 0x7f0303d5
com.example.myapplication:anim/mtrl_bottom_sheet_slide_in = 0x7f01001d
com.example.myapplication:style/Base.TextAppearance.Material3.TitleMedium = 0x7f110043
com.example.myapplication:attr/altSrc = 0x7f030031
com.example.myapplication:anim/btn_radio_to_off_mtrl_ring_outer_path_animation = 0x7f010014
com.example.myapplication:color/material_dynamic_tertiary99 = 0x7f0501e1
com.example.myapplication:attr/subtitleTextAppearance = 0x7f03039e
com.example.myapplication:dimen/mtrl_calendar_landscape_header_width = 0x7f0601a2
com.example.myapplication:style/TextAppearance.AppCompat.Button = 0x7f110175
com.example.myapplication:style/MaterialAlertDialog.Material3.Title.Icon = 0x7f110118
com.example.myapplication:id/on = 0x7f080154
com.example.myapplication:color/m3_ref_palette_secondary50 = 0x7f050109
com.example.myapplication:attr/bottomNavigationStyle = 0x7f03006c
com.example.myapplication:color/cardview_shadow_start_color = 0x7f05002d
com.example.myapplication:id/tv_detail_deadline = 0x7f0801e9
com.example.myapplication:dimen/design_navigation_max_width = 0x7f06007d
com.example.myapplication:attr/placeholder_emptyVisibility = 0x7f03031f
com.example.myapplication:attr/reactiveGuide_valueId = 0x7f030340
com.example.myapplication:attr/circularflow_angles = 0x7f0300be
com.example.myapplication:id/fitToContents = 0x7f0800cc
com.example.myapplication:attr/crossfade = 0x7f030131
com.example.myapplication:attr/maxVelocity = 0x7f0302bd
com.example.myapplication:attr/iconTintMode = 0x7f0301f5
com.example.myapplication:style/RtlOverlay.Widget.AppCompat.PopupMenuItem.SubmenuArrow = 0x7f11013c
com.example.myapplication:attr/colorPrimaryDark = 0x7f0300f8
com.example.myapplication:attr/defaultState = 0x7f030147
com.example.myapplication:style/Base.Widget.AppCompat.ListView.Menu = 0x7f1100de
com.example.myapplication:style/Base.V7.Theme.AppCompat.Dialog = 0x7f1100b0
com.example.myapplication:attr/autoTransition = 0x7f030044
com.example.myapplication:attr/contentPadding = 0x7f030115
com.example.myapplication:attr/SharedValueId = 0x7f030001
com.example.myapplication:attr/behavior_skipCollapsed = 0x7f030064
com.example.myapplication:attr/constraints = 0x7f03010c
com.example.myapplication:attr/colorOnSurfaceVariant = 0x7f0300f2
com.example.myapplication:style/Base.V24.Theme.Material3.Light = 0x7f1100a8
com.example.myapplication:integer/mtrl_view_invisible = 0x7f09002b
com.example.myapplication:attr/barrierDirection = 0x7f030058
com.example.myapplication:color/m3_sys_color_light_primary_container = 0x7f05017e
com.example.myapplication:attr/touchAnchorSide = 0x7f030437
com.example.myapplication:attr/chipCornerRadius = 0x7f0300a9
com.example.myapplication:styleable/Chip = 0x7f120020
com.example.myapplication:style/Widget.MaterialComponents.ProgressIndicator = 0x7f1103d8
com.example.myapplication:id/onInterceptTouchReturnSwipe = 0x7f080155
com.example.myapplication:attr/collapsedTitleTextAppearance = 0x7f0300d6
com.example.myapplication:attr/colorOnSurfaceInverse = 0x7f0300f1
com.example.myapplication:attr/itemSpacing = 0x7f03021f
com.example.myapplication:attr/colorOnPrimaryContainer = 0x7f0300ec
com.example.myapplication:attr/actionMenuTextColor = 0x7f030012
com.example.myapplication:attr/carousel_emptyViewsBehavior = 0x7f030091
com.example.myapplication:attr/colorOnErrorContainer = 0x7f0300ea
com.example.myapplication:attr/colorOnError = 0x7f0300e9
com.example.myapplication:attr/colorErrorContainer = 0x7f0300e6
com.example.myapplication:attr/colorError = 0x7f0300e5
com.example.myapplication:anim/mtrl_bottom_sheet_slide_out = 0x7f01001e
com.example.myapplication:styleable/KeyPosition = 0x7f120049
com.example.myapplication:style/Base.TextAppearance.AppCompat.Title = 0x7f11002c
com.example.myapplication:attr/itemShapeInsetStart = 0x7f03021d
com.example.myapplication:color/m3_ref_palette_dynamic_neutral60 = 0x7f050095
com.example.myapplication:id/chip = 0x7f08007f
com.example.myapplication:dimen/mtrl_low_ripple_pressed_alpha = 0x7f0601df
com.example.myapplication:attr/materialAlertDialogTitleIconStyle = 0x7f030294
com.example.myapplication:color/design_dark_default_color_on_primary = 0x7f050035
com.example.myapplication:attr/counterEnabled = 0x7f03012b
com.example.myapplication:attr/colorContainer = 0x7f0300e1
com.example.myapplication:id/collapseActionView = 0x7f08008b
com.example.myapplication:animator/mtrl_fab_transformation_sheet_collapse_spec = 0x7f02001e
com.example.myapplication:attr/indicatorInset = 0x7f030203
com.example.myapplication:color/material_slider_thumb_color = 0x7f0501f8
com.example.myapplication:styleable/MaterialCalendar = 0x7f120056
com.example.myapplication:attr/materialTimePickerStyle = 0x7f0302b3
com.example.myapplication:attr/customColorValue = 0x7f030136
com.example.myapplication:attr/transitionDisable = 0x7f030443
com.example.myapplication:style/RtlOverlay.Widget.AppCompat.PopupMenuItem.Shortcut = 0x7f11013b
com.example.myapplication:dimen/mtrl_extended_fab_icon_size = 0x7f0601c9
com.example.myapplication:attr/animateRelativeTo = 0x7f030034
com.example.myapplication:string/delete_task = 0x7f10002c
com.example.myapplication:attr/state_liftable = 0x7f03038f
com.example.myapplication:attr/collapsingToolbarLayoutStyle = 0x7f0300dc
com.example.myapplication:attr/layout_constrainedWidth = 0x7f03023a
com.example.myapplication:dimen/material_clock_display_padding = 0x7f06013d
com.example.myapplication:attr/collapsingToolbarLayoutMediumStyle = 0x7f0300db
com.example.myapplication:attr/chipSpacingVertical = 0x7f0300b5
com.example.myapplication:id/btn_personal_tasks = 0x7f08006e
com.example.myapplication:attr/fontVariationSettings = 0x7f0301cf
com.example.myapplication:attr/collapsedTitleTextColor = 0x7f0300d7
com.example.myapplication:styleable/SplitPairFilter = 0x7f12008a
com.example.myapplication:styleable/ChipGroup = 0x7f120021
com.example.myapplication:attr/textAllCaps = 0x7f0303ca
com.example.myapplication:style/Base.Widget.AppCompat.ActionBar.TabText = 0x7f1100ba
com.example.myapplication:id/test_checkbox_app_button_tint = 0x7f0801c3
com.example.myapplication:attr/boxCornerRadiusBottomEnd = 0x7f030072
com.example.myapplication:attr/colorPrimaryInverse = 0x7f0300f9
com.example.myapplication:id/disableHome = 0x7f0800aa
com.example.myapplication:id/textEnd = 0x7f0801c8
com.example.myapplication:attr/collapsedTitleGravity = 0x7f0300d5
com.example.myapplication:id/FirstFragment = 0x7f080005
com.example.myapplication:layout/abc_screen_toolbar = 0x7f0b0017
com.example.myapplication:id/text_input_start_icon = 0x7f0801cf
com.example.myapplication:attr/checkMarkTintMode = 0x7f03009d
com.example.myapplication:attr/collapseContentDescription = 0x7f0300d2
com.example.myapplication:color/m3_sys_color_dynamic_light_on_surface_variant = 0x7f05015f
com.example.myapplication:id/stop = 0x7f0801af
com.example.myapplication:style/TextAppearance.MaterialComponents.Button = 0x7f1101c7
com.example.myapplication:style/TextAppearance.AppCompat.Large.Inverse = 0x7f11017e
com.example.myapplication:color/design_fab_stroke_top_outer_color = 0x7f050052
com.example.myapplication:attr/values = 0x7f030450
com.example.myapplication:style/ThemeOverlay.MaterialComponents.Light.Dialog.Alert.Framework = 0x7f11028d
com.example.myapplication:attr/colorOnPrimarySurface = 0x7f0300ed
com.example.myapplication:attr/closeIconTint = 0x7f0300cf
com.example.myapplication:style/Base.V26.Widget.AppCompat.Toolbar = 0x7f1100ac
com.example.myapplication:color/purple_200 = 0x7f05023b
com.example.myapplication:attr/motionEffect_start = 0x7f0302de
com.example.myapplication:attr/clockHandColor = 0x7f0300c7
com.example.myapplication:color/m3_dark_primary_text_disable_only = 0x7f050075
com.example.myapplication:dimen/mtrl_progress_circular_inset_medium = 0x7f0601f4
com.example.myapplication:attr/dynamicColorThemeOverlay = 0x7f03016b
com.example.myapplication:id/FUNCTION = 0x7f080004
com.example.myapplication:attr/behavior_peekHeight = 0x7f030062
com.example.myapplication:attr/clickAction = 0x7f0300c5
com.example.myapplication:attr/circularflow_radiusInDP = 0x7f0300c1
com.example.myapplication:attr/onTouchUp = 0x7f030300
com.example.myapplication:attr/motionDurationMedium2 = 0x7f0302d3
com.example.myapplication:attr/tabInlineLabel = 0x7f0303b5
com.example.myapplication:attr/themeLineHeight = 0x7f03040c
com.example.myapplication:color/abc_background_cache_hint_selector_material_light = 0x7f050001
com.example.myapplication:style/Base.Theme.MaterialComponents = 0x7f110060
com.example.myapplication:attr/chipSurfaceColor = 0x7f0300bb
com.example.myapplication:animator/fragment_close_enter = 0x7f020003
com.example.myapplication:dimen/abc_cascading_menus_min_smallest_width = 0x7f060016
com.example.myapplication:animator/fragment_fade_exit = 0x7f020006
com.example.myapplication:attr/tabIconTintMode = 0x7f0303ad
com.example.myapplication:styleable/ScrimInsetsFrameLayout = 0x7f120081
com.example.myapplication:attr/expandedHintEnabled = 0x7f030189
com.example.myapplication:style/TextAppearance.AppCompat.Widget.Button.Borderless.Colored = 0x7f110199
com.example.myapplication:attr/chipStrokeColor = 0x7f0300b8
com.example.myapplication:attr/tabGravity = 0x7f0303ab
com.example.myapplication:layout/abc_action_menu_item_layout = 0x7f0b0002
com.example.myapplication:style/Widget.MaterialComponents.Button = 0x7f110395
com.example.myapplication:animator/linear_indeterminate_line2_head_interpolator = 0x7f02000b
com.example.myapplication:attr/backgroundTintMode = 0x7f03004f
com.example.myapplication:style/Base.V14.ThemeOverlay.MaterialComponents.Dialog.Alert = 0x7f110095
com.example.myapplication:attr/guidelineUseRtl = 0x7f0301d8
com.example.myapplication:styleable/SnackbarLayout = 0x7f120088
com.example.myapplication:attr/chipSpacing = 0x7f0300b3
com.example.myapplication:id/mtrl_picker_text_input_date = 0x7f080134
com.example.myapplication:dimen/abc_dialog_list_padding_bottom_no_buttons = 0x7f060020
com.example.myapplication:attr/chipMinTouchTargetSize = 0x7f0300b2
com.example.myapplication:style/Base.Widget.AppCompat.ActionButton.CloseMode = 0x7f1100bd
com.example.myapplication:style/Base.V21.ThemeOverlay.AppCompat.Dialog = 0x7f11009f
com.example.myapplication:attr/popupTheme = 0x7f030328
com.example.myapplication:attr/chipIconSize = 0x7f0300ae
com.example.myapplication:attr/customNavigationLayout = 0x7f03013a
com.example.myapplication:string/material_timepicker_minute = 0x7f100061
com.example.myapplication:attr/queryHint = 0x7f030336
com.example.myapplication:attr/badgeRadius = 0x7f030051
com.example.myapplication:attr/itemFillColor = 0x7f03020c
com.example.myapplication:id/disableScroll = 0x7f0800ad
com.example.myapplication:attr/chipEndPadding = 0x7f0300aa
com.example.myapplication:style/Base.TextAppearance.AppCompat.Widget.ActionBar.Title.Inverse = 0x7f110033
com.example.myapplication:attr/trackHeight = 0x7f03043e
com.example.myapplication:attr/indicatorSize = 0x7f030204
com.example.myapplication:style/Base.V28.Theme.AppCompat = 0x7f1100ad
com.example.myapplication:color/abc_search_url_text_pressed = 0x7f05000f
com.example.myapplication:color/design_dark_default_color_surface = 0x7f05003d
com.example.myapplication:attr/checkedIconVisible = 0x7f0300a6
com.example.myapplication:dimen/abc_action_bar_stacked_tab_max_width = 0x7f06000a
com.example.myapplication:color/m3_ref_palette_dynamic_tertiary100 = 0x7f0500c4
com.example.myapplication:attr/checkedIconEnabled = 0x7f0300a2
com.example.myapplication:attr/hintTextColor = 0x7f0301e8
com.example.myapplication:attr/checkedIcon = 0x7f0300a1
com.example.myapplication:id/material_label = 0x7f08010d
com.example.myapplication:attr/flow_horizontalAlign = 0x7f0301b6
com.example.myapplication:dimen/m3_extended_fab_bottom_padding = 0x7f0600e9
com.example.myapplication:color/m3_ref_palette_dynamic_secondary95 = 0x7f0500c0
com.example.myapplication:style/TextAppearance.AppCompat.Inverse = 0x7f11017c
com.example.myapplication:attr/state_collapsed = 0x7f03038c
com.example.myapplication:attr/counterOverflowTextColor = 0x7f03012e
com.example.myapplication:attr/animateCircleAngleTo = 0x7f030033
com.example.myapplication:attr/sliderStyle = 0x7f030370
com.example.myapplication:color/material_dynamic_neutral20 = 0x7f0501a4
com.example.myapplication:id/action_settings = 0x7f080047
com.example.myapplication:attr/checkedChip = 0x7f0300a0
com.example.myapplication:color/primary_material_light = 0x7f050236
com.example.myapplication:attr/boxBackgroundMode = 0x7f030070
com.example.myapplication:style/Base.Widget.AppCompat.ProgressBar.Horizontal = 0x7f1100e3
com.example.myapplication:attr/motionEasingAccelerated = 0x7f0302d6
com.example.myapplication:color/design_dark_default_color_on_surface = 0x7f050037
com.example.myapplication:dimen/m3_sys_typescale_body_large_text_size = 0x7f06011d
com.example.myapplication:style/Widget.MaterialComponents.AppBarLayout.Surface = 0x7f110387
com.example.myapplication:attr/actionModeCutDrawable = 0x7f030018
com.example.myapplication:attr/chainUseRtl = 0x7f03009a
com.example.myapplication:id/asConfigured = 0x7f080057
com.example.myapplication:layout/activity_main = 0x7f0b001c
com.example.myapplication:attr/itemTextAppearanceInactive = 0x7f030224
com.example.myapplication:attr/buttonStyleSmall = 0x7f030085
com.example.myapplication:attr/tabPaddingStart = 0x7f0303bc
com.example.myapplication:style/ThemeOverlayColorAccentRed = 0x7f1102a2
com.example.myapplication:attr/actionButtonStyle = 0x7f03000e
com.example.myapplication:dimen/mtrl_badge_radius = 0x7f060168
com.example.myapplication:id/carryVelocity = 0x7f080076
com.example.myapplication:attr/buttonTintMode = 0x7f030087
com.example.myapplication:attr/customIntegerValue = 0x7f030139
com.example.myapplication:style/Theme.MaterialComponents.Light.DarkActionBar.Bridge = 0x7f11022e
com.example.myapplication:color/mtrl_btn_text_btn_bg_color_selector = 0x7f050201
com.example.myapplication:string/abc_searchview_description_query = 0x7f100014
com.example.myapplication:attr/carousel_previousState = 0x7f030096
com.example.myapplication:color/material_on_surface_disabled = 0x7f0501ef
com.example.myapplication:id/view_offset_helper = 0x7f0801f8
com.example.myapplication:color/abc_decor_view_status_guard = 0x7f050005
com.example.myapplication:layout/support_simple_spinner_dropdown_item = 0x7f0b0069
com.example.myapplication:drawable/abc_btn_radio_material_anim = 0x7f070011
com.example.myapplication:style/RtlOverlay.Widget.AppCompat.PopupMenuItem = 0x7f110139
com.example.myapplication:attr/drawerLayoutStyle = 0x7f030167
com.example.myapplication:attr/cardPreventCornerOverlap = 0x7f03008d
com.example.myapplication:attr/chipGroupStyle = 0x7f0300ab
com.example.myapplication:attr/paddingLeftSystemWindowInsets = 0x7f030306
com.example.myapplication:attr/numericModifiers = 0x7f0302f9
com.example.myapplication:attr/colorOnTertiaryContainer = 0x7f0300f4
com.example.myapplication:string/previous = 0x7f100094
com.example.myapplication:id/allStates = 0x7f08004f
com.example.myapplication:style/ThemeOverlay.MaterialComponents.MaterialAlertDialog.Picker.Date.Header.Text.Day = 0x7f110293
com.example.myapplication:attr/clearTop = 0x7f0300c3
com.example.myapplication:id/line1 = 0x7f0800fa
com.example.myapplication:animator/m3_chip_state_list_anim = 0x7f020011
com.example.myapplication:id/bounceBoth = 0x7f080065
com.example.myapplication:color/m3_ref_palette_error80 = 0x7f0500d8
com.example.myapplication:attr/collapsingToolbarLayoutLargeSize = 0x7f0300d8
com.example.myapplication:attr/activityAction = 0x7f030027
com.example.myapplication:attr/layout_constraintLeft_toRightOf = 0x7f030255
com.example.myapplication:attr/materialCalendarDay = 0x7f03029a
com.example.myapplication:dimen/mtrl_extended_fab_elevation = 0x7f0601c6
com.example.myapplication:style/ThemeOverlay.MaterialComponents.Toolbar.Popup.Primary = 0x7f11029f
com.example.myapplication:attr/extendedFloatingActionButtonTertiaryStyle = 0x7f030197
com.example.myapplication:attr/curveFit = 0x7f030133
com.example.myapplication:id/material_timepicker_view = 0x7f080116
com.example.myapplication:dimen/material_filled_edittext_font_1_3_padding_bottom = 0x7f06014f
com.example.myapplication:color/m3_ref_palette_dynamic_neutral99 = 0x7f05009a
com.example.myapplication:attr/textAppearanceHeadline6 = 0x7f0303da
com.example.myapplication:attr/buttonGravity = 0x7f030081
com.example.myapplication:drawable/abc_list_selector_disabled_holo_dark = 0x7f070033
com.example.myapplication:dimen/abc_text_size_display_2_material = 0x7f060044
com.example.myapplication:dimen/abc_search_view_preferred_height = 0x7f060036
com.example.myapplication:attr/colorTertiary = 0x7f030103
com.example.myapplication:color/mtrl_tabs_legacy_text_color_selector = 0x7f050229
com.example.myapplication:attr/borderRoundPercent = 0x7f030067
com.example.myapplication:color/abc_tint_spinner = 0x7f050017
com.example.myapplication:attr/isLightTheme = 0x7f030207
com.example.myapplication:attr/viewTransitionOnPositiveCross = 0x7f030457
com.example.myapplication:attr/badgeStyle = 0x7f030052
com.example.myapplication:dimen/m3_appbar_size_compact = 0x7f0600ac
com.example.myapplication:color/mtrl_fab_ripple_color = 0x7f050214
com.example.myapplication:attr/flow_verticalStyle = 0x7f0301c3
com.example.myapplication:attr/boxBackgroundColor = 0x7f03006f
com.example.myapplication:attr/backgroundInsetBottom = 0x7f030047
com.example.myapplication:attr/backgroundInsetEnd = 0x7f030048
com.example.myapplication:style/Widget.MaterialComponents.BottomNavigationView.PrimarySurface = 0x7f110392
com.example.myapplication:dimen/mtrl_btn_text_btn_padding_left = 0x7f060188
com.example.myapplication:color/m3_sys_color_dynamic_light_inverse_on_surface = 0x7f050156
com.example.myapplication:bool/m3_sys_typescale_title_small_text_all_caps = 0x7f040010
com.example.myapplication:attr/cornerFamily = 0x7f030120
com.example.myapplication:drawable/material_ic_keyboard_arrow_left_black_24dp = 0x7f07007c
com.example.myapplication:style/Platform.MaterialComponents.Dialog = 0x7f11012b
com.example.myapplication:attr/extendMotionSpec = 0x7f030192
com.example.myapplication:integer/config_navAnimTime = 0x7f090005
com.example.myapplication:id/spread = 0x7f0801a1
com.example.myapplication:style/Widget.MaterialComponents.TimePicker.Display = 0x7f1103f1
com.example.myapplication:attr/defaultNavHost = 0x7f030145
com.example.myapplication:id/masked = 0x7f080102
com.example.myapplication:color/m3_sys_color_dynamic_dark_primary = 0x7f05014d
com.example.myapplication:anim/btn_checkbox_to_unchecked_check_path_merged_animation = 0x7f010010
com.example.myapplication:attr/fontProviderQuery = 0x7f0301cc
com.example.myapplication:attr/borderWidth = 0x7f030068
com.example.myapplication:attr/flow_firstVerticalBias = 0x7f0301b4
com.example.myapplication:attr/minSeparation = 0x7f0302c6
com.example.myapplication:attr/colorOnPrimary = 0x7f0300eb
com.example.myapplication:style/ThemeOverlay.MaterialComponents.AutoCompleteTextView = 0x7f11027e
com.example.myapplication:style/Base.CardView = 0x7f11000d
com.example.myapplication:attr/materialDividerStyle = 0x7f0302b1
com.example.myapplication:id/exitUntilCollapsed = 0x7f0800c2
com.example.myapplication:style/Base.Widget.Material3.CompoundButton.CheckBox = 0x7f1100f7
com.example.myapplication:attr/helperTextTextAppearance = 0x7f0301df
com.example.myapplication:style/Base.TextAppearance.AppCompat.Widget.Button.Inverse = 0x7f110039
com.example.myapplication:id/accessibility_custom_action_7 = 0x7f08002f
com.example.myapplication:attr/fastScrollVerticalTrackDrawable = 0x7f0301a4
com.example.myapplication:id/accessibility_custom_action_10 = 0x7f080014
com.example.myapplication:attr/actionBarSplitStyle = 0x7f030007
com.example.myapplication:drawable/ic_keyboard_black_24dp = 0x7f070066
com.example.myapplication:attr/badgeWidePadding = 0x7f030054
com.example.myapplication:attr/dayTodayStyle = 0x7f030143
com.example.myapplication:attr/prefixText = 0x7f03032a
com.example.myapplication:styleable/Carousel = 0x7f12001e
com.example.myapplication:color/primary_text_disabled_material_light = 0x7f05023a
com.example.myapplication:style/Theme.MaterialComponents.DayNight.Dialog.Bridge = 0x7f110218
com.example.myapplication:attr/percentHeight = 0x7f030315
com.example.myapplication:attr/behavior_autoShrink = 0x7f03005b
com.example.myapplication:attr/maxHeight = 0x7f0302ba
com.example.myapplication:color/m3_ref_palette_primary95 = 0x7f050101
com.example.myapplication:attr/colorPrimaryVariant = 0x7f0300fb
com.example.myapplication:dimen/abc_action_bar_default_padding_start_material = 0x7f060004
com.example.myapplication:string/mtrl_picker_a11y_prev_month = 0x7f10006a
com.example.myapplication:animator/design_fab_show_motion_spec = 0x7f020002
com.example.myapplication:attr/constraint_referenced_ids = 0x7f03010a
com.example.myapplication:attr/scrimVisibleHeightTrigger = 0x7f030351
com.example.myapplication:style/CardView.Light = 0x7f110113
com.example.myapplication:attr/closeIconEnabled = 0x7f0300cb
com.example.myapplication:color/design_default_color_secondary_variant = 0x7f050049
com.example.myapplication:color/blue_200 = 0x7f050253
com.example.myapplication:id/mtrl_calendar_months = 0x7f080127
com.example.myapplication:style/Base.ThemeOverlay.Material3.Dialog = 0x7f11007c
com.example.myapplication:dimen/mtrl_textinput_outline_box_expanded_padding = 0x7f06021b
com.example.myapplication:style/Theme.Material3.DayNight = 0x7f1101fb
com.example.myapplication:attr/boxStrokeWidth = 0x7f030078
com.example.myapplication:style/Widget.AppCompat.Toolbar = 0x7f1102ea
com.example.myapplication:attr/contentInsetEnd = 0x7f03010f
com.example.myapplication:style/Platform.MaterialComponents.Light.Dialog = 0x7f11012d
com.example.myapplication:drawable/test_custom_background = 0x7f070099
com.example.myapplication:color/m3_ref_palette_neutral_variant80 = 0x7f0500f2
com.example.myapplication:anim/fragment_fast_out_extra_slow_in = 0x7f01001c
com.example.myapplication:style/ThemeOverlay.MaterialComponents.MaterialCalendar.Fullscreen = 0x7f110296
com.example.myapplication:drawable/design_ic_visibility_off = 0x7f070062
com.example.myapplication:attr/textAppearanceListItem = 0x7f0303e3
com.example.myapplication:attr/colorSurfaceInverse = 0x7f030100
com.example.myapplication:color/m3_ref_palette_neutral0 = 0x7f0500dc
com.example.myapplication:color/m3_textfield_stroke_color = 0x7f05018e
com.example.myapplication:attr/backgroundColor = 0x7f030046
com.example.myapplication:attr/helperText = 0x7f0301dd
com.example.myapplication:dimen/fastscroll_minimum_range = 0x7f060095
com.example.myapplication:anim/btn_checkbox_to_checked_icon_null_animation = 0x7f01000e
com.example.myapplication:attr/enableEdgeToEdge = 0x7f030174
com.example.myapplication:color/m3_timepicker_button_background_color = 0x7f05018f
com.example.myapplication:color/bright_foreground_inverse_material_light = 0x7f050025
com.example.myapplication:attr/actionBarTabBarStyle = 0x7f030009
com.example.myapplication:style/Base.TextAppearance.AppCompat.Widget.Button.Colored = 0x7f110038
com.example.myapplication:attr/animationMode = 0x7f030035
com.example.myapplication:attr/constraint_referenced_tags = 0x7f03010b
com.example.myapplication:styleable/Constraint = 0x7f120029
com.example.myapplication:attr/materialAlertDialogButtonSpacerVisibility = 0x7f030292
com.example.myapplication:attr/alwaysExpand = 0x7f030032
com.example.myapplication:attr/layout_constraintLeft_toLeftOf = 0x7f030254
com.example.myapplication:attr/backgroundStacked = 0x7f03004d
com.example.myapplication:attr/alpha = 0x7f03002f
com.example.myapplication:attr/closeIconSize = 0x7f0300cd
com.example.myapplication:id/transition_scene_layoutid_cache = 0x7f0801e5
com.example.myapplication:id/btn_search = 0x7f08006f
com.example.myapplication:attr/alertDialogTheme = 0x7f03002d
com.example.myapplication:attr/expandedTitleMarginBottom = 0x7f03018c
com.example.myapplication:anim/mtrl_card_lowers_interpolator = 0x7f01001f
com.example.myapplication:attr/alertDialogButtonGroupStyle = 0x7f03002a
com.example.myapplication:attr/circularProgressIndicatorStyle = 0x7f0300bd
com.example.myapplication:id/action_mode_close_button = 0x7f080046
com.example.myapplication:style/Base.V28.Theme.AppCompat.Light = 0x7f1100ae
com.example.myapplication:attr/boxCornerRadiusBottomStart = 0x7f030073
com.example.myapplication:anim/btn_radio_to_on_mtrl_ring_outer_path_animation = 0x7f010017
com.example.myapplication:attr/activityName = 0x7f030029
com.example.myapplication:attr/layout_constraintBaseline_creator = 0x7f03023b
com.example.myapplication:style/ThemeOverlay.Material3.TextInputEditText.FilledBox = 0x7f110274
com.example.myapplication:attr/trackThickness = 0x7f03043f
com.example.myapplication:id/mtrl_card_checked_layer_id = 0x7f08012b
com.example.myapplication:attr/floatingActionButtonLargeSecondaryStyle = 0x7f0301a9
com.example.myapplication:attr/windowActionBarOverlay = 0x7f030462
com.example.myapplication:dimen/design_snackbar_padding_vertical_2lines = 0x7f060089
com.example.myapplication:color/m3_textfield_indicator_text_color = 0x7f05018b
com.example.myapplication:attr/actionDropDownStyle = 0x7f03000f
com.example.myapplication:attr/layout_constraintGuide_percent = 0x7f03024a
com.example.myapplication:id/dialog_button = 0x7f0800a7
com.example.myapplication:attr/flow_verticalBias = 0x7f0301c1
com.example.myapplication:attr/textureBlurFactor = 0x7f030407
com.example.myapplication:attr/actionModeSelectAllDrawable = 0x7f03001c
com.example.myapplication:attr/onCross = 0x7f0302fa
com.example.myapplication:style/Theme.AppCompat.Light.NoActionBar = 0x7f1101ec
com.example.myapplication:attr/actionBarSize = 0x7f030006
com.example.myapplication:style/Widget.AppCompat.Light.ActionMode.Inverse = 0x7f1102c9
com.example.myapplication:attr/actionModePasteDrawable = 0x7f03001a
com.example.myapplication:attr/layout_constraintBottom_creator = 0x7f03023f
com.example.myapplication:dimen/mtrl_tooltip_cornerSize = 0x7f06021f
com.example.myapplication:color/secondary_text_disabled_material_dark = 0x7f050243
com.example.myapplication:attr/actionModeCloseDrawable = 0x7f030016
com.example.myapplication:id/sawtooth = 0x7f080177
com.example.myapplication:attr/actionLayout = 0x7f030010
com.example.myapplication:id/text2 = 0x7f0801c7
com.example.myapplication:attr/iconStartPadding = 0x7f0301f3
com.example.myapplication:style/ThemeOverlay.Material3.TextInputEditText.OutlinedBox = 0x7f110276
com.example.myapplication:id/material_minute_text_input = 0x7f08010e
com.example.myapplication:color/m3_ref_palette_dynamic_secondary20 = 0x7f0500b8
com.example.myapplication:attr/startIconContentDescription = 0x7f030387
com.example.myapplication:attr/materialCalendarHeaderDivider = 0x7f03029f
com.example.myapplication:attr/flow_verticalGap = 0x7f0301c2
com.example.myapplication:id/anticipate = 0x7f080055
com.example.myapplication:animator/nav_default_enter_anim = 0x7f020020
com.example.myapplication:attr/actionBarWidgetTheme = 0x7f03000d
com.example.myapplication:layout/abc_list_menu_item_checkbox = 0x7f0b000e
com.example.myapplication:id/action_container = 0x7f08003e
com.example.myapplication:attr/textColorSearchUrl = 0x7f0303f6
com.example.myapplication:layout/mtrl_picker_header_fullscreen = 0x7f0b005a
com.example.myapplication:attr/materialButtonToggleGroupStyle = 0x7f030299
com.example.myapplication:dimen/mtrl_bottomappbar_height = 0x7f060173
com.example.myapplication:attr/allowStacking = 0x7f03002e
com.example.myapplication:dimen/mtrl_btn_pressed_z = 0x7f060184
com.example.myapplication:anim/abc_grow_fade_in_from_bottom = 0x7f010002
com.example.myapplication:attr/trackTint = 0x7f030440
com.example.myapplication:dimen/mtrl_progress_circular_radius = 0x7f0601f6
com.example.myapplication:style/Base.Theme.MaterialComponents.Dialog = 0x7f110063
com.example.myapplication:attr/dropDownListViewStyle = 0x7f030168
com.example.myapplication:styleable/NavGraphNavigator = 0x7f12006f
com.example.myapplication:dimen/mtrl_progress_circular_inset_small = 0x7f0601f5
com.example.myapplication:color/material_dynamic_neutral0 = 0x7f0501a1
com.example.myapplication:animator/mtrl_fab_hide_motion_spec = 0x7f02001c
com.example.myapplication:dimen/m3_sys_typescale_headline_small_text_size = 0x7f06012d
com.example.myapplication:anim/nav_default_pop_exit_anim = 0x7f010023
com.example.myapplication:id/arc = 0x7f080056
com.example.myapplication:attr/spanCount = 0x7f030374
com.example.myapplication:attr/behavior_fitToContents = 0x7f03005e
com.example.myapplication:attr/closeItemLayout = 0x7f0300d1
com.example.myapplication:attr/minTouchTargetSize = 0x7f0302c7
com.example.myapplication:attr/actionBarTabTextStyle = 0x7f03000b
com.example.myapplication:color/m3_sys_color_dynamic_dark_inverse_primary = 0x7f050141
com.example.myapplication:color/material_dynamic_neutral10 = 0x7f0501a2
com.example.myapplication:color/m3_ref_palette_primary100 = 0x7f0500f8
com.example.myapplication:drawable/abc_list_longpressed_holo = 0x7f07002e
com.example.myapplication:bool/m3_sys_typescale_label_medium_text_all_caps = 0x7f04000c
com.example.myapplication:attr/colorOnSurface = 0x7f0300f0
com.example.myapplication:attr/backgroundInsetTop = 0x7f03004a
com.example.myapplication:id/date_picker_actions = 0x7f08009b
com.example.myapplication:attr/materialCardViewFilledStyle = 0x7f0302aa
com.example.myapplication:attr/springStopThreshold = 0x7f030381
com.example.myapplication:attr/navigationMode = 0x7f0302f1
com.example.myapplication:style/Base.V7.Theme.AppCompat.Light = 0x7f1100b1
com.example.myapplication:attr/closeIconStartPadding = 0x7f0300ce
com.example.myapplication:id/container = 0x7f08008f
com.example.myapplication:style/Widget.MaterialComponents.Snackbar.FullWidth = 0x7f1103dc
com.example.myapplication:attr/actionModeWebSearchDrawable = 0x7f030021
com.example.myapplication:dimen/mtrl_progress_track_thickness = 0x7f0601ff
com.example.myapplication:xml/backup_rules = 0x7f130000
com.example.myapplication:style/ThemeOverlay.Material3.MaterialCalendar.HeaderCancelButton = 0x7f11026e
com.example.myapplication:color/m3_ref_palette_secondary40 = 0x7f050108
com.example.myapplication:anim/btn_checkbox_to_checked_box_inner_merged_animation = 0x7f01000c
com.example.myapplication:style/Theme.AppCompat.Empty = 0x7f1101e5
com.example.myapplication:color/m3_sys_color_dynamic_light_surface_variant = 0x7f050168
com.example.myapplication:id/SHOW_PATH = 0x7f08000a
com.example.myapplication:attr/textInputOutlinedDenseStyle = 0x7f0303fd
com.example.myapplication:dimen/item_touch_helper_swipe_escape_max_velocity = 0x7f06009e
com.example.myapplication:drawable/abc_star_half_black_48dp = 0x7f070047
com.example.myapplication:attr/brightness = 0x7f03007a
com.example.myapplication:id/toolbar = 0x7f0801dc
com.example.myapplication:attr/alertDialogStyle = 0x7f03002c
com.example.myapplication:style/TextAppearance.Material3.TitleMedium = 0x7f1101c2
com.example.myapplication:layout/m3_alert_dialog_title = 0x7f0b0034
com.example.myapplication:id/beginning = 0x7f080060
com.example.myapplication:anim/design_snackbar_out = 0x7f01001b
com.example.myapplication:anim/btn_radio_to_off_mtrl_ring_outer_animation = 0x7f010013
com.example.myapplication:attr/splitTrack = 0x7f03037c
com.example.myapplication:style/CardView = 0x7f110111
com.example.myapplication:color/bright_foreground_disabled_material_light = 0x7f050023
com.example.myapplication:color/mtrl_chip_text_color = 0x7f05020d
com.example.myapplication:attr/listDividerAlertDialog = 0x7f030282
com.example.myapplication:id/sharedValueUnset = 0x7f08018d
com.example.myapplication:styleable/FontFamily = 0x7f12003a
com.example.myapplication:layout/mtrl_layout_snackbar = 0x7f0b0053
com.example.myapplication:dimen/abc_text_size_medium_material = 0x7f060049
com.example.myapplication:attr/autoSizePresetSizes = 0x7f030041
com.example.myapplication:attr/alertDialogCenterButtons = 0x7f03002b
com.example.myapplication:styleable/MaterialTextAppearance = 0x7f12005d
com.example.myapplication:style/Theme.MaterialComponents.DayNight.Dialog.MinWidth = 0x7f11021b
com.example.myapplication:color/m3_sys_color_light_inverse_primary = 0x7f05016f
com.example.myapplication:string/abc_prepend_shortcut_label = 0x7f100011
com.example.myapplication:dimen/m3_sys_elevation_level2 = 0x7f060111
com.example.myapplication:integer/material_motion_duration_short_2 = 0x7f09001e
com.example.myapplication:anim/abc_slide_in_bottom = 0x7f010006
com.example.myapplication:anim/btn_radio_to_on_mtrl_ring_outer_animation = 0x7f010016
com.example.myapplication:animator/m3_btn_state_list_anim = 0x7f02000e
com.example.myapplication:id/mtrl_calendar_main_pane = 0x7f080126
com.example.myapplication:style/Theme.MaterialComponents.DialogWhenLarge = 0x7f110228
com.example.myapplication:dimen/abc_text_size_subtitle_material_toolbar = 0x7f06004e
com.example.myapplication:attr/listPreferredItemHeight = 0x7f030287
com.example.myapplication:anim/nav_default_pop_enter_anim = 0x7f010022
com.example.myapplication:style/RtlOverlay.Widget.AppCompat.Search.DropDown.Icon1 = 0x7f110140
com.example.myapplication:color/m3_ref_palette_secondary0 = 0x7f050103
com.example.myapplication:color/highlighted_text_material_light = 0x7f05005e
com.example.myapplication:color/m3_sys_color_dynamic_dark_on_secondary_container = 0x7f050147
com.example.myapplication:anim/abc_tooltip_exit = 0x7f01000b
com.example.myapplication:attr/layout_scrollFlags = 0x7f030276
com.example.myapplication:color/m3_sys_color_dark_on_secondary = 0x7f050130
com.example.myapplication:string/m3_ref_typeface_plain_regular = 0x7f10003c
com.example.myapplication:id/rounded = 0x7f080172
com.example.myapplication:attr/textAppearanceLabelSmall = 0x7f0303e0
com.example.myapplication:style/ThemeOverlay.Material3.Dark = 0x7f11025b
com.example.myapplication:dimen/abc_text_size_large_material = 0x7f060048
com.example.myapplication:attr/paddingBottomSystemWindowInsets = 0x7f030304
com.example.myapplication:attr/layout_optimizationLevel = 0x7f030274
com.example.myapplication:color/m3_ref_palette_dynamic_neutral20 = 0x7f050091

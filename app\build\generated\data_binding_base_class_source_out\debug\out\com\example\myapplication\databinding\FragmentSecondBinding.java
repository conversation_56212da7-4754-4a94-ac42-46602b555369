// Generated by view binder compiler. Do not edit!
package com.example.myapplication.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageButton;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.myapplication.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class FragmentSecondBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final ImageButton btnCalendarToday;

  @NonNull
  public final ImageButton btnNextMonth;

  @NonNull
  public final ImageButton btnPrevMonth;

  @NonNull
  public final ImageView logoCalendar;

  @NonNull
  public final LinearLayout navCalendarActive;

  @NonNull
  public final LinearLayout navProfileCalendar;

  @NonNull
  public final LinearLayout navTasksCalendar;

  @NonNull
  public final RecyclerView recyclerCalendarTasks;

  @NonNull
  public final TextView tvMonthYear;

  @NonNull
  public final TextView tvToday;

  private FragmentSecondBinding(@NonNull LinearLayout rootView,
      @NonNull ImageButton btnCalendarToday, @NonNull ImageButton btnNextMonth,
      @NonNull ImageButton btnPrevMonth, @NonNull ImageView logoCalendar,
      @NonNull LinearLayout navCalendarActive, @NonNull LinearLayout navProfileCalendar,
      @NonNull LinearLayout navTasksCalendar, @NonNull RecyclerView recyclerCalendarTasks,
      @NonNull TextView tvMonthYear, @NonNull TextView tvToday) {
    this.rootView = rootView;
    this.btnCalendarToday = btnCalendarToday;
    this.btnNextMonth = btnNextMonth;
    this.btnPrevMonth = btnPrevMonth;
    this.logoCalendar = logoCalendar;
    this.navCalendarActive = navCalendarActive;
    this.navProfileCalendar = navProfileCalendar;
    this.navTasksCalendar = navTasksCalendar;
    this.recyclerCalendarTasks = recyclerCalendarTasks;
    this.tvMonthYear = tvMonthYear;
    this.tvToday = tvToday;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentSecondBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentSecondBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_second, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentSecondBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btn_calendar_today;
      ImageButton btnCalendarToday = ViewBindings.findChildViewById(rootView, id);
      if (btnCalendarToday == null) {
        break missingId;
      }

      id = R.id.btn_next_month;
      ImageButton btnNextMonth = ViewBindings.findChildViewById(rootView, id);
      if (btnNextMonth == null) {
        break missingId;
      }

      id = R.id.btn_prev_month;
      ImageButton btnPrevMonth = ViewBindings.findChildViewById(rootView, id);
      if (btnPrevMonth == null) {
        break missingId;
      }

      id = R.id.logo_calendar;
      ImageView logoCalendar = ViewBindings.findChildViewById(rootView, id);
      if (logoCalendar == null) {
        break missingId;
      }

      id = R.id.nav_calendar_active;
      LinearLayout navCalendarActive = ViewBindings.findChildViewById(rootView, id);
      if (navCalendarActive == null) {
        break missingId;
      }

      id = R.id.nav_profile_calendar;
      LinearLayout navProfileCalendar = ViewBindings.findChildViewById(rootView, id);
      if (navProfileCalendar == null) {
        break missingId;
      }

      id = R.id.nav_tasks_calendar;
      LinearLayout navTasksCalendar = ViewBindings.findChildViewById(rootView, id);
      if (navTasksCalendar == null) {
        break missingId;
      }

      id = R.id.recycler_calendar_tasks;
      RecyclerView recyclerCalendarTasks = ViewBindings.findChildViewById(rootView, id);
      if (recyclerCalendarTasks == null) {
        break missingId;
      }

      id = R.id.tv_month_year;
      TextView tvMonthYear = ViewBindings.findChildViewById(rootView, id);
      if (tvMonthYear == null) {
        break missingId;
      }

      id = R.id.tv_today;
      TextView tvToday = ViewBindings.findChildViewById(rootView, id);
      if (tvToday == null) {
        break missingId;
      }

      return new FragmentSecondBinding((LinearLayout) rootView, btnCalendarToday, btnNextMonth,
          btnPrevMonth, logoCalendar, navCalendarActive, navProfileCalendar, navTasksCalendar,
          recyclerCalendarTasks, tvMonthYear, tvToday);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}

package com.example.myapplication;

public class Task {
    private String id;
    private String title;
    private String category;
    private String description;
    private String deadline;
    private String status;
    private int statusColor;

    public Task(String title, String category, String description, String deadline, String status, int statusColor) {
        this.title = title;
        this.category = category;
        this.description = description;
        this.deadline = deadline;
        this.status = status;
        this.statusColor = statusColor;
    }

    // Getters
    public String getId() {
        return id;
    }

    public String getTitle() {
        return title;
    }

    public String getCategory() {
        return category;
    }

    public String getDescription() {
        return description;
    }

    public String getDeadline() {
        return deadline;
    }

    public String getStatus() {
        return status;
    }

    public int getStatusColor() {
        return statusColor;
    }

    // Setters
    public void setId(String id) {
        this.id = id;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public void setDeadline(String deadline) {
        this.deadline = deadline;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public void setStatusColor(int statusColor) {
        this.statusColor = statusColor;
    }
}

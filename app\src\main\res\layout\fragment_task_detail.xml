<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#F5F5F5">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <!-- Header -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:padding="16dp"
            android:background="#2196F3"
            android:gravity="center_vertical">

            <ImageButton
                android:id="@+id/btn_back"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:src="@android:drawable/ic_menu_revert"
                android:background="?android:attr/selectableItemBackgroundBorderless" />
<!--                android:tint="@android:color/white" />-->

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="Chi tiết Task"
                android:textColor="@android:color/white"
                android:textSize="20sp"
                android:textStyle="bold"
                android:layout_marginStart="12dp" />

            <ImageButton
                android:id="@+id/btn_edit"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:src="@android:drawable/ic_menu_edit"
                android:background="?android:attr/selectableItemBackgroundBorderless" />
<!--                android:tint="@android:color/white" />-->

        </LinearLayout>

        <!-- Task Content -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="16dp"
            android:background="@android:color/white"
            android:elevation="4dp"
            android:orientation="vertical"
            android:padding="20dp">



                <!-- Tiêu đề -->
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Tiêu đề"
                    android:textSize="14sp"
                    android:textColor="#757575"
                    android:layout_marginBottom="4dp" />

                <TextView
                    android:id="@+id/tv_detail_title"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Tên task chi tiết"
                    android:textSize="24sp"
                    android:textStyle="bold"
                    android:textColor="#212121"
                    android:layout_marginBottom="16dp" />

                <!-- Chủ đề -->
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Chủ đề"
                    android:textSize="14sp"
                    android:textColor="#757575"
                    android:layout_marginBottom="4dp" />

                <TextView
                    android:id="@+id/tv_detail_category"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Cá nhân"
                    android:textSize="16sp"
                    android:textColor="@android:color/white"
                    android:background="#2196F3"
                    android:paddingHorizontal="12dp"
                    android:paddingVertical="6dp"
                    android:layout_marginBottom="16dp" />

                <!-- Mô tả -->
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Mô tả"
                    android:textSize="14sp"
                    android:textColor="#757575"
                    android:layout_marginBottom="4dp" />

                <TextView
                    android:id="@+id/tv_detail_description"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Mô tả chi tiết của task. Đây là nội dung mô tả dài có thể có nhiều dòng để giải thích rõ ràng về task này."
                    android:textSize="16sp"
                    android:textColor="#424242"
                    android:lineSpacingExtra="4dp"
                    android:layout_marginBottom="16dp" />

                <!-- Deadline -->
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Hạn chót"
                    android:textSize="14sp"
                    android:textColor="#757575"
                    android:layout_marginBottom="4dp" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical"
                    android:layout_marginBottom="16dp">

                    <ImageView
                        android:layout_width="20dp"
                        android:layout_height="20dp"
                        android:src="@android:drawable/ic_menu_recent_history" />
<!--                        android:tint="#FF5722" />-->

                    <TextView
                        android:id="@+id/tv_detail_deadline"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="25/12/2024 - 14:30"
                        android:textSize="16sp"
                        android:textColor="#FF5722"
                        android:layout_marginStart="8dp" />

                </LinearLayout>

                <!-- Trạng thái -->
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Trạng thái"
                    android:textSize="14sp"
                    android:textColor="#757575"
                    android:layout_marginBottom="4dp" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical">

                    <View
                        android:id="@+id/view_detail_status"
                        android:layout_width="16dp"
                        android:layout_height="16dp"
                        android:background="#4CAF50" />

                    <TextView
                        android:id="@+id/tv_detail_status"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Đang thực hiện"
                        android:textSize="16sp"
                        android:textColor="#4CAF50"
                        android:layout_marginStart="8dp" />

                </LinearLayout>

        </LinearLayout>

        <!-- Action Buttons -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:padding="16dp"
            android:gravity="center">

            <Button
                android:id="@+id/btn_mark_complete"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="Hoàn thành"
                android:backgroundTint="#4CAF50"
                android:layout_marginEnd="8dp" />

            <Button
                android:id="@+id/btn_delete_task"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="Xóa"
                android:background="@android:drawable/btn_default"
                android:textColor="#F44336"
                android:layout_marginStart="8dp" />

        </LinearLayout>

    </LinearLayout>

</ScrollView>

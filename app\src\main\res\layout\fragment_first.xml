<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res/auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="#F5F5F5"
    tools:context=".FirstFragment">

    <!-- Header với Logo và Navbar -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="16dp"
        android:background="#2196F3"
        android:gravity="center_vertical">

        <ImageView
            android:id="@+id/logo"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:src="@android:drawable/ic_menu_agenda"
            android:tint="@android:color/white" />

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="Task Manager"
            android:textColor="@android:color/white"
            android:textSize="20sp"
            android:textStyle="bold"
            android:layout_marginStart="12dp" />

        <ImageButton
            android:id="@+id/btn_search"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:src="@android:drawable/ic_menu_search"
            android:background="?android:attr/selectableItemBackgroundBorderless"
            android:tint="@android:color/white" />

    </LinearLayout>

    <!-- Phần chọn chủ đề -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp"
        android:background="@android:color/white"
        android:layout_marginBottom="8dp">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Chủ đề:"
            android:textSize="16sp"
            android:textStyle="bold"
            android:layout_marginBottom="8dp" />

        <HorizontalScrollView
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <Button
                    android:id="@+id/btn_all_tasks"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Tất cả"
                    android:layout_marginEnd="8dp"
                    android:backgroundTint="#2196F3"
                    android:textColor="@android:color/white" />

                <Button
                    android:id="@+id/btn_personal_tasks"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Cá nhân"
                    android:layout_marginEnd="8dp"
                    android:background="@android:drawable/btn_default"
                    android:textColor="#2196F3" />

                <Button
                    android:id="@+id/btn_work_tasks"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Công việc"
                    android:layout_marginEnd="8dp"
                    android:background="@android:drawable/btn_default"
                    android:textColor="#2196F3" />

                <Button
                    android:id="@+id/btn_add_category"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="+ Thêm chủ đề"
                    android:background="@android:drawable/btn_default"
                    android:textColor="#4CAF50" />

            </LinearLayout>
        </HorizontalScrollView>
    </LinearLayout>

    <!-- Danh sách Task -->
    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recycler_tasks"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:padding="16dp"
            android:clipToPadding="false"
            tools:listitem="@layout/item_task" />

        <!-- Nút thêm task mới -->
        <com.google.android.material.floatingactionbutton.FloatingActionButton
            android:id="@+id/fab_add_task"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:layout_alignParentBottom="true"
            android:layout_margin="16dp"
            android:src="@android:drawable/ic_input_add"
            app:backgroundTint="#4CAF50" />

    </RelativeLayout>

    <!-- Footer Navigation -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:background="@android:color/white"
        android:elevation="8dp">

        <LinearLayout
            android:id="@+id/nav_tasks"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical"
            android:padding="12dp"
            android:gravity="center"
            android:background="?android:attr/selectableItemBackground">

            <ImageView
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:src="@android:drawable/ic_menu_agenda"
                android:tint="#2196F3" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Nhiệm Vụ"
                android:textSize="12sp"
                android:textColor="#2196F3"
                android:layout_marginTop="4dp" />

        </LinearLayout>

        <LinearLayout
            android:id="@+id/nav_calendar"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical"
            android:padding="12dp"
            android:gravity="center"
            android:background="?android:attr/selectableItemBackground">

            <ImageView
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:src="@android:drawable/ic_menu_my_calendar"
                android:tint="#757575" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Lịch"
                android:textSize="12sp"
                android:textColor="#757575"
                android:layout_marginTop="4dp" />

        </LinearLayout>

        <LinearLayout
            android:id="@+id/nav_profile"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical"
            android:padding="12dp"
            android:gravity="center"
            android:background="?android:attr/selectableItemBackground">

            <ImageView
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:src="@android:drawable/ic_menu_myplaces"
                android:tint="#757575" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Của tôi"
                android:textSize="12sp"
                android:textColor="#757575"
                android:layout_marginTop="4dp" />

        </LinearLayout>

    </LinearLayout>

</LinearLayout>
// Generated by view binder compiler. Do not edit!
package com.example.myapplication.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.ImageButton;
import android.widget.ImageView;
import android.widget.LinearLayout;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.myapplication.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class FragmentFirstBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final Button btnAddCategory;

  @NonNull
  public final Button btnAllTasks;

  @NonNull
  public final Button btnPersonalTasks;

  @NonNull
  public final ImageButton btnSearch;

  @NonNull
  public final Button btnWorkTasks;

  @NonNull
  public final Button fabAddTask;

  @NonNull
  public final ImageView logo;

  @NonNull
  public final LinearLayout navCalendar;

  @NonNull
  public final LinearLayout navProfile;

  @NonNull
  public final LinearLayout navTasks;

  @NonNull
  public final RecyclerView recyclerTasks;

  private FragmentFirstBinding(@NonNull LinearLayout rootView, @NonNull Button btnAddCategory,
      @NonNull Button btnAllTasks, @NonNull Button btnPersonalTasks, @NonNull ImageButton btnSearch,
      @NonNull Button btnWorkTasks, @NonNull Button fabAddTask, @NonNull ImageView logo,
      @NonNull LinearLayout navCalendar, @NonNull LinearLayout navProfile,
      @NonNull LinearLayout navTasks, @NonNull RecyclerView recyclerTasks) {
    this.rootView = rootView;
    this.btnAddCategory = btnAddCategory;
    this.btnAllTasks = btnAllTasks;
    this.btnPersonalTasks = btnPersonalTasks;
    this.btnSearch = btnSearch;
    this.btnWorkTasks = btnWorkTasks;
    this.fabAddTask = fabAddTask;
    this.logo = logo;
    this.navCalendar = navCalendar;
    this.navProfile = navProfile;
    this.navTasks = navTasks;
    this.recyclerTasks = recyclerTasks;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentFirstBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentFirstBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_first, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentFirstBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btn_add_category;
      Button btnAddCategory = ViewBindings.findChildViewById(rootView, id);
      if (btnAddCategory == null) {
        break missingId;
      }

      id = R.id.btn_all_tasks;
      Button btnAllTasks = ViewBindings.findChildViewById(rootView, id);
      if (btnAllTasks == null) {
        break missingId;
      }

      id = R.id.btn_personal_tasks;
      Button btnPersonalTasks = ViewBindings.findChildViewById(rootView, id);
      if (btnPersonalTasks == null) {
        break missingId;
      }

      id = R.id.btn_search;
      ImageButton btnSearch = ViewBindings.findChildViewById(rootView, id);
      if (btnSearch == null) {
        break missingId;
      }

      id = R.id.btn_work_tasks;
      Button btnWorkTasks = ViewBindings.findChildViewById(rootView, id);
      if (btnWorkTasks == null) {
        break missingId;
      }

      id = R.id.fab_add_task;
      Button fabAddTask = ViewBindings.findChildViewById(rootView, id);
      if (fabAddTask == null) {
        break missingId;
      }

      id = R.id.logo;
      ImageView logo = ViewBindings.findChildViewById(rootView, id);
      if (logo == null) {
        break missingId;
      }

      id = R.id.nav_calendar;
      LinearLayout navCalendar = ViewBindings.findChildViewById(rootView, id);
      if (navCalendar == null) {
        break missingId;
      }

      id = R.id.nav_profile;
      LinearLayout navProfile = ViewBindings.findChildViewById(rootView, id);
      if (navProfile == null) {
        break missingId;
      }

      id = R.id.nav_tasks;
      LinearLayout navTasks = ViewBindings.findChildViewById(rootView, id);
      if (navTasks == null) {
        break missingId;
      }

      id = R.id.recycler_tasks;
      RecyclerView recyclerTasks = ViewBindings.findChildViewById(rootView, id);
      if (recyclerTasks == null) {
        break missingId;
      }

      return new FragmentFirstBinding((LinearLayout) rootView, btnAddCategory, btnAllTasks,
          btnPersonalTasks, btnSearch, btnWorkTasks, fabAddTask, logo, navCalendar, navProfile,
          navTasks, recyclerTasks);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}

<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    android:padding="12dp"
    android:background="?android:attr/selectableItemBackground"
    android:gravity="center_vertical">

    <!-- Time -->
    <TextView
        android:id="@+id/tv_calendar_task_time"
        android:layout_width="60dp"
        android:layout_height="wrap_content"
        android:text="09:00"
        android:textSize="14sp"
        android:textColor="#2196F3"
        android:textStyle="bold"
        android:gravity="center" />

    <!-- Task Info -->
    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:orientation="vertical"
        android:layout_marginStart="16dp">

        <TextView
            android:id="@+id/tv_calendar_task_title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Họp team dự án"
            android:textSize="16sp"
            android:textStyle="bold"
            android:textColor="#212121"
            android:layout_marginBottom="4dp" />

        <TextView
            android:id="@+id/tv_calendar_task_category"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Công việc"
            android:textSize="12sp"
            android:textColor="@android:color/white"
            android:background="#FF9800"
            android:paddingHorizontal="8dp"
            android:paddingVertical="2dp" />

    </LinearLayout>

    <!-- Status Indicator -->
    <View
        android:id="@+id/view_calendar_task_status"
        android:layout_width="12dp"
        android:layout_height="12dp"
        android:background="#4CAF50"
        android:layout_marginStart="8dp" />

</LinearLayout>
